<?php

namespace App\Http\Controllers;

use App\Models\BeatMaster;
use App\Models\CallMaster;
use App\Models\CustomerMaster;
use App\Models\EmployeeMaster;
use App\Models\EmpNotification;
use App\Models\Options;
use App\Models\OrderMaster;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use LaravelDaily\Invoices\Invoice;
use LaravelDaily\Invoices\Classes\Buyer;
use LaravelDaily\Invoices\Classes\Party;
use LaravelDaily\Invoices\Classes\InvoiceItem;
use App\CustomItem;
use App\Models\CustomerStockMaster;
use App\Models\ProductMaster;
use App\Models\waOrderMessage;
use PdfReport;
use App\Traits\SendFcmMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\Mailer\Transport;

class OrdersController extends Controller
{
    private $today;
    // private $start_date;
    // private $end_date;

    public function __construct()
    {
        $this->today = Carbon::today();
        // $this->start_date = $this->today->startOfDay()->toDateTimeString();
        // $this->end_date = $this->today->endOfDay()->toDateTimeString();
    }

    use SendFcmMessage;

    private function getOrderViewData()
    {
        $empList = EmployeeMaster::select('emp_code', 'emp_name')->orderBy('emp_name', 'asc')->get();
        $ssList = CustomerMaster::select('cm_code', 'cm_name', 'cm_type')->where('cm_type', 2)->orderBy('cm_name', 'asc')->get();
        $distributorList = CustomerMaster::select('cm_code', 'cm_name', 'cm_type')->where('cm_type', 3)->orderBy('cm_name', 'asc')->get();
        $delDistList = CustomerMaster::select('cm_code', 'cm_name', 'cm_type')->whereIn('cm_type', [3, 4])->get();
        $dealerList = CustomerMaster::select('cm_code', 'cm_name', 'cm_type')->where('cm_type', 4)->orderBy('cm_name', 'asc')->get();
        $retailerList = CustomerMaster::select('cm_code', 'cm_name', 'cm_type', 'cm_area')->where('cm_type', 5)->orderBy('cm_name', 'asc')->get();

        return compact('empList', 'ssList', 'distributorList', 'delDistList', 'dealerList', 'retailerList');
    }

    public function ss_orders()
    {
        $options = Options::where('key', 'customer_types')->value('value');
        $customerTypes = json_decode($options, true);

        if (!isset($customerTypes['ss']) || $customerTypes['ss']['status'] != 1) {
            return view('errors.permission_denied');
        }

        $data = $this->getOrderViewData();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('orders.ss_orders', $data)
            . view('modal.ss_order_detail_edit_modal')
            . view('layouts.footer');
    }

    public function distributor_orders()
    {
        $options = Options::where('key', 'customer_types')->value('value');
        $customerTypes = json_decode($options, true);

        if (!isset($customerTypes['distributor']) || $customerTypes['distributor']['status'] != 1) {
            return view('errors.permission_denied');
        }

        $data = $this->getOrderViewData();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('orders.distributor_orders', $data)
            . view('modal.distributor_order_detail_edit_modal')
            . view('layouts.footer');
    }

    public function dealer_orders()
    {
        $options = Options::where('key', 'customer_types')->value('value');
        $customerTypes = json_decode($options, true);

        if (!isset($customerTypes['dealer']) || $customerTypes['dealer']['status'] != 1) {
            return view('errors.permission_denied');
        }

        $data = $this->getOrderViewData();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('orders.dealer_orders', $data)
            . view('modal.dealer_order_detail_edit_modal')
            . view('layouts.footer');
    }

    public function retailer_orders()
    {
        $options = Options::where('key', 'customer_types')->value('value');
        $customerTypes = json_decode($options, true);

        if (!isset($customerTypes['retailer']) || $customerTypes['retailer']['status'] != 1) {
            return view('errors.permission_denied');
        }

        $data = $this->getOrderViewData();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('orders.retailer_orders', $data)
            . view('modal.retailer_order_detail_edit_modal')
            . view('layouts.footer');
    }

    public function dispatch_order_list()
    {
        $emp_list = EmployeeMaster::all();
        $beat_list = BeatMaster::all();
        $customerList = CustomerMaster::all();

        $customerOrders = CallMaster::select(
            'call_master.call_code',
            'call_master.emp_code',
            'call_master.invoice_number',
            'call_master.product_order_type',
            'call_master.client_code',
            'call_master.grand_total',
            'call_master.party_code',
            'call_master.call_status',
            'call_master.created_at as date',
            'call_master.start_time',
            'call_master.stop_time',
            'employee_master.emp_name as empName',
            'buyer.cm_name as buyerName',
            'seller.cm_name as sellerName'
        )
            ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
            ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
            ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
            ->get();

        // $customerOrders = CallMaster::with('employee', 'customer_cli', 'customer_party')
        //     ->select('call_code', 'emp_code', 'invoice_number', 'client_code', 'grand_total', 'party_code', 'call_status', 'created_at', 'start_time', 'stop_time')
        //     ->get();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('orders.dispatch_order_list', compact('emp_list', 'beat_list', 'customerList', 'customerOrders'))
            . view('layouts.footer');
    }

    public function updateOrderDetail(Request $request, $id)
    {
        $order = OrderMaster::find($id);
        if (!$order) {
            return response()->json(['status' => 'error', 'message' => 'Order not found']);
        }

        $field = $request->get('field');
        $value = $request->get('value');
        $allowedFields = ['quantity', 'rate_basic', 'total_basic_rate', 'grand_total', 'gst'];
        if (in_array($field, $allowedFields)) {
            $order->$field = $value;
            if ($field == 'quantity' || $field == 'rate_basic') {
                // Calculate the total_basic_rate, gst, and grand_total
                $quantity = $order->quantity;
                $rate_basic = $order->rate_basic;
                $gst_rate = $order->gst;
                $pcs = $order->pcs;

                $price_with_tax = $rate_basic + ($rate_basic * $gst_rate);
                $total_basic_rate = $quantity * $rate_basic;
                //gst is total basic price * gst %
                $gst = ($total_basic_rate * $gst_rate) / 100;
                $grand_total = $total_basic_rate + $gst;
                // Update the total_basic_rate, gst, and grand_total fields
                $order->total_basic_rate = $total_basic_rate;
                $order->gst_amount = $gst;
                $order->grand_total = $grand_total;
            }
            $order->save();

            // Retrieve the related CallMaster record
            $call = CallMaster::where('call_code', $order->call_code)->first();
            if ($call) {
                // Update the call's totals (here, assuming total_quantity is the sum of order quantities)
                $call->total_quantity = $call->orders->sum('quantity');  // Sum of all order quantities
                $call->grand_total = $call->orders->sum('grand_total'); // Sum of all order grand totals
                $call->save();
            }

            return response()->json(['status' => 'success', 'message' => 'Order updated successfully']);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Invalid field']);
        }
    }

    public function editOrderFun($call_code, $orderType)
    {
        try {
            $orders = CallMaster::with('employee', 'customer_cli:cm_code,cm_name,cm_contact_person,cm_address,cm_gst,cm_mobile', 'customer_party:cm_code,cm_name,cm_contact_person,cm_address,cm_gst,cm_mobile', 'orders.product.uom')
                ->select('call_code', 'emp_code', 'client_code', 'order_sign', 'accuracy', 'total_quantity', 'party_code', 'reason_id', 'grand_total', 'product_order_type', 'invoice_number', 'call_status', 'remarks', 'packaging_charge', 'transportation_charge', 'transportation_name', 'start_time', 'stop_time', 'cancel_description', 'is_telephonic', 'created_at')
                ->find($call_code)->makeVisible('created_at');

            if (!$orders) {
                throw new Exception(ucfirst($orderType) . ' order not found');
            }

            return response()->json([
                'status' => 'success',
                'data' => $orders,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 404);
        }
    }

    public function edit_ss_order($call_code)
    {
        return $this->editOrderFun($call_code, 'ss');
    }

    public function edit_distributor_order($call_code)
    {
        return $this->editOrderFun($call_code, 'distributor');
    }

    public function edit_dealer_order($call_code)
    {
        return $this->editOrderFun($call_code, 'dealer');
    }

    public function edit_retailer_order($call_code)
    {
        return $this->editOrderFun($call_code, 'retailer');
    }

    private function ordersTableRefresh(Request $request, $cmType)
    {
        try {
            $todayDate = date('Y-m-d');
            // dd($todayDate);
            $orderTable = CallMaster::select(
                'call_master.call_code',
                'call_master.emp_code',
                'call_master.invoice_number',
                'call_master.product_order_type',
                'call_master.client_code',
                'call_master.grand_total',
                'call_master.party_code',
                'call_master.call_status',
                'call_master.created_at as date',
                'call_master.start_time',
                'call_master.stop_time',
                'employee_master.emp_name as empName',
                'buyer.cm_name as buyerName',
                'seller.cm_name as sellerName'
            )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->where('buyer.cm_type', $cmType)
                ->whereDate('call_master.created_at', $todayDate)
                ->orderBy('call_master.created_at', 'desc')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => $orderTable,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function ssOrderUpdateTableRefresh(Request $request)
    {
        return $this->ordersTableRefresh($request,  2); // 2- ss
    }

    public function distOrderUpdateTableRefresh(Request $request)
    {
        return $this->ordersTableRefresh($request,  3); // 3- distributor
    }

    public function dealerOrderUpdateTableRefresh(Request $request)
    {
        return $this->ordersTableRefresh($request,  4); // 4- dealer
    }

    public function retailerOrderUpdateTableRefresh(Request $request)
    {
        return $this->ordersTableRefresh($request, 5); // 5- retailer
    }

    public function getDispatchOrderList(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'date' => 'required',
            ], [
                'date.required' => 'Date is required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Dispatch Order Validation failed', 'errors' => $validator->errors()], 422);
            }

            $dates = explode(',', $request->date);
            $start_date = isset($dates[0]) ? $dates[0] : null;
            $end_date = isset($dates[1]) ? $dates[1] : null;
            $employee = $request->employee;
            $type = $request->type;
            $ss_id = $request->ss_id;
            $dist_id = $request->dist_id;
            $dealer_id = $request->dealer_id;
            $retailer_id = $request->retailer_id;

            $query = CallMaster::select(
                'call_master.call_code',
                'call_master.emp_code',
                'call_master.invoice_number',
                'call_master.product_order_type',
                'call_master.client_code',
                'call_master.grand_total',
                'call_master.party_code',
                'call_master.call_status',
                'call_master.created_at as date',
                'call_master.start_time',
                'call_master.stop_time',
                'employee_master.emp_name as empName',
                'buyer.cm_name as buyerName',
                'seller.cm_name as sellerName'
            )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code');


            // $query = CallMaster::with('employee', 'customer_cli')->select('call_code', 'emp_code', 'client_code', 'grand_total', 'call_status', 'created_at');

            if ($start_date && $end_date) {
                // Ensure start and end dates are formatted as Y-m-d
                $start_date = date('Y-m-d', strtotime($start_date));
                $end_date = date('Y-m-d', strtotime($end_date));

                // Use whereBetween for date range
                $query->whereBetween("call_master.created_at", [$start_date . ' 00:00:00', $end_date . ' 23:59:59']);
            } elseif ($start_date) {
                // Ensure start date is formatted as Y-m-d
                $start_date = date('Y-m-d', strtotime($start_date));

                // Use whereDate for specific date
                $query->whereDate('call_master.created_at', $start_date);
            }

            if ($employee) {
                $query = $query->where('emp_code', $employee);
            }

            if ($type === "1") {
                $query = $query->whereHas('customer_cli', function ($query) {
                    $query->where('cm_type', 2); // 2- ss
                });
            } elseif ($type === "2") {
                $query = $query->whereHas('customer_cli', function ($query) {
                    $query->where('cm_type', 3); // 3- dist
                });
            } elseif ($type === "3") {
                $query = $query->whereHas('customer_cli', function ($query) {
                    $query->where('cm_type', 4); // 4- dist
                });
            } elseif ($type === "4") {
                $query = $query->whereHas('customer_cli', function ($query) {
                    $query->where('cm_type', 5); // 5- retailer
                });
            } elseif ($type === "all") {
                $query = $query->whereHas('customer_cli', function ($query) {
                    $query->whereIn('cm_type', [2, 3, 4, 5]);
                });
            } else {
                return null;
            }

            if ($type === "1" && $ss_id) {
                // Check if type is 1 and SS ID is provided, then filter based on SS ID
                $query->whereHas('customer_cli', function ($query) use ($ss_id) {
                    $query->where('cm_type', 2)->where('cm_code', $ss_id); // 2- ss
                });
            } elseif ($type === "2" && $dist_id) {
                $query->whereHas('customer_cli', function ($query) use ($dist_id) {
                    $query->where('cm_type', 3)->where('cm_code', $dist_id); // 3- dist
                });
            } elseif ($type === "3" && $dealer_id) {
                $query->whereHas('customer_cli', function ($query) use ($dealer_id) {
                    $query->where('cm_type', 4)->where('cm_code', $dealer_id); // 4- dealer
                });
            } elseif ($type === "4" && $retailer_id) {
                $query->whereHas('customer_cli', function ($query) use ($retailer_id) {
                    $query->where('cm_type', 5)->where('cm_code', $retailer_id); // 5- retailer
                });
            } elseif ($type === "all") {
                $query->whereHas('customer_cli', function ($query) use ($retailer_id) {
                    $query->whereIn('cm_type', [2, 3, 4, 5]);
                });
            } else {
                return null;
            }

            $dispatchOrder = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $dispatchOrder,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // this below function to update into index file using multiple select
    public function order_confirm_status_update(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required|array',
                'call_status' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
            }

            $call_code = $request->call_code;
            $newStatus = $request->call_status;

            $arrcall_code = array_column($call_code, 'call_code');
            $orders = CallMaster::whereIn('call_code', $arrcall_code)->get();

            foreach ($orders as $order) {
                if ($order->call_status == 4) {
                    return response()->json(['status' => 'error', 'message' => 'Cancelled Order cannot be confirmed']);
                }
                if ($order->call_status == 1) {
                    return response()->json(['status' => 'success', 'message' => 'Order status already confirmed']);
                }
            }

            // Store old statuses before updating
            $oldStatuses = $orders->pluck('call_status', 'call_code')->toArray();

            // Update call_status
            CallMaster::whereIn('call_code', $arrcall_code)->update(['call_status' => $request->call_status]);

            if ($request->call_status) {
                $this->sendOrderNotification($orders, 'Confirmed');
            }

            $waBuyer = waOrderMessage::where('category', 'buyer')->where('type', 'productive')->first();
            $waSeller = waOrderMessage::where('category', 'seller')->where('type', 'productive')->first();
            $waBuyerNonProd = waOrderMessage::where('category', 'buyer')->where('type', 'non_productive')->first();

            $adminAuth = Auth::user()->name;
            foreach ($orders as $order) {
                $updateLog = new CallMaster();
                $updateLog->logStatusChange($order->call_code, $order->invoice_number, $oldStatuses[$order->call_code], $newStatus, $adminAuth);

                // send message to productive order
                if ($order->product_order_type == 1) {
                    if ($waBuyer && $waBuyer->status == 1) {
                        $evtData = explode(',', $waBuyer->event);
                        if (in_array('confirm', $evtData)) {
                            waOrderMessages($order->call_code, $order->client_code, $order->party_code, $order->invoice_number, $order->grand_total, $order->total_quantity, $waBuyer->template_id, $waBuyer->language, 'buyer', 1);
                        }
                    }

                    if ($waSeller && $waSeller->status == 1) {
                        $evtData = explode(',', $waSeller->event);
                        if (in_array('confirm', $evtData)) {
                            waOrderMessages($order->call_code, $order->client_code, $order->party_code, $order->invoice_number, $order->grand_total, $order->total_quantity, $waSeller->template_id, $waSeller->language, 'seller', 1);
                        }
                    }
                } else {
                    // non productive order messages if have
                    if ($waBuyerNonProd && $waBuyerNonProd->status == 1) {
                        $evtData = explode(',', $waBuyerNonProd->event);
                        if (in_array('confirm', $evtData)) {
                            waOrderMessages($order->call_code, $order->client_code, $order->party_code, $order->invoice_number, $order->grand_total, $order->total_quantity, $waBuyerNonProd->template_id, $waBuyerNonProd->language, 'buyer', 0);
                        }
                    }
                }
            }

            return response()->json(['status' => 'success', 'message' => 'Order Status Updated Successfully']);
        } catch (Exception $e) {
            Log::error('Error updating ss order status: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the ss order status: ' . $e->getMessage()], 500);
        }
    }

    public function order_cancel_status_update(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required|array',
                'call_status' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
            }

            $call_code = $request->call_code;
            $newStatus = $request->call_status;

            $arrcall_code = array_column($call_code, 'call_code');
            $orders = CallMaster::whereIn('call_code', $arrcall_code)->get();

            foreach ($orders as $order) {
                if ($order->call_status == 4) {
                    return response()->json(['status' => 'error', 'message' => 'Already order status is cancel']);
                }
            }

            // Store old statuses before updating
            $oldStatuses = $orders->pluck('call_status', 'call_code')->toArray();

            // Update call_status
            CallMaster::whereIn('call_code', $arrcall_code)->update(['call_status' => $request->call_status]);

            if ($request->call_status) {
                $this->sendOrderNotification($orders, 'Calcelled');
            }

            $adminAuth = Auth::user()->name;
            foreach ($orders as $order) {
                $updateLog = new CallMaster();
                $updateLog->logStatusChange($order->call_code, $order->invoice_number, $oldStatuses[$order->call_code], $newStatus, $adminAuth);
            }

            return response()->json(['status' => 'success', 'message' => 'Order Status Updated Successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order status: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order status: ' . $e->getMessage()], 500);
        }
    }

    public function order_delievered_status_update(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required|array',
                'call_status' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
            }

            $call_code = $request->call_code;
            $newStatus = $request->call_status;

            $arrcall_code = array_column($call_code, 'call_code');
            $orders = CallMaster::whereIn('call_code', $arrcall_code)->get();

            foreach ($orders as $order) {
                if ($order->call_status == 4) {
                    return response()->json(['status' => 'error', 'message' => 'Cancelled order(s) cannot be marked as delivered']);
                }

                if ($order->call_status == 3) {
                    return response()->json(['status' => 'success', 'message' => 'Order status already delivered']);
                }
            }

            // Store old statuses before updating
            $oldStatuses = $orders->pluck('call_status', 'call_code')->toArray();

            // Update call_status
            CallMaster::whereIn('call_code', $arrcall_code)->update(['call_status' => $request->call_status]);

            // Update outstanding amount for delivered orders
            foreach ($orders as $order) {

                $this->manageCustomerStock($order->call_code, 3); //3-delivered

                if ($order->call_status == 3) { // Only deduct outstanding amount for delivered orders
                    $customer = CustomerMaster::where('cm_code', $order->client_code)->first();
                    if ($customer) {
                        $customer->cm_outstanding_amount -= $order->grand_total;
                        $customer->save();
                    }
                }
            }

            if ($request->call_status) {
                $this->sendOrderNotification($orders, 'Delivered');
            }

            $adminAuth = Auth::user()->name;
            foreach ($orders as $order) {
                $updateLog = new CallMaster();
                $updateLog->logStatusChange($order->call_code, $order->invoice_number, $oldStatuses[$order->call_code], $newStatus, $adminAuth);
            }

            return response()->json(['status' => 'success', 'message' => 'Order Status Updated Successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order status: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order status: ' . $e->getMessage()], 500);
        }
    }

    public function order_pend_deli_status_update(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required|array',
                'call_status' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
            }

            $call_code = $request->call_code;
            $newStatus = $request->call_status;

            $arrcall_code = array_column($call_code, 'call_code');
            $orders = CallMaster::whereIn('call_code', $arrcall_code)->get();

            foreach ($orders as $order) {
                if ($order->call_status == 4) {
                    return response()->json(['status' => 'error', 'message' => 'Cancelled order(s) cannot be marked as partial delivered']);
                }
            }

            // Store old statuses before updating
            $oldStatuses = $orders->pluck('call_status', 'call_code')->toArray();

            // Update call_status
            CallMaster::whereIn('call_code', $arrcall_code)->update(['call_status' => $request->call_status]);

            if ($request->call_status) {
                $this->sendOrderNotification($orders, 'Partial Delivered');
            }

            $adminAuth = Auth::user()->name;
            foreach ($orders as $order) {
                $updateLog = new CallMaster();
                $updateLog->logStatusChange($order->call_code, $order->invoice_number, $oldStatuses[$order->call_code], $newStatus, $adminAuth);
            }

            return response()->json(['status' => 'success', 'message' => 'Order Status Updated Successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order status partial delivered: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order status partial delivered: ' . $e->getMessage()], 500);
        }
    }

    // use for index file multiple order confirm and cancel 
    private function sendOrderNotification($orders, $status)
    {
        // dd('sadfasd',$orders);
        foreach ($orders as $order) {
            $employee = $order->employee;
            $partyName = $order->customer_party;
            $buyerName = $order->customer_cli;
            // dd($partyName);
            if ($employee && $employee->device_info) {

                $title = 'Order Status Updated';
                // $body_message = 'Your order status has been updated. Please check it.';
                $body_message = 'Order Number: ' . $order->invoice_number . ', Party Name: ' . $buyerName->cm_name . ', Status: ' . $status;

                if ($status === 'Cancelled' && isset($order->cancel_description)) {
                    $body_message .= "\nCancel Reason: " . $order->cancel_description;
                }

                $file = null;

                $deviceInfo = json_decode($employee->device_info, true);
                if (isset($deviceInfo['fcm_token'])) {
                    $fcmToken = $deviceInfo['fcm_token'];

                    // Send notification
                    $sendOrderResponse = $this->sendNotification($fcmToken, $title, $body_message, $file);

                    $notificationStatus = ($sendOrderResponse->status() === 200) ? 1 : 2;
                    $responseMessage = ($notificationStatus === 1) ? 'Status updated and notification sent successfully' : 'Status updated but notification not sent: ' . $sendOrderResponse->getReasonPhrase();

                    // Save notification in database
                    EmpNotification::create([
                        'en_title' => $title,
                        'en_message' => $body_message,
                        'emp_code' => $order->emp_code,
                        'en_status' => $notificationStatus, // 1-sent,2-failed
                        'en_failed_reason' => ($notificationStatus === 2) ? $responseMessage : null
                    ]);
                    return response()->json([
                        'status' => $notificationStatus === 1 ? 'success' : 'error',
                        'message' => $responseMessage,
                    ], $sendOrderResponse->status());
                } else {
                    // EmpNotification::create([
                    //     'en_title' => $title,
                    //     'en_message' => $body_message,
                    //     'emp_code' => $order->emp_code,
                    //     'en_status' => 2, // 1-sent,2-failed
                    //     'en_failed_reason' => 'FCM token not found in device info for order: ' . $order->call_code,
                    // ]);
                    Log::error('FCM token not found in device info for order: ' . $order->call_code);
                    return response()->json(['status' => 'success', 'message' => 'Status updated but Notification Not Sent']);
                }
            } else {
                Log::error('Employee not found or device info not set for order: ' . $order->call_code);
            }
        }
    }

    // below three use for open modal and update status
    public function update_status_conf_order(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required',
                'invoice_number' => 'required|unique:call_master,invoice_number,' . $request->call_code . ',call_code',
            ], [
                'call_code.required' => 'Call id is required',
                'invoice_number.required' => 'Invoice Number is required',
                'invoice_number.unique' => 'Invoice Number already exists',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Order Validation failed', 'errors' => $validator->errors()], 422);
            }

            $callMaster = CallMaster::findOrFail($request->call_code);
            if (!$callMaster) {
                throw new Exception('Order not found');
            }

            if ($callMaster->call_status == 4) {
                return response()->json(['message' => 'Order cannot be confirmed as it is already cancelled'], 422);
            }

            if ($callMaster->call_status == 1) {
                return response()->json(['status' => 'success', 'message' => 'Order status already confirmed']);
            }

            // Store the old status before updating
            $oldStatus = $callMaster->call_status;

            $callMaster->invoice_number = $request->invoice_number;
            $callMaster->call_status = 1; // confirmed
            $callMaster->save();

            // Send notification
            $this->sendOrderStatusNotification($callMaster, 'Confirmed');

            $adminAuth = Auth::user()->name;
            // Log the status change
            $callMaster->logStatusChange($callMaster->call_code, $callMaster->invoice_number, $oldStatus, $callMaster->call_status, $adminAuth);

            $waBuyer = waOrderMessage::where('category', 'buyer')->where('type', 'productive')->first();
            $waSeller = waOrderMessage::where('category', 'seller')->where('type', 'productive')->first();
            $waBuyerNonProd = waOrderMessage::where('category', 'buyer')->where('type', 'non_productive')->first();

            // send order messages to whatsapp
            if ($callMaster->product_order_type == 1) {
                if ($waBuyer && $waBuyer->status == 1) {
                    $evtData = explode(',', $waBuyer->event);
                    if (in_array('confirm', $evtData)) {
                        waOrderMessages($callMaster->call_code, $callMaster->client_code, $callMaster->party_code, $callMaster->invoice_number, $callMaster->grand_total, $callMaster->total_quantity, $waBuyer->template_id, $waBuyer->language, 'buyer', 1);
                    }
                }

                if ($waSeller && $waSeller->status == 1) {
                    $evtData = explode(',', $waSeller->event);
                    if (in_array('confirm', $evtData)) {
                        waOrderMessages($callMaster->call_code, $callMaster->client_code, $callMaster->party_code, $callMaster->invoice_number, $callMaster->grand_total, $callMaster->total_quantity, $waSeller->template_id, $waSeller->language, 'seller', 1);
                    }
                }
            } else {
                // non productive order messages if have
                if ($waBuyerNonProd && $waBuyerNonProd->status == 1) {
                    $evtData = explode(',', $waBuyerNonProd->event);
                    if (in_array('confirm', $evtData)) {
                        waOrderMessages($callMaster->call_code, $callMaster->client_code, $callMaster->party_code, $callMaster->invoice_number, $callMaster->grand_total, $callMaster->total_quantity, $waSeller->template_id, $waSeller->language, 'seller', 0);
                    }
                }
            }

            return response()->json(['status' => 'success', 'message' => 'Order Status updated successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order: ' . $e->getMessage()], 500);
        }
    }

    // modal open cancel status update function 
    public function update_status_cancel_order(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required',
                'invoice_number' => 'required|unique:call_master,invoice_number,' . $request->call_code . ',call_code',
                'cancel_description' => 'required',
            ], [
                'call_code.required' => 'Call id is required',
                'cancel_description.required' => 'Cancel Order Reason is required',
                'invoice_number.required' => 'Invoice Number is required',
                'invoice_number.unique' => 'Invoice Number already exists',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Order Validation failed', 'errors' => $validator->errors()], 422);
            }

            $callMaster = CallMaster::findOrFail($request->call_code);
            if (!$callMaster) {
                throw new Exception('Order not found');
            }

            if ($callMaster->call_status == 4) {
                return response()->json(['message' => 'Order cannot be cancelled as it is already cancelled'], 422);
            }

            // Store the old status before updating
            $oldStatus = $callMaster->call_status;

            if ($oldStatus == 3) { //3-delivered
                // Increase the stock for cancelled orders
                $this->manageCustomerStock($request->call_code, 4); //4-cancelled
            }

            // dd('vishal');
            $callMaster->cancel_description = $request->cancel_description;
            $callMaster->invoice_number = $request->invoice_number;
            $callMaster->call_status = 4; // cancelled
            $callMaster->save();

            // Send notification
            $this->sendOrderStatusNotification($callMaster, 'Cancelled');

            $adminAuth = Auth::user()->name;
            // Log the status change
            $callMaster->logStatusChange($callMaster->call_code, $callMaster->invoice_number, $oldStatus, $callMaster->call_status, $adminAuth);

            return response()->json(['status' => 'success', 'message' => 'Order Status updated successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order: ' . $e->getMessage()], 500);
        }
    }

    // modal open delivered status update function 
    public function update_status_deliev_order(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_code' => 'required',
                'invoice_number' => 'required|unique:call_master,invoice_number,' . $request->call_code . ',call_code',
            ], [
                'call_code.required' => 'Call id is required',
                'invoice_number.required' => 'Invoice Number is required',
                'invoice_number.unique' => 'Invoice Number already exists',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Order Validation failed', 'errors' => $validator->errors()], 422);
            }

            $callMaster = CallMaster::findOrFail($request->call_code);
            if (!$callMaster) {
                throw new Exception('Order not found');
            }

            if ($callMaster->call_status == 4) {
                return response()->json(['message' => 'Order cannot be delivered as it is already cancelled'], 422);
            }

            if ($callMaster->call_status == 3) {
                return response()->json(['status' => 'success', 'message' => 'Order status already delivered']);
            }

            // manage customer stock
            $this->manageCustomerStock($request->call_code, 3); //3-delivered

            // Store the old status before updating
            $oldStatus = $callMaster->call_status;

            // Update call_status
            $callMaster->invoice_number = $request->invoice_number;
            $callMaster->call_status = 3; // delivered
            $callMaster->save();

            // Deduct grand total from customer's outstanding amount
            $customerMaster = CustomerMaster::where('cm_code', $callMaster->client_code)->first();
            if ($customerMaster) {
                $customerMaster->cm_outstanding_amount -= $callMaster->grand_total;
                $customerMaster->save();
            }

            // Send notification
            $this->sendOrderStatusNotification($callMaster, 'Delivered');

            $adminAuth = Auth::user()->name;

            // Log the status change
            $callMaster->logStatusChange($callMaster->call_code, $callMaster->invoice_number, $oldStatus, $callMaster->call_status, $adminAuth);

            return response()->json(['status' => 'success', 'message' => 'Order Status updated successfully']);
        } catch (Exception $e) {
            Log::error('Error updating order: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the order: ' . $e->getMessage()], 500);
        }
    }

    private function manageCustomerStock($call_code, $status)
    {
        // Fetch the order details
        $call = CallMaster::where('call_code', $call_code)->first();

        $orders = OrderMaster::where('call_code', $call_code)->get();

        foreach ($orders as $order) {
            $product = ProductMaster::where('product_code', $order->product_code)
                ->where('product_status', 1)
                ->first();

            $custStock = CustomerStockMaster::where('cm_code', $call->party_code)
                ->where('product_code', $product->product_code)
                ->first();

            if ($status == 3) { //3-delivered
                // dd('get here');
                // Decrease the stock for delivered orders
                if ($custStock) {
                    $custStock->qty -= $order->quantity;
                    $custStock->save();
                }
            } elseif ($status == 4) { //4-cancelled
                // Increase the stock for cancelled orders
                if ($custStock) {
                    $custStock->qty += $order->quantity;
                    $custStock->save();
                }
            } else {
                // no action
            }
        }
    }

    private function sendOrderStatusNotification($callMaster, $status)
    {
        $partyName = $callMaster->customer_party;
        $buyerName = $callMaster->customer_cli;

        // Send notification
        $employee = $callMaster->employee;
        if ($employee && $employee->device_info) {

            $title = 'Order Status Updated';
            $body_message = 'Order Number: ' . $callMaster->invoice_number . ', Party Name: ' . $buyerName->cm_name . ', Status: ' . $status;
            // Include cancel reason if status is "Cancelled"
            if ($status === 'Cancelled' && isset($callMaster->cancel_description)) {
                $body_message .= "\nCancel Reason: " . $callMaster->cancel_description;
            }
            $file = null;

            $deviceInfo = json_decode($employee->device_info, true);
            if (isset($deviceInfo['fcm_token'])) {
                $fcmToken = $deviceInfo['fcm_token'];

                // Send notification
                $orderResponse = $this->sendNotification($fcmToken, $title, $body_message, $file);

                $notificationStatus = ($orderResponse->status() === 200) ? 1 : 2;
                $responseMessage = ($notificationStatus === 1) ? 'Status updated and notification sent successfully' : 'Status updated but notification not sent: ' . $orderResponse->getReasonPhrase();

                EmpNotification::create([
                    'en_title' => $title,
                    'en_message' => $body_message,
                    'emp_code' => $employee->emp_code,
                    'en_status' => $notificationStatus, // 1-sent,2-failed
                    'en_failed_reason' => ($notificationStatus === 2) ? $responseMessage : null
                ]);
                return response()->json([
                    'status' => $notificationStatus === 1 ? 'success' : 'error',
                    'message' => $responseMessage
                ], $orderResponse->status());
            } else {
                // EmpNotification::create([
                //     'en_title' => $title,
                //     'en_message' => $body_message,
                //     'emp_code' => $employee->emp_code,
                //     'en_status' => 2, // 1-sent,2-failed
                //     'en_failed_reason' => 'FCM token not found in device info for employee: ' . $employee->emp_code,
                // ]);
                Log::error('FCM token not found in device info for employee: ' . $employee->emp_code);
                return response()->json(['status' => 'success', 'message' => 'Status updated but Notification Not Sent']);
            }
        } else {
            Log::error('Employee not found or device info not set for order: ' . $callMaster->call_code);
        }
    }

    public function view_order_invoice(Request $request, $call_code)
    {

        // Query for CallMaster details
        $call = CallMaster::select(
            'call_master.call_code',
            'call_master.emp_code',
            'call_master.client_code',
            'call_master.party_code',
            'call_master.invoice_number',
            'call_master.packaging_charge',
            'call_master.transportation_charge',
            'call_master.transportation_name',
            'call_master.created_at',
            'call_master.remarks',
            'employee_master.emp_name as employee_name',
            'employee_master.emp_mobile as empMobile',
            'employee_master.emp_code',
            'customer_cli.cm_code as cli_code',
            'customer_cli.cm_name as cli_name',
            'customer_cli.cm_contact_person as cli_contact_person',
            'customer_cli.cm_address as cli_address',
            'customer_cli.cm_gst as cli_gst',
            'customer_cli.cm_mobile as cli_mobile',
            'customer_cli.cm_mobile2 as cli_mobile2',
            'customer_cli.cm_area as cli_area',
            'customer_cli.cm_type as orderName',
            'customer_cli.cm_pincode as cli_pincode',
            'customer_party.cm_code as party_code',
            'customer_party.cm_name as party_name',
            'customer_party.cm_contact_person as party_contact_person',
            'customer_party.cm_address as party_address',
            'customer_party.cm_gst as party_gst',
            'customer_party.cm_mobile as party_mobile',
            'customer_party.cm_mobile2 as party_mobile2',
            'customer_party.cm_area as party_area',
            'customer_party.cm_pincode as party_pincode',
        )
            ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
            ->join('customer_master as customer_cli', 'call_master.client_code', '=', 'customer_cli.cm_code')
            ->join('customer_master as customer_party', 'call_master.party_code', '=', 'customer_party.cm_code')
            ->where('call_master.call_code', $call_code)
            ->first();

        if (!$call) {
            // Handle scenario where $call is not found
            // For example, redirect or return an error response
            return response()->json(['error' => 'Call not found.'], 404);
        }

        if ($call->party_mobile && $call->party_mobile2) {
            $sellerNo = $call->party_mobile . ' / ' . $call->party_mobile2;
        } else {
            $sellerNo = $call->party_mobile;
        }

        $seller = new Party([
            'name' => $call->party_name . ' (' . $call->party_code . ')',
            'onName' => $call->party_name,
            'address' => $call->party_address . ' - ' . $call->party_pincode,
            'custom_fields' => [
                'GSTIN' => $call->party_gst,
            ],
            'phone' => $sellerNo,
        ]);

        if ($call->cli_mobile && $call->cli_mobile2) {
            $buyerNo = $call->cli_mobile . ' / ' . $call->cli_mobile2;
        } else {
            $buyerNo = $call->cli_mobile;
        }

        $buyer = new Party([
            'name' => $call->cli_name . ' - ' . $call->cli_area . ' (' . $call->cli_code . ')',
            'address' => $call->cli_address . ' - ' . $call->cli_pincode,
            'custom_fields' => [
                'GSTIN' => $call->cli_gst,
            ],
            'phone' => $buyerNo,
        ]);

        // Query for OrderMaster details
        $order = OrderMaster::select(
            'order_master.*',
            'product_master.product_name',
            'product_master.product_hsn_sac_code',
            'product_master.unit_size',
            'product_master.barcode',
            'uom_master.uom_name',
            'product_type.pt_name as productType'
        )
            ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
            ->join('product_type', 'product_master.pm_pt_id', '=', 'product_type.pt_id')
            ->join('uom_master', 'product_master.uom', '=', 'uom_master.uom_id')
            ->where('order_master.call_code', $call->call_code)
            ->get();

        if ($order->isEmpty()) {
            // Handle scenario where no orders are found
            return response()->json(['error' => 'No orders found for this call.'], 404);
        }

        // get logo from optios table where key is logo then get his value
        $logo = Options::where('key', 'logo')->first();
        $logo = $logo->value;

        $products = [];
        //extend invoice item add gst , hsn/sac code 

        //loop through order details and create invoice items
        foreach ($order as $item) {
            $products[] = (new CustomItem())->title($item->barcode . ' - ' . $item->product_name . ' (' . $item->productType . ')' . ' -' . $item->unit_size)
                ->pricePerUnit($item->rate_basic)
                ->quantity($item->quantity)
                ->units($item->uom_name)
                ->hsn($item->product_hsn_sac_code ?? 0)
                ->bunch($item->bunch)
                ->box($item->box)
                ->mrp($item->mrp)
                ->pcs($item->pcs)
                ->gst($item->gst_amount . ' (' . $item->gst . '%)')
                // ->taxRate($item->gst)
                ->subTotalPrice($item->grand_total)
                ->discount(0);
        }

        switch ($call->orderName) {
            case 5:
                $title = 'Retailer Order';
                break;
            case 4:
                $title = 'Dealer Order';
                break;
            case 3:
                $title = 'Distributor Order';
                break;
            case 2:
                $title = 'SS Order';
                break;
            default:
                $title = 'Sales Orders';
                break;
        }

        if ($call->invoice_number) {
            $customData = "<div>Order No : <strong>" . $call->invoice_number . "</strong></div>";
        } else {
            $customData = "<div>Order No : <strong>" . 'IV' . "</strong></div>";
        }
        //$customData = "<div>Order No : <strong>" . $call->invoice_number ?? '' . "</strong></div>";

        $date = new \DateTime($call->created_at);
        $formattedDate = $date->format('d/m/Y');
        $customData .= "<div>Order Date : <strong>" . $formattedDate . "</strong></div>";

        $todayDate = Carbon::now()->format('d/m/Y');
        $customData .= "<div>Due Date: <strong>" . $todayDate . "</strong></div>";

        $customData .= "<div>Order By : <strong>" . $call->employee_name . ' (' . $call->emp_code . ')' . "</strong></div>";

        if (!empty($call->empMobile)) {
            $customData .= "<div style='margin-top:2px;'>Contact No: <strong>" . $call->empMobile . "</strong></div>";
        } else {
            $customData .= "<div style='margin-top:2px;'>Contact No: </div>";
        }

        // $date = new \DateTime($call->created_at);
        // $formattedDate = $date->format('d/m/Y');

        // $customData .= "<div>Order Date: <strong>" . $formattedDate . "</strong></div>";

        if (!empty($call->transportation_name)) {
            $customData .= "<div style='margin-top:2px;'>Transport Name: <strong>" . $call->transportation_name . "</strong></div>";
        } else {
            $customData .= "<div style='margin-top:2px;'>Transport Name: </div>";
        }

        // $todayDate = Carbon::now()->format('d/m/Y');
        // $customData .= "<div>Due Date: <strong>" . $todayDate . "</strong></div>";

        //create invoice
        $invoice = Invoice::make('invoice')
            ->seller($seller)
            ->name($title)
            ->buyer($buyer)
            ->serialNumberFormat($call->invoice_number ?? 'INV-000')
            ->date($call->created_at)
            ->dateFormat('d/m/Y')
            ->currencySymbol('₹')
            ->currencyCode('INR')
            ->currencyFraction('Paisa')
            ->filename($call->call_code)
            ->addItems($products)
            ->shipping($call->transportation_charge)
            ->taxableAmount($call->packaging_charge)
            ->notes($call->remarks ?? '')
            ->setCustomData($customData);
        //get logo from public/images/nxclogo.png
        //->logo(public_path($logo));
        return $invoice->stream();
    }

    private function buildOrderQuery(Request $request, $cmType)
    {
        $query = CallMaster::select(
            'call_master.call_code',
            'call_master.emp_code',
            'call_master.invoice_number',
            'call_master.product_order_type',
            'call_master.client_code',
            'call_master.grand_total',
            'call_master.party_code',
            'call_master.call_status',
            'call_master.created_at as date',
            'call_master.start_time',
            'call_master.stop_time',
            'employee_master.emp_name as empName',
            'buyer.cm_name as buyerName',
            'buyer.cm_mobile as buyerMobile',
            'seller.cm_name as sellerName'
        )
            ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
            ->join('customer_master as buyer', function ($join) use ($cmType) {
                $join->on('buyer.cm_code', '=', 'call_master.client_code')
                    ->where('buyer.cm_type', '=', $cmType);
            })
            // ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
            ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
            // ->where('buyer.cm_type', $cmType)
            ->orderBy('call_master.created_at', 'desc');

        // Apply search criteria
        if ($searchTerm = $request->input('search')) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('call_master.call_code', 'LIKE', "%$searchTerm%")
                    ->orWhere('call_master.invoice_number', 'LIKE', "%$searchTerm%")
                    ->orWhere('buyer.cm_name', 'LIKE', "%$searchTerm%")
                    ->orWhere('seller.cm_name', 'LIKE', "%$searchTerm%")
                    ->orWhere('call_master.call_status', 'LIKE', "%$searchTerm%")
                    ->orWhere('employee_master.emp_name', 'LIKE', "%$searchTerm%");
            });
        }

        // Apply date range filter
        $dates = explode(',', $request->date);

        // $start_date = isset($dates[0]) ? $dates[0] : null;
        // $end_date = isset($dates[1]) ? $dates[1] : null;
        $start = $dates[0] ?? null;
        $end = $dates[1] ?? null;

        if ($start && $end) {
            $query->whereBetween('call_master.created_at', [
                date('Y-m-d 00:00:00', strtotime($start)),
                date('Y-m-d 23:59:59', strtotime($end))
            ]);
        } elseif ($start) {
            $query->whereDate('call_master.created_at', $start);
        }

        // Apply other filters
        if ($request->filled('employee')) {
            $query->where('call_master.emp_code', $request->employee);
        }

        if ($request->filled('invoice')) {
            $query->where('invoice_number', $request->invoice);
        }

        if ($request->filled('orderType')) {
            $map = [
                'pending' => 0,
                'confirm' => 1,
                'dispatch' => 2,
                'cancel' => 4,
                'delivered' => 3,
                'partialDelivered' => 5
            ];
            if (isset($map[$request->orderType])) {
                $query->where('call_status', $map[$request->orderType]);
            }
        }

        if ($request->filled('clientCode')) {
            $query->where('client_code', $request->clientCode);
        }
        if ($request->filled('partyCode')) {
            $query->where('party_code', $request->partyCode);
        }

        // Handle sorting
        if ($request->has('sort')) {
            foreach (json_decode($request->sort, true) as $sort) {
                $query->orderBy($sort['selector'], $sort['desc'] ? 'desc' : 'asc');
            }
        }

        return $query;
    }

    private function getOrderFilterList(Request $request, $cmType)
    {
        try {
            $validator = Validator::make($request->all(), [
                'date' => 'required',
            ], [
                'date.required' => 'Date is required',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Order Filter Validation failed', 'errors' => $validator->errors()], 422);
            }

            $query = $this->buildOrderQuery($request, $cmType);

            $orderList = $query->get();
            // dd($orderList);
            return response()->json([
                'status' => 'success',
                'data' => $orderList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getSsFilterList(Request $request)
    {
        return $this->getOrderFilterList($request, 2); // 2- ss
    }

    public function getDistributorFilterList(Request $request)
    {
        return $this->getOrderFilterList($request, 3); // 3- distributor
    }

    public function getDealerFilterList(Request $request)
    {
        return $this->getOrderFilterList($request, 4); // 4- dealer
    }

    public function getRetailerFilterList(Request $request)
    {
        return $this->getOrderFilterList($request, 5); // 5- retailer
    }

    private function getOrderAjaxData(Request $request, $cmType)
    {
        try {
            $query = CallMaster::select(
                'call_master.call_code',
                'call_master.emp_code',
                'call_master.invoice_number',
                'call_master.product_order_type',
                'call_master.client_code',
                'call_master.grand_total',
                'call_master.party_code',
                'call_master.call_status',
                DB::raw('CONCAT(DATE(call_master.created_at), " ", TIME(call_master.start_time)) as date'),
                'call_master.start_time',
                'call_master.stop_time',
                'employee_master.emp_name as empName',
                'buyer.cm_name as buyerName',
                'buyer.cm_mobile as buyerMobile',
                'seller.cm_name as sellerName'
            )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->where('buyer.cm_type', $cmType)
                ->orderBy('call_master.created_at', 'desc');

            if (!$request->input('export')) {
                // Apply default filters only if not exporting
                $todayDate = date('Y-m-d');
                $start_date = $this->today->startOfDay()->toDateTimeString();
                $end_date = $this->today->endOfDay()->toDateTimeString();
                $query->whereBetween('call_master.created_at', [$start_date, $end_date]);
                //->whereDate('call_master.created_at', $todayDate);
            }

            // Apply search criteria
            // if ($searchTerm = $request->input('search')) {
            //     $query->where(function ($q) use ($searchTerm) {
            //         $q->where('call_master.call_code', 'LIKE', "%$searchTerm%")
            //             ->orWhere('call_master.invoice_number', 'LIKE', "%$searchTerm%")
            //             ->orWhere('buyer.cm_name', 'LIKE', "%$searchTerm%")
            //             ->orWhere('seller.cm_name', 'LIKE', "%$searchTerm%")
            //             ->orWhere('call_master.call_status', 'LIKE', "%$searchTerm%")
            //             ->orWhere('employee_master.emp_name', 'LIKE', "%$searchTerm%");
            //     });
            // }

            // Apply date range filter
            $dates = explode(',', $request->date);

            $start_date = $dates[0] ?? null;
            $end_date = $dates[1] ?? null;

            if ($start_date) {
                $start = Carbon::parse($start_date)->startOfDay();

                if ($end_date) {
                    $end = Carbon::parse($end_date)->endOfDay();
                    $query->whereBetween('call_master.created_at', [$start, $end]);
                } else {
                    $query->whereDate('call_master.created_at', $start->toDateString());
                }
            }

            // Apply other filters
            if ($employee = $request->employee) {
                $query->where('call_master.emp_code', $employee);
            }

            if ($invoice = $request->invoice) {
                $query->where('invoice_number', $invoice);
            }

            if ($orderType = $request->orderType) {
                $statusMap = [
                    'pending' => 0,
                    'confirm' => 1,
                    'dispatch' => 2,
                    'cancel' => 4,
                    'delivered' => 3,
                    'partialDelivered' => 5
                ];

                if (array_key_exists($orderType, $statusMap)) {
                    $query->where('call_status', $statusMap[$orderType]);
                }
            }

            if ($clientCode = $request->clientCode) {
                $query->where('client_code', $clientCode);
            }

            if ($partyCode = $request->partyCode) {
                $query->where('party_code', $partyCode);
            }

            // Handle sorting
            if ($request->has('sort')) {
                $sorts = json_decode($request->input('sort'), true);
                foreach ($sorts as $sort) {
                    $query->orderBy($sort['selector'], $sort['desc'] ? 'desc' : 'asc');
                }
            }

            if ($request->input('export')) {
                // Fetch all data without pagination
                $orderData = $query->get();
                return response()->json([
                    'data' => $orderData,
                    'total' => $orderData->count(),
                    'summary' => null // Add summary data if needed
                ]);
            }

            $pageSize = $request->input('pageSize', 10);
            $page = $request->input('page', 1);
            $orderData = $query->paginate($pageSize, ['*'], 'page', $page);

            return response()->json([
                'data' => $orderData->items(),
                'total' => $orderData->total(),
                'summary' => null // Add summary data if needed
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getSsOrderAjaxData(Request $request)
    {
        return $this->getOrderAjaxData($request, 2); // 5- ss
    }

    public function getDistributorOrderAjaxData(Request $request)
    {
        return $this->getOrderAjaxData($request, 3); // 3- distributor
    }

    public function getDealerOrderAjaxData(Request $request)
    {
        return $this->getOrderAjaxData($request, 4); // 4- dealer
    }

    public function getRetailerOrderAjaxData(Request $request)
    {
        return $this->getOrderAjaxData($request, 5); // 5- retailer
    }

    public function getDispatchOrderAjaxData(Request $request)
    {
        try {
            $pageSize = $request->input('pageSize', 10);
            $todayDate = date('Y-m-d');
            $searchTerm = $request->input('search');

            $query = CallMaster::select(
                'call_master.call_code',
                'call_master.emp_code',
                'call_master.invoice_number',
                'call_master.product_order_type',
                'call_master.client_code',
                'call_master.grand_total',
                'call_master.party_code',
                'call_master.call_status',
                'call_master.created_at as date',
                'call_master.start_time',
                'call_master.stop_time',
                'employee_master.emp_name as empName',
                'buyer.cm_name as buyerName',
                'seller.cm_name as sellerName'
            )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->whereDate('call_master.created_at', $todayDate)
                ->orderBy('call_master.created_at', 'desc');

            // Apply search criteria
            if ($searchTerm) {
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('call_master.call_code', 'LIKE', "%$searchTerm%")
                        ->orWhere('call_master.invoice_number', 'LIKE', "%$searchTerm%")
                        ->orWhere('buyer.cm_name', 'LIKE', "%$searchTerm%")
                        ->orWhere('seller.cm_name', 'LIKE', "%$searchTerm%")
                        ->orWhere('call_master.call_status', 'LIKE', "%$searchTerm%")
                        ->orWhere('employee_master.emp_name', 'LIKE', "%$searchTerm%");
                    // Add more search criteria if needed
                });
            }

            // Check if this is an export request
            if ($request->input('export')) {
                // Fetch all data without pagination
                $disOrderData = $query->get();
                return response()->json([
                    'data' => $disOrderData,
                    'total' => $disOrderData->count(),
                    'summary' => null // Add summary data if needed
                ]);
            }

            $DispatchOrderData = $query->paginate($pageSize);

            return response()->json($DispatchOrderData);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
