<div class="page">
    <div class="header-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12" id="cols">
                    @include('customers.customers_header')
                </div>
            </div>
        </div>
    </div>
    <div class="page-content container-fluid table_content_page">
        @include('components.success_error_message')
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('Filter Retailer List') }}</h3>
                <div class="panel-actions" style="right: 10px !important">
                    {{-- add search button --}}
                    <button id="toggleFormButton" class="waves-effect waves-classic btn btn-primary">
                        <i class="bi bi-funnel fa-lg" aria-hidden="true"></i>
                    </button>
                    <button onclick="window.location='{{ route('add_customer', ['type' => 'retailer']) }}'"
                        class="btn btn-primary btn-sm waves-effect waves-classic">{{ __('Add Retailer') }}</button>
                </div>
            </div>
            <div class="panel-body container-fluid" id="dealerFilterFormContainer"
                style="padding:15px 0px 0px 0px !important; display: none;">
                <form id="custRetailerFilterForm" class="add_form">
                    @csrf
                    <div class="col-md-12">
                        <div class="col-sm-12 col-md-6">

                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('State') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchState chosen-select form-control" tabindex="-1"
                                        id="searchState" name="searchState">
                                        <option value=""></option>
                                        @foreach ($state_list as $state)
                                            <option value="{{ $state->state_id }}">{{ $state->state_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('District') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchDistrict chosen-select form-control" tabindex="-1"
                                        id="searchDistrict" name="searchDistrict">
                                        <option value=""></option>
                                        {{-- @foreach ($districtList as $dist)
                                            <option value="{{ $dist->dm_id }}">{{ $dist->dm_name }}
                                            </option>
                                        @endforeach --}}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Town') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchTown chosen-select form-control" tabindex="-1"
                                        id="searchTown" name="searchTown">
                                        <option value=""></option>
                                        {{-- @foreach ($townList as $town)
                                            <option value="{{ $town->town_id }}">{{ $town->town_name }}
                                            </option>
                                        @endforeach --}}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Retailer') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchRetailer form-control" name="searchRetailer"
                                        id="searchRetailer">
                                        <option></option>
                                        <!-- append from search droupdown using ajax -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Beat') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchBeat form-control" name="searchBeat" id="searchBeat">
                                        <option></option>
                                        @foreach ($beat_list as $beat)
                                            <option value="{{ $beat->beat_code }}">{{ $beat->beat_name }} -
                                                {{ $beat->beat_code }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Market Type') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchMarType chosen-select form-control" id="searchMarType"
                                        name="searchMarType">
                                        <option value=""></option>
                                        @foreach ($marketType as $mtype)
                                            <option value="{{ $mtype->mtm_id }}">
                                                {{ $mtype->mtm_type }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('SS') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchSs form-control" name="searchSs" id="searchSs">
                                        <option></option>
                                        @foreach ($ss_list as $ss)
                                            <option value="{{ $ss->cm_code }}">{{ $ss->cm_name }} -
                                                {{ $ss->cm_code }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Status') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchstatus chosen-select form-control" id="searchstatus"
                                        name="searchstatus">
                                        <option value=""></option>
                                        <option value="active">{{ __('Active') }}</option>
                                        <option value="deactive">{{ __('Deactive') }}</option>
                                    </select>
                                </div>
                            </div>

                        </div>
                    </div>
                    {{-- <div style="width:100%;float: left"></div> --}}
                    <div class="clear-both submit-toolbar p-10">
                        <button class="btn btn-primary btn-sm waves-effect waves-classic" id="apply"><i
                                class="fa-filter mr-10" aria-hidden="true"></i>{{ __('Apply Filter') }}</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('Retailer List') }}</h3>
                <div class="panel-actions">
                    <input type="text" id="retailerSearchInput" placeholder="{{ __('Search') }}...">
                </div>
            </div>
            <div class="panel-body p-0">
                <div id="retailer_list_table"></div>
            </div>
        </div>
    </div>
</div>

{{-- button onclick search area hide and unhide --}}
<script>
    $(document).ready(function() {
        $('#toggleFormButton').on('click', function() {
            $('#dealerFilterFormContainer').toggle();
        });
    });
</script>

<script type="text/javascript">
    mark_sidebar_active("customer");
    var dataGrid
    $(document).ready(function() {
        var pageSize = 10;
        dataGrid = $("#retailer_list_table").dxDataGrid({

            // function PATH: public/js/customFunction.js
            dataSource: customServerSidePagination("#retailerSearchInput",
                "{{ route('getRetailerAjaxData') }}"),

            remoteOperations: {
                paging: true, // Enable remote paging
                sorting: true
            },
            // columnHidingEnabled: true,
            allowColumnResizing: true,
            columnAutoWidth: true,
            showRowLines: true,
            rowAlternationEnabled: true,
            showBorders: true,
            filterRow: {
                visible: false
            },
            columnChooser: {
                enabled: true,
                mode: "select"
            },
            columnFixing: {
                enabled: true
            },
            export: {
                enabled: false,
            },
            paging: {
                pageSize: pageSize
            },
            pager: {
                showPageSizeSelector: false,
                showNavigationButtons: true,
                allowedPageSizes: [5, 10, 20],
                showInfo: true
            },
            headerFilter: {
                visible: true
            },
            columns: [{
                    dataField: "autoIncrementId",
                    caption: "#",
                    dataType: "autoinc",
                    fixed: true,
                    width: 50,
                    allowFiltering: false,
                    allowSorting: false,
                },
                {
                    allowExporting: false,
                    dataField: "cm_code",
                    caption: "{{ __('Action') }}",
                    dataType: "edit",
                    showInColumnChooser: false,
                    allowFiltering: false,
                    allowSorting: false,
                    width: 80,
                    fixed: true,
                    fixedPosition: "left",
                    cellTemplate: function(container, options) {
                        container.append(
                            "<div id='disFlex'><a id='pointerCursor' onClick='editCustomerRetailer(\"" +
                            options.value +
                            "\")' ><i class='bi bi-person-fill-gear' style='font-size:20px;'></i></a></div>"
                        );
                    },
                },
                // {
                //     dataField: "cm_code",
                //     caption: "Code"
                // },
                {
                    dataField: "cm_name",
                    caption: "{{ __('Name') }}",
                    cellTemplate: function(container, options) {
                        var cmName = options.data.cm_name;
                        var cmArea = options.data.cm_area;
                        // Combine cm_name and cm_area into one line
                        container.text(cmName + ' - ' + cmArea);
                    }
                },
                {
                    dataField: "cm_mobile",
                    caption: "{{ __('Mobile') }}"
                },
                {
                    visible: false,
                    dataField: "cm_mobile2",
                    caption: "{{ __('Mobile 2') }}"
                },
                {
                    visible: false,
                    dataField: "cm_email",
                    caption: "{{ __('Email') }}"
                },
                {
                    dataField: "cm_address",
                    caption: "{{ __('Address') }}"
                },
                {
                    visible: false,
                    dataField: "cm_pincode",
                    caption: "{{ __('Pincode') }}"
                },
                {
                    visible: false,
                    dataField: "cm_gst",
                    caption: "{{ __('Gst') }}"
                },
                {
                    visible: false,
                    dataField: "cm_pan",
                    caption: "{{ __('Pan') }}"
                },
                {
                    visible: false,
                    dataField: "cm_outstanding_amount",
                    caption: "{{ __('Outstanding Amount') }}"
                },
                {
                    visible: false,
                    dataField: "cm_contact_person",
                    caption: "{{ __('Contact Person') }}"
                }, 
                {
                    visible: false,
                    dataField: "cm_area",
                    caption: "{{ __('Area') }}"
                },
                {
                    dataField: "cust_town_relation.town_name",
                    caption: "{{ __('Town') }}"
                },
                {
                    dataField: "market_type_master.mtm_type",
                    caption: "{{ __('Market Type') }}"
                },
                {
                    dataField: "cm_status",
                    caption: "{{ __('Status') }}",
                    dataType: "string",
                    cellTemplate: function(container, options) {
                        if (options.data.cm_status == 1) {
                            $('<span>')
                                .addClass('badge badge-success')
                                .css('font-size', '+=11')
                                .text('{{ __('Active') }}')
                                .appendTo(container);
                        } else if (options.data.cm_status == 0) {
                            $('<span>')
                                .addClass('badge badge-danger')
                                .css('font-size', '+=11')
                                .text('{{ __('Inactive') }}')
                                .appendTo(container);
                        } else {
                            $('<span>')
                                .addClass('badge badge-danger')
                                .css('font-size', '+=11')
                                .text('{{ __('Inactive') }}')
                                .appendTo(container);
                        }
                    },
                    lookup: {
                        dataSource: [{
                                value: 1,
                                text: '{{ __('Active') }}'
                            },
                            {
                                value: 0,
                                text: '{{ __('Inactive') }}'
                            }
                        ],
                        displayExpr: 'text',
                        valueExpr: 'value'
                    }
                },
            ],
            onToolbarPreparing: function(e) {
                var toolbarItems = e.toolbarOptions.items;
                toolbarItems.push({
                    location: "after",
                    widget: "dxButton",
                    options: {
                        icon: "exportxlsx",
                        // text: "Export",
                        onClick: function() {
                            exportAllRetailerData();
                        }
                    }
                });
            }
        }).dxDataGrid('instance');

        // Add event listener for the search input
        $("#retailerSearchInput").on("input", function() {
            dataGrid.refresh();
        });

    });

    // filter distributor function
    $(document).ready(function() {

        $('#custRetailerFilterForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();

            $.ajax({
                url: "{{ route('getFilterRetailerList') }}",
                type: "GET",
                data: formData,
                dataType: "JSON",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data) {
                    if (data.status == "success") {
                        getFilterRetailerTableData(data.data);
                        setTimeout(function() {
                            $("#retailer_list_table").show();
                        }, 1000);
                    } else {
                        toastr.error(data.message);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    toastr.error('Error: ' + textStatus);
                }
            });
        });
    });

    function getFilterRetailerTableData(newData) {

        const retailerFilterData = newData.map((item, index) => {
            return {
                ...item,
                autoIncrementId: index + 1
            };
        });

        dataGrid.option("dataSource", retailerFilterData);
    }

    function exportAllRetailerData() {
        var formData = $("#custRetailerFilterForm").serialize();
        var searchTerm = $("#retailerSearchInput").val();

        $.ajax({
            url: "{{ route('getRetailerAjaxData') }}",
            dataType: "json",
            data: formData + "&search=" + searchTerm + "&export=true",
            success: function(response) {
                var workbook = new ExcelJS.Workbook();
                var worksheet = workbook.addWorksheet("Retailer List");

                // Get visible columns from dxDataGrid
                const gridInstance = $("#retailer_list_table").dxDataGrid("instance");
                const visibleColumns = gridInstance.getVisibleColumns();

                // Build worksheet columns dynamically based on visible columns
                const worksheetColumns = visibleColumns
                    .filter(col => col.dataField && col.dataField !== "cm_code") // exclude cm_code (Action)
                    .map(col => {
                        return {
                            header: col.caption || col.dataField,
                            key: col.dataField,
                            width: 20
                        };
                    });

                // Always add "#" (index) column at the start
                worksheet.columns = [{
                    header: "#",
                    key: "autoIncrementId",
                    width: 10
                }, ...worksheetColumns];

                // Add rows
                response.data.forEach(function(item, index) {
                    let rowData = {
                        autoIncrementId: index + 1
                    };

                    worksheetColumns.forEach(col => {
                        const fieldPath = col.key.split('.');
                        let value = item;

                        for (let i = 0; i < fieldPath.length; i++) {
                            value = value ? value[fieldPath[i]] : '';
                        }

                        // Status handling
                        if (col.key === 'cm_status') {
                            value = value == 1 ? 'Active' : 'Inactive';
                        }

                        rowData[col.key] = value ?? '';
                    });

                    worksheet.addRow(rowData);
                });

                workbook.xlsx.writeBuffer().then(function(buffer) {
                    saveAs(new Blob([buffer], {
                        type: "application/octet-stream"
                    }), "Retailer List.xlsx");
                });
            },
            error: function() {
                alert("Data loading error");
            }
        });
    }

    function editCustomerRetailer(cm_code) {
        $.ajax({
            url: "edit_retailer/" + cm_code,
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.status === "success") {
                    var data = response.data;
                    var modal = $("#retailer_view_details_modal");
                    modal.modal("show");

                    // modal.find("#cm_code").val(data.cm_code);

                    modal.find("#cm_code").val(data.cm_code);
                    modal.find("#cm_name").val(data.cm_name);
                    modal.find("#cm_email").val(data.cm_email);
                    modal.find("#cm_mobile").val(data.cm_mobile);
                    modal.find("#second_mobile").val(data.cm_mobile2 || null);
                    // modal.find("#latitude").val(data.latitude);
                    // modal.find("#longitude").val(data.longitude);
                    modal.find("#cm_outstanding_amount").val(data.cm_outstanding_amount);
                    modal.find("#cm_gst").val(data.cm_gst);
                    modal.find("#cm_pan").val(data.cm_pan);
                    modal.find("#cm_contact_person").val(data.cm_contact_person);
                    modal.find("#cm_pincode").val(data.cm_pincode);
                    modal.find("#cm_area").val(data.cm_area);
                    modal.find("#cm_address").val(data.cm_address);
                    modal.find("#beat_assign").val(data.beatRel).trigger('change');
                    modal.find("#cust_category").val(data.categoryRel).trigger('change');
                    modal.find("#grade_type").val(data.gradeRel).trigger('change');

                    var anni_date = data.anni_date;
                    modal.find("#anni_date").val(''); // Clear the previous value
                    if (anni_date) {
                        modal.find("#anni_date").val(anni_date);
                    }
                    var birth_date = data.birth_date;
                    modal.find("#birth_date").val(''); // Clear the previous value
                    if (birth_date) {
                        modal.find("#birth_date").val(birth_date);
                    }

                    modal.find("#cst_edit_state_name").val(data.state_id).trigger('change');

                    setTimeout(function() {
                        modal.find("#cst_edit_district_name").val(data.district_id).trigger(
                            'change');
                    }, 1000); // Delay of 1 second

                    setTimeout(function() {
                        modal.find("#cst_edit_town_name").val(data.town_id).trigger('change');
                    }, 2000); // Delay of 2 seconds

                    // select market type when open modal
                    var marketTypeSelect = modal.find("#cm_market_type");
                    marketTypeSelect.val(data.marketType).trigger('change');
                    // modal.find("#cm_market_type").val(data.marketType);
                    modal.find("#parent_ss").val(data.cm_relation_code).trigger('change');

                    var assignSelect = modal.find("#assign_to");
                    assignSelect.val(data.emp_ids).trigger('change');

                    modal.find("#latitude").val(data.latitude);
                    modal.find("#longitude").val(data.longitude);

                    if (data.retailer_image && data.retailer_image.length > 0) {
                        var images = data.retailer_image[0].split(',');
                        var image_html = "<div class='row'>"; // Start the row
                        if (images.length > 0) {
                            images.forEach(function(image, index) {
                                image_html +=
                                    "<div class='col-md-4'><a href='{{ asset('uploads/retailer_images') }}/" +
                                    image.trim() +
                                    "' target='_blank'><img src='{{ asset('uploads/retailer_images') }}/" +
                                    image.trim() +
                                    "' class='img-thumbnail' style='width: 100px; height: 100px; margin: 3px;'></a></div>";
                                if ((index + 1) % 3 === 0 && index !== images.length - 1) {
                                    image_html +=
                                        "</div><div class='row'>"; // End the row and start a new one every 3 images
                                }
                            });
                        }
                        image_html += "</div>"; // End the row
                        // Append the images to the #image_preview div
                        modal.find("#image_preview").html(image_html);
                    } else {
                        modal.find("#image_preview").html(
                            ""); // Clear the #image_preview div if there are no images
                    }

                    modal.find("#cm_status").val(data.cm_status).trigger('change');
                    //modal.find("#cm_status").prop('checked', data.cm_status);

                    //edit ss pin
                    modal.find("#epin_cm_id").val(data.cm_code);
                    modal.find("#cm_login_pin").val(data.cm_login_pin);

                    // user info
                    modal.find("#uinfo_cm_id").val(data.cm_code);
                    modal.find('#cst_name').text(data.cm_name)
                    // modal.find("#i_emp_mobile").text(data.emp_mobile);
                    modal.find("#cst_mobile").text(data.cm_mobile);
                    modal.find("#cst_email").text(data.cm_email || 'NA');
                    modal.find("#cst_code").text(data.cm_code);
                    //gst
                    modal.find("#cst_gst_code").text(data.cm_gst || 'NA');
                    //pan
                    modal.find("#cst_pan_code").text(data.cm_pan || 'NA');
                    // contact person
                    modal.find("#cst_cont_pers").text(data.cm_contact_person);
                    //outstanding amount
                    modal.find("#cst_outst_amo").text(data.cm_outstanding_amount || 'NA');
                    //pincode
                    modal.find("#cst_pin").text(data.cm_login_pin);

                    modal.find("#cst_assign_emp_name").val('');
                    modal.find("#cst_assign_emp_name").html(data.cst_emp_assign_name.length > 0 ? data
                        .cst_emp_assign_name
                        .join('<br>') : 'NA');

                    modal.find("#created_emp_code").val(data.created_emp_code).trigger('change');

                    // order info
                    modal.find("#uorder_cm_id").val(data.cm_code);

                    // payment info
                    modal.find("#upayment_cm_id").val(data.cm_code);
                }
            }
        });
    }

    function updateCustomerRetailer() {
        event.preventDefault();
        var data = new FormData($("#edit_retailer_form")[0]);

        $.ajax({
            url: "{{ route('update_customer', ['type' => 'retailer']) }}",
            type: "POST",
            data: data,
            dataType: "JSON",
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.status == "success") {
                    $("#retailer_view_details_modal").modal("hide");
                    toastr.success(data.message);
                    // reloadCustRetailerDataTable('retailer');
                    // Refresh the data grid
                    dataGrid.refresh();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                if (jqXHR.responseJSON && jqXHR.responseJSON.errors) {
                    // If response contains validation errors
                    var errors = jqXHR.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        toastr.error(value);
                    });
                } else {
                    toastr.error("An error occurred while updating the retailer");
                }
            }
        });
    }

    function updateCustomerRetailerPin() {
        // prevent form submission
        event.preventDefault();
        var data = new FormData($("#retailer_edit_pin_form")[0]);

        $.ajax({
            url: "{{ route('update_customer_pin') }}",
            type: "POST",
            data: data,
            dataType: "JSON",
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.status == "success") {
                    $("#retailer_view_details_modal").modal("hide");
                    toastr.success(data.message);
                    // reloadCustRetailerDataTable('retailer');
                    dataGrid.refresh();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                toastr.error('Error: ' + textStatus);
            }
        });
    }

    // reloadDataTable() function called from public/js/customFunction.js
    // function reloadCustRetailerDataTable(type) {
    //     const url = "{{ route('updateTableRefresh', ['type' => '__type__']) }}".replace('__type__', type);
    //     const sortField = 'cm_code';
    //     reloadDataTable(url, sortField);
    // }
</script>

<script>
    $(".select2_searchBeat").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Beat') }}",
        allowClear: true
    });
    $(".select2_searchSs").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select SS') }}",
        allowClear: true
    });
    $(".select2_searchState").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select State') }}",
        allowClear: true
    });
    $(".select2_searchDistrict").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select District') }}",
        allowClear: true
    });
    $(".select2_searchTown").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Town') }}",
        allowClear: true
    });
    $(".select2_searchHq").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Headquarter') }}",
        allowClear: true
    });
    $(".select2_searchMarType").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Market Type') }}",
        allowClear: true
    });
    $(".select2_searchstatus").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Status') }}",
        allowClear: true
    });
</script>
<script>
    $(document).ready(function() {
        $('.select2_searchRetailer').select2({
            placeholder: '{{ __('Search Retailer') }}',
            minimumInputLength: 2,
            ajax: {
                url: "{{ route('searchRetailerByName') }}",
                dataType: 'json',
                delay: 500, // Delay in milliseconds before making the AJAX request
                processResults: function(data) {
                    // Process the JSON data received from the server
                    var options = [];

                    // Loop through the data and construct Select2 option objects
                    $.each(data, function(index, retailer) {
                        options.push({
                            id: retailer
                                .cm_code, // Assuming cm_code is the unique identifier
                            text: retailer.cm_name + ' (' + retailer.cm_area +
                                ')' // Assuming cm_name is the text to display
                        });
                    });

                    // Return the constructed options
                    return {
                        results: options
                    };
                },
                // Additional parameters to send with the request
                data: function(params) {
                    return {
                        term: params.term // Search term entered by the user
                    };
                }
            }
        });
    });
</script>


<script>
    // get district data on state change
    $(document).ready(function() {
        $('#searchState').change(function() {
            var stateId = $(this).val();
            if (stateId) {
                $.ajax({
                    url: "{{ route('get_district_by_state_id') }}",
                    method: 'GET',
                    data: {
                        state_id: stateId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#searchDistrict').empty();
                        var options = '<option value="">Select District</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#searchDistrict').html(options);
                    }
                });
            } else {
                $('#searchDistrict').empty();
            }
        });
    });

    // get town data on district change
    $(document).ready(function() {
        $('#searchDistrict').change(function() {
            var districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: "{{ route('get_town_by_dist_id') }}",
                    method: 'GET',
                    data: {
                        district_id: districtId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#searchTown').empty();
                        var options = '<option value="">Select Town</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#searchTown').html(options);
                    }
                });
            } else {
                $('#searchTown').empty();
            }
        });
    });
</script>
