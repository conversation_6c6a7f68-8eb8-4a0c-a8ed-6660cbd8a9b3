<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;

class NewPartyReport implements FromCollection, WithHeadings, WithCustomStartCell, WithEvents
{
    protected $data;
    protected $title;

    public function __construct($data, $title)
    {
        $this->data = $data;
        $this->title = $title;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return collect($this->data)->map(function ($row) {
            return [
                'created_employee' => $row->emp_code . ' - ' . $row->emp_name,
                'code' => $row->cm_code,
                'name' => $row->cm_name,
                'mobile' => $row->cm_mobile,
                'email' => $row->cm_email,
                'address' => $row->cm_address,
                'pincode' => $row->cm_pincode,
                'gst' => $row->cm_gst,
                'pan' => $row->cm_pan,
                'status' => $this->formatStatus($row->cm_status),
                'outstanding_amount' => $row->cm_outstanding_amount,
                'contact_person' => $row->cm_contact_person,
                'area' => $row->cm_area,
                'type' => $this->formatCustomerType($row->cm_type),
                'market_type' => $row->mtm_type,
                'town' => $row->town_name,
                'create_date' => date('d-m-Y', strtotime($row->created_at))
            ];
        });
    }

    /**
     * Format customer status
     */
    private function formatStatus($status): string
    {
        return $status == 1 ? 'Active' : 'Inactive';
    }

    /**
     * Format customer type
     */
    private function formatCustomerType($type): string
    {
        return match ($type) {
            2 => 'SS',
            3 => 'Distributor',
            4 => 'Dealer',
            5 => 'Retailer',
            default => 'No Type',
        };
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Employee',
            'Code',
            'Name',
            'Mobile',
            'Email',
            'Address',
            'Pincode',
            'GST',
            'PAN',
            'Status',
            'Outstanding Amount',
            'Contact Person',
            'Area',
            'Type',
            'Market Type',
            'Town',
            'Create Date'
        ];
    }

    /**
     * @return string
     */
    public function startCell(): string
    {
        return 'A3';
    }

    private function getTotalCustomerCount(): int
    {
        return count($this->data);
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $worksheet = $event->sheet->getDelegate();
                $lastColumn = chr(64 + count($this->headings()));

                // Set and style title
                $worksheet->mergeCells("A1:{$lastColumn}1");
                $worksheet->setCellValue('A1', $this->title);
                
                // Style title cell
                $worksheet->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER
                    ]
                ]);
                
                $worksheet->setCellValue('A2', 'Total Customers: ' . $this->getTotalCustomerCount());
                $worksheet->mergeCells("A2:B2");
                $worksheet->getStyle('A2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_LEFT
                    ]
                ]);

                // Style header row
                $worksheet->getStyle("A3:{$lastColumn}3")->applyFromArray([
                    'font' => [
                        'bold' => true
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER
                    ]
                ]);
                
                // Auto-size all columns
                foreach (range('A', $lastColumn) as $column) {
                    $worksheet->getColumnDimension($column)->setAutoSize(true);
                }
            }
        ];
    }
}