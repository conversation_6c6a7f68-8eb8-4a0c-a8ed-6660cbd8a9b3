<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class TargetVsAchievementReportExport implements FromCollection, WithStyles, WithCustomStartCell, ShouldAutoSize
{
    protected $data;
    protected $year;
    protected $type;

    public function __construct(array $data)
    {
        $this->data = $data;
        $this->year = $data['year'];
        $this->type = $data['type'];
    }

    public function collection()
    {
        $rows = [];

        // Title Row
        $rows[] = ["Target Vs Achievement Report - {$this->type} - {$this->year}"];

        // Employee Details Row
        $rows[] = [
            'Employee Code',
            'Employee Name',
            'Designation',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
        ];

        $rows[] = [
            $this->data['employee']['empCode'],
            $this->data['employee']['empName'],
            $this->data['employee']['designation'],
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
        ];

        // Empty Row
        $rows[] = array_fill(0, 19, '');

        // Section Headers Row
        $rows[] = [
            '',
            'Primary',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            'Secondary',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
        ];

        // Headers Row
        $rows[] = [
            'Month',
            'Monthly',
            '',
            '',
            'Quarterly',
            '',
            '',
            'Yearly',
            '',
            '',
            'Monthly',
            '',
            '',
            'Quarterly',
            '',
            '',
            'Yearly',
            '',
            ''
        ];

        // Sub Headers Row
        $rows[] = [
            '',
            'Target',
            'Achieved',
            '%',
            'Target',
            'Achieved',
            '%',
            'Target',
            'Achieved',
            '%',
            'Target',
            'Achieved',
            '%',
            'Target',
            'Achieved',
            '%',
            'Target',
            'Achieved',
            '%'
        ];

        // Month rows
        for ($month = 1; $month <= 12; $month++) {
            $monthData = $this->data['targets']['monthly'][$month] ?? null;
            $quarter = ceil($month / 3);
            $quarterKey = "Q{$quarter}-{$this->year}";
            $quarterlyData = $this->data['targets']['quarterly'][$quarterKey] ?? null;
            $yearlyData = $this->data['targets']['yearly'] ?? null;

            $row = [
                date('F', mktime(0, 0, 0, $month, 1)),
                // Primary Monthly
                $monthData['priTarget'] ?? 0,
                $monthData['primaryAchievement'] ?? 0,
                $monthData['primaryAchievementPercentage'] ?? 0,
                // Primary Quarterly
                ($month % 3 === 1) ? ($quarterlyData['priTarget'] ?? 0) : 0,
                ($month % 3 === 1) ? ($quarterlyData['primaryAchievement'] ?? 0) : 0,
                ($month % 3 === 1) ? ($quarterlyData['primaryAchievementPercentage'] ?? 0) : 0,
                // Primary Yearly
                ($month === 1) ? ($yearlyData['priTarget'] ?? 0) : 0,
                ($month === 1) ? ($yearlyData['primaryAchievement'] ?? 0) : 0,
                ($month === 1) ? ($yearlyData['primaryAchievementPercentage'] ?? 0) : 0,
                // Secondary Monthly
                $monthData['secTarget'] ?? 0,
                $monthData['secondaryAchievement'] ?? 0,
                $monthData['secondaryAchievementPercentage'] ?? 0,
                // Secondary Quarterly
                ($month % 3 === 1) ? ($quarterlyData['secTarget'] ?? 0) : 0,
                ($month % 3 === 1) ? ($quarterlyData['secondaryAchievement'] ?? 0) : 0,
                ($month % 3 === 1) ? ($quarterlyData['secondaryAchievementPercentage'] ?? 0) : 0,
                // Secondary Yearly
                ($month === 1) ? ($yearlyData['secTarget'] ?? 0) : 0,
                ($month === 1) ? ($yearlyData['secondaryAchievement'] ?? 0) : 0,
                ($month === 1) ? ($yearlyData['secondaryAchievementPercentage'] ?? 0) : 0
            ];

            $rows[] = $row;
        }

        return collect($rows);
    }

    private function calculatePercentage($achieved, $target)
    {
        if ($target <= 0) {
            return '0.0';
        }
        return number_format(($achieved / $target) * 100, 1);
    }

    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        $lastColumn = 'S';

        // Default style for all cells
        $sheet->getStyle("A1:{$lastColumn}{$lastRow}")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN
                ]
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER
            ]
        ]);

        // Title row
        $sheet->mergeCells("A1:{$lastColumn}1");
        $sheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 14],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER
            ]
        ]);

        // Employee details styling
        $sheet->getStyle('A2:C2')->applyFromArray([
            'font' => ['bold' => true],
            // 'alignment' => [
            //     'horizontal' => Alignment::HORIZONTAL_CENTER
            // ]
        ]);

        // Section headers (Primary/Secondary)
        $sheet->mergeCells('B5:J5');
        $sheet->mergeCells('K5:S5');
        $sheet->getStyle('A5:S7')->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER
            ]
        ]);

        // Headers styling
        $sheet->getStyle('A6:S7')->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER
            ]
        ]);

        // Merge header cells
        $headerMerges = [
            'A4:S4',//merge 4th row
            'B6:D6', 'E6:G6', 'H6:J6',  // Primary section
            'K6:M6', 'N6:P6', 'Q6:S6',   // Secondary section
            'E8:E10','E11:E13','E14:E16','E17:E19', // Primary Quarterly Target merge
            'F8:F10','F11:F13','F14:F16','F17:F19',// Primary Quarterly Achievement merge
            'G8:G10','G11:G13','G14:G16','G17:G19',// Primary Quarterly % merge
            'H8:H19',// Primary Yearly Target merge
            'I8:I19',// Primary Yearly Achievement merge
            'J8:J19',// Primary Yearly % merge

            'N8:N10','N11:N13','N14:N16','N17:N19', // Secondary Quarterly Target merge
            'O8:O10','O11:O13','O14:O16','O17:O19',// Secondary Quarterly Achievement merge
            'P8:P10','P11:P13','P14:P16','P17:P19',// Secondary Quarterly % merge
            'Q8:Q19',// Secondary Yearly Target merge
            'R8:R19',// Secondary Yearly Achievement merge
            'S8:S19',// Secondary Yearly % merge
        ];
        foreach ($headerMerges as $range) {
            $sheet->mergeCells($range);
        }

        // Number formatting for target and achievement columns
        $numberColumns = ['B', 'C', 'E', 'F', 'H', 'I', 'K', 'L', 'N', 'O', 'Q', 'R'];
        foreach ($numberColumns as $col) {
            $sheet->getStyle("{$col}8:{$col}{$lastRow}")
                ->getNumberFormat()
                ->setFormatCode('#,##0');
        }

        // Percentage formatting
        $percentageColumns = ['D', 'G', 'J', 'M', 'P', 'S'];
        foreach ($percentageColumns as $col) {
            $sheet->getStyle("{$col}8:{$col}{$lastRow}")
                ->getNumberFormat()
                ->setFormatCode('0.0"%"');
        }

        return $sheet;
    }

    public function startCell(): string
    {
        return 'A1';
    }
}