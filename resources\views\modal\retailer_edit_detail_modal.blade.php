<style>
    .col-md-6,
    .col-md-12 {
        float: left;
    }
</style>
<div class="modal fade" id="retailer_view_details_modal" aria-labelledby="SsviewDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="margin: 30px 200px;">
        <div class="modal-content">
            <div class="modal-header">
                <div class="container">
                    <div class="row align-items-center" style="margin:0px 0px -6px -15px;">
                        <ul class="nav nav-tabs nav-tabs-line" role="tablist">
                            <li class="nav-item" role="presentation"><a class="nav-link active" data-toggle="tab"
                                    href="#editSS" aria-controls="editSS" role="tab">Edit Retailer</a></li>
                            <li class="nav-item" role="presentation"><a class="nav-link" data-toggle="tab"
                                    href="#editPin" aria-controls="editPin" role="tab">Edit Pin</a></li>
                            <li class="nav-item" role="presentation"><a class="nav-link" data-toggle="tab"
                                    href="#userInfo" aria-controls="userInfo" role="tab">View User Info</a></li>
                            {{-- <li class="nav-item" role="presentation"><a class="nav-link" data-toggle="tab"
                                    href="#ss_list_order" aria-controls="orders" role="tab">Orders</a></li>
                            <li class="nav-item" role="presentation"><a class="nav-link" data-toggle="tab"
                                    href="#paymentDetails" aria-controls="paymentDetails" role="tab">Payment
                                    Details</a></li> --}}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="tab-content pt-5">
                    <div class="tab-pane active" id="editSS">
                        <div class="container">
                            <form id="edit_retailer_form" class="add_form">
                                @csrf
                                <div class="col-md-12">
                                    <div class="col-sm-12 col-md-6">
                                        <input type="text" name="cm_code" id="cm_code" readonly hidden>
                                        {{-- <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Code </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control " name="cm_code"
                                                    id="cm_code" placeholder="Enter Code"
                                                    oninput="this.value = this.value.toUpperCase()" readonly>
                                                <div class="invalid-feedback">
                                                    <!-- Error message will be displayed here -->
                                                </div>
                                            </div>
                                        </div> --}}
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Mobile No <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control" name="cm_mobile"
                                                    id="cm_mobile" placeholder="Enter Mobile Number"
                                                    onkeypress="return /[0-9]/i.test(event.key)">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">State <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_state_name chosen-select form-control"
                                                    id="cst_edit_state_name" name="state_name">
                                                    <option value=""></option>
                                                    @foreach ($state_list as $state)
                                                        <option value="{{ $state->state_id }}">{{ $state->state_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">District <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_district_name chosen-select form-control"
                                                    id="cst_edit_district_name" name="district_name">
                                                    <option value=""></option>
                                                </select>
                                            </div>

                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Town <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_town_name chosen-select form-control"
                                                    id="cst_edit_town_name" name="cm_town_id">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Assign Beat <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_beat_assign chosen-select form-control"
                                                    id="beat_assign" name="beat_assign">
                                                    @foreach ($beat_list as $beat)
                                                        <option value="{{ $beat->beat_code }}">{{ $beat->beat_name }}
                                                            ({{ $beat->beat_code }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Grade Type <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="gradeType chosen-select form-control" id="grade_type"
                                                    name="grade_type">
                                                    <option value=""></option>
                                                    @foreach ($gradeType as $grade)
                                                        <option value="{{ $grade->gm_id }}">{{ $grade->gm_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Outstanding Amount </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control "
                                                    name="cm_outstanding_amount" id="cm_outstanding_amount"
                                                    value="0">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Gst Number </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control" name="cm_gst"
                                                    id="cm_gst" placeholder="Enter Gst Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Pan Number </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control " name="cm_pan"
                                                    id="cm_pan" placeholder="Enter Pan Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label" for="occupation">Birth
                                                Date </label>
                                            <input type="date" name="birth_date" id="birth_date"
                                                class="form-control col-md-8 col-xs-12"
                                                style="background-color:white !important;">
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label"
                                                for="occupation">Anniversary Date<span
                                                    class="required"></span></label>
                                            <input type="date" name="anni_date" id="anni_date"
                                                class="form-control col-md-8 col-xs-12"
                                                style="background-color:white !important;">
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Location </label>
                                            <div style="padding:0;position: sticky;" class="col-sm-4 col-md-4">
                                                <input type="text" class="form-control" name="latitude"
                                                    id="latitude" placeholder="Latitude">
                                            </div>
                                            <div style="padding:0;position: sticky;" class="col-sm-4 col-md-4">
                                                <input type="text" class="form-control" name="longitude"
                                                    id="longitude" placeholder="Longitude">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6">
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Mobile 2 </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control" name="cm_mobile2"
                                                    id="second_mobile" placeholder="Enter Second Mobile Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Name <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control" name="cm_name"
                                                    id="cm_name" placeholder="Enter Name">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Email </label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="email" class="form-control " name="cm_email"
                                                    id="cm_email" placeholder="Enter Email"
                                                    oninput="this.value = this.value.toLowerCase()">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Contact Person <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control " name="cm_contact_person"
                                                    id="cm_contact_person" placeholder="Enter Contact Person Name">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Market Type <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_cm_market_type chosen-select form-control"
                                                    id="cm_market_type" name="cm_market_type">
                                                    <option value=""></option>
                                                    @foreach ($marketType as $marketType)
                                                        <option value="{{ $marketType->mtm_id }}">
                                                            {{ $marketType->mtm_type }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-4 col-form-label">Pincode <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-md-8">
                                                <input type="text" class="form-control " name="cm_pincode"
                                                    id="cm_pincode" placeholder="Enter Pincode" minlength="6"
                                                    maxlength="6"
                                                    onkeypress="return /^[0-9]{1,6}$/i.test(event.key)">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Category <span
                                                    class="required">*</span></label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="custCategory chosen-select form-control"
                                                    id="cust_category" name="cust_category">
                                                    <option value=""></option>
                                                    @foreach ($customerCategory as $category)
                                                        <option value="{{ $category->ccm_id }}">
                                                            {{ $category->ccm_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Distributor Or Dealer
                                            </label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_parent_ss form-control" name="parent_ss"
                                                    id="parent_ss">
                                                    <option value=""></option>
                                                    @foreach ($ss_list as $ss)
                                                        <option value="{{ $ss->cm_code }}">{{ $ss->cm_name }}
                                                            ({{ $ss->cm_code }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        {{-- <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Asign To </label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_assign_to form-control" name="assign_to[]"
                                                    id="assign_to" multiple="">
                                                    @foreach ($emp_list as $emp)
                                                        <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                                            ({{ $emp->emp_code }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div> --}}
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Area <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control" name="cm_area"
                                                    id="cm_area" placeholder="Enter Area">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Address <span
                                                    class="required">*</span></label>
                                            <div style="padding:0;" class="col-sm-8 col-md-8">
                                                <textarea class="form-control" id="cm_address" name="cm_address" placeholder="Enter Address"></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">{{ __('Create For') }}
                                            </label>
                                            <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                                <select class="select2_created_emp_code chosen-select form-control"
                                                    id="created_emp_code" name="created_emp_code">
                                                    <option value=""></option>
                                                    @foreach ($emp_list as $emp)
                                                        <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Status</label>
                                            <div style="padding:0;" class="col-sm-8 col-md-8">
                                                <select class="form-control" name="cm_status" id="cm_status">
                                                    <option value="1">Active</option>
                                                    <option value="0">Inactive</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Images </label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <div class="input-group input-group-file product_image_btn"
                                                    data-plugin="inputGroupFile">
                                                    {{-- <input type="text" id="product_image" name="product_image"
                                                        class="form-control" placeholder="Product Image"> --}}
                                                    <span class="input-group-append">
                                                        <span class="btn btn-primary btn-file">
                                                            <i class="icon md-upload" aria-hidden="true"></i>
                                                            <input type="file" id="retailer_images"
                                                                class="form-control" name="retailer_images[]"
                                                                accept="image/*" multiple>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group row" style="margin-left: 27px;">
                                            {{-- <label class="col-sm-4 col-md-4 col-form-label">Preview Image</label> --}}
                                            <div class="col-sm-12 col-md-12" id="image_preview">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="clear-both modal-footer">
                                    <button type="submit" class="btn btn-primary btn-sm waves-effect waves-classic"
                                        onclick="updateCustomerRetailer()">Save</button>
                                    <button data-dismiss="modal"
                                        class="btn btn-default waves-effect btn-sm waves-classic">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="tab-pane" id="editPin" role="tabpanel">
                        <div class="container">
                            <form id="retailer_edit_pin_form" class="add_form">
                                @csrf
                                <div class="col-md-6">
                                    <div class="form-group row">
                                        <input type="text" name="epin_cm_id" id="epin_cm_id" readonly hidden>
                                        <label class="col-md-4 col-form-label">Enter New Pin<span
                                                class="required">*</span></label>
                                        <div style="padding:0;" class="col-md-8">
                                            <input type="text" class="form-control form-control-sm"
                                                name="cm_login_pin" id="cm_login_pin">
                                        </div>
                                    </div>
                                </div>
                                <div class="clear-both modal-footer">
                                    <button type="submit" class="btn btn-primary btn-sm waves-effect waves-classic"
                                        onclick="updateCustomerRetailerPin()">Change
                                        Pin</button>
                                    <button data-dismiss="modal"
                                        class="btn btn-default waves-effect btn-sm waves-classic">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="tab-pane" id="userInfo" role="tabpanel">
                        <div class="container">
                            <input type="text" name="uinfo_cm_id" id="uinfo_cm_id" readonly hidden>
                            <form class="user_info_form">
                                <div class="col-md-12 P-4">
                                    <div class="col-sm-12 col-md-6">
                                        <div style="border: 1px solid #eee;padding: 0 25px;">
                                            <div class="panel-heading">
                                                <h3 class="panel-title">Personal Information</h3>
                                                <h4 class="panel-title" id="cst_name"></h4>
                                            </div>
                                            <ul class="list-unstyled">
                                                <li><i class="fa fa-phone"></i>&nbsp; Phone :
                                                    <a href="tel:+" id="cst_mobile"></a>
                                                </li><br>
                                                <li><i class="fa fa-envelope"></i>&nbsp; Email :
                                                    <a href="mailto: " id="cst_email"> </a>
                                                </li><br>
                                                <li><i class="fa fa-lock"></i>&nbsp; Code :
                                                    <span id="cst_code"></span>
                                                </li><br>
                                                <li><i class="fa fa-lock"></i>&nbsp; Pin :
                                                    <span id="cst_pin"></span>
                                                </li><br>
                                                <li><i class="fa fa-tag"></i>&nbsp; GST Number :
                                                    <span id="cst_gst_code"></span>
                                                </li><br>
                                                <li><i class="fa fa-tag"></i>&nbsp; Pan Number :
                                                    <span id="cst_pan_code"></span>
                                                </li><br>
                                                <li><i class="fa fa-user"></i>&nbsp; Contact Person :
                                                    <span id="cst_cont_pers"></span>
                                                </li><br>
                                                <li><i class="fa fa-money"></i>&nbsp; Outstanding Amount :
                                                    <span id="cst_outst_amo"></span>
                                                </li><br>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6">
                                        <div style="border: 1px solid #eee;padding: 0 25px;">
                                            <div class="panel-heading">
                                                <h4 class="panel-title">Employee's Relationship Info</h4>
                                            </div>
                                            <ul class="list-unstyled">
                                                <li><i class="fa fa-user"> </i>&nbsp; Assign to :
                                                    <br>
                                                    <span id="cst_assign_emp_name" style="margin-top: 2px;"></span>
                                                </li><br>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="clear-both modal-footer">
                                    <button data-dismiss="modal"
                                        class="btn btn-default waves-effect btn-sm waves-classic">Close</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="tab-pane" id="ss_list_order" role="tabpanel">
                        <div class="container">
                            <form class="add_form">
                                <input type="text" name="uorder_cm_id" id="uorder_cm_id" readonly>
                                <div class="modal-header">
                                    <h4 class="modal-title">Order List</h4>
                                    <div class="panel-actions" style="right:0px !important;top:25px !important;">
                                        <div class="form-group mr-5" style="float: left;">
                                            <div class="input-group input-group-icon">
                                                <input type="text" class="form-control form-control-sm"
                                                    name="order_date" id="order_date"
                                                    placeholder="01/01/2020 - 31/01/2020" style="min-width: 185px;" />
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="height: auto;">
                                                        <i class="icon fa-calendar" aria-hidden="true"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <button
                                            class="btn btn-primary btn-sm waves-effect waves-classic confirm_all"><!-- <i class="fa fa-check"></i> -->
                                            Confirm</button>
                                        <button
                                            class="btn btn-primary btn-sm waves-effect waves-classic cancel_all"><!-- <i class="fa fa-times"></i> -->
                                            Cancel</button>
                                        <button
                                            class="btn btn-primary btn-sm waves-effect waves-classic delivered_all"><!-- <i class="fa fa-truck"></i> -->
                                            Delivered </button>
                                    </div>
                                </div>
                                <div class="modal-body">
                                    <div id="ss_orders_list_table"> </div>
                                </div>
                                <div class="clear-both modal-footer">
                                    <button type="button" class="btn btn-default waves-effect btn-sm waves-classic"
                                        data-dismiss="modal">Close</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="tab-pane" id="paymentDetails" role="tabpanel">
                        <div class="container">
                            <input type="text" name="upayment_cm_id" id="upayment_cm_id" readonly>
                            <form class="add_form" method="post">
                                <div class="modal-header">
                                    <h4 class="modal-title">Payment Details</h4>
                                    <div class="panel-actions" style="right:70px !important;top:25px !important;">
                                        <div class="form-group" style="float: left;">
                                            <div class="input-group input-group-icon">
                                                <input type="text" class="form-control form-control-sm"
                                                    name="payment_date" id="payment_date"
                                                    placeholder="01/01/2020 - 31/01/2020" style="min-width: 185px;" />
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="height: auto;">
                                                        <i class="icon fa-calendar" aria-hidden="true"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-body">
                                    <div id="ss_payment_collection_list_table"> </div>
                                </div>
                                <div class="clear-both modal-footer">
                                    <button type="button" class="btn btn-default waves-effect btn-sm waves-classic"
                                        data-dismiss="modal">Close</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- End Example Tabs Line -->
            </div>
        </div>
    </div>
</div>

@include('scripts.globleValidationConfig')
<script type="text/javascript">
    $(document).ready(function() {
        $('#retailer_images').on('change', function() {
            var files = $(this).get(0).files;
            $('#image_preview').empty(); // Clear the preview area

            for (var i = 0; i < files.length; i++) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#image_preview').append('<img src="' + e.target.result +
                        '" width="100" height="100" style="margin: 10px;">');
                }

                reader.readAsDataURL(files[i]);
            }
        });
    });

    $(document).ready(function() {
        $('#edit_retailer_form').formValidation({
            ...validationConf,
            fields: {
                cm_code: {
                    validators: {
                        ...validationConf.fields.cm_code.validators,
                    }
                },
                cm_name: {
                    validators: {
                        ...validationConf.fields.cm_name.validators,
                    }
                },
                cm_email: {
                    validators: {
                        ...validationConf.fields.cm_email.validators,
                    }
                },
                cm_mobile: {
                    validators: {
                        ...validationConf.fields.cm_mobile.validators,
                    }
                },
                cm_mobile2: {
                    validators: {
                        ...validationConf.fields.cm_mobile2.validators,
                    }
                },
                cm_contact_person: {
                    validators: {
                        ...validationConf.fields.cm_contact_person.validators,
                    }
                },
                cm_address: {
                    validators: {
                        ...validationConf.fields.cm_address.validators,
                    }
                },
                state_name: {
                    validators: {
                        ...validationConf.fields.state_name.validators,
                    }
                },
                district_name: {
                    validators: {
                        ...validationConf.fields.district_name.validators,
                    }
                },
                cm_town_id: {
                    validators: {
                        ...validationConf.fields.cm_town_id.validators,
                    }
                },
                beat_assign: {
                    validators: {
                        ...validationConf.fields.beat_assign.validators,
                    }
                },
                grade_type: {
                    validators: {
                        ...validationConf.fields.grade_type.validators,
                    }
                },
                cm_pincode: {
                    validators: {
                        ...validationConf.fields.cm_pincode.validators,
                    }
                },
                cust_category: {
                    validators: {
                        ...validationConf.fields.cust_category.validators,
                    }
                },
                cm_market_type: {
                    validators: {
                        ...validationConf.fields.cm_market_type.validators,
                    }
                },
                cm_outstanding_amount: {
                    validators: {
                        ...validationConf.fields.cm_outstanding_amount.validators,
                    }
                },
                cm_area: {
                    validators: {
                        ...validationConf.fields.cm_area.validators,
                    }
                },
            },
        });
        $('#edit_retailer_form').on('keypress', function(e) {
            if (e.which == 13) {
                e.preventDefault();
                updateCustomerRetailer();
            }
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#retailer_edit_pin_form').formValidation({
            framework: "bootstrap4",
            button: {
                disabled: 'disabled'
            },
            icon: null,
            fields: {
                cm_login_pin: {
                    validators: {
                        notEmpty: {
                            message: 'Pin is required'
                        }
                    }
                },
            },
            err: {
                clazz: 'invalid-feedback'
            },
            control: {
                // The CSS class for valid control
                valid: 'is-valid',
                // The CSS class for invalid control
                invalid: 'is-invalid'
            },
            row: {
                invalid: 'has-danger'
            }
        });
        $('#retailer_edit_pin_form').on('keypress', function(e) {
            if (e.which == 13) {
                e.preventDefault();
                updateCustomerRetailerPin();
            }
        });
    });
</script>

<script>
    $(".select2_assign_to").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select Asign To",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_parent_ss").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select Distributor Or Dealer",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_cm_market_type").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select Market Type",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_town_name").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select Town",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_district_name").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select District",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_state_name").select2({
        //   maximumSelectionLength: 4,
        placeholder: "Select State",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_beat_assign").select2({
        // maximumSelectionLength: 4,
        placeholder: "Select Beat",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".gradeType").select2({
        // maximumSelectionLength: 4,
        placeholder: "Select Grade",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".custCategory").select2({
        // maximumSelectionLength: 4,
        placeholder: "Select Categoty",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
    $(".select2_created_emp_code").select2({
        // maximumSelectionLength: 4,
        placeholder: "Select Create For",
        dropdownParent: $('#retailer_view_details_modal'),
        allowClear: true
    });
</script>

{{-- append district and town data select on state --}}
<script>
    // get district data on state change
    $(document).ready(function() {
        $('#cst_edit_state_name').change(function() {
            var stateId = $(this).val();
            if (stateId) {
                $.ajax({
                    url: "{{ route('get_district_by_state_id') }}",
                    method: 'GET',
                    data: {
                        state_id: stateId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#cst_edit_district_name').empty();
                        var options = '<option value="">Select District</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#cst_edit_district_name').html(options);
                    }
                });
            } else {
                $('#cst_edit_district_name').empty();
            }
        });
    });

    // get town data on district change
    $(document).ready(function() {
        $('#cst_edit_district_name').change(function() {
            var districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: "{{ route('get_town_by_dist_id') }}",
                    method: 'GET',
                    data: {
                        district_id: districtId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#cst_edit_town_name').empty();
                        var options = '<option value="">Select Town</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#cst_edit_town_name').html(options);
                    }
                });
            } else {
                $('#cst_edit_town_name').empty();
            }
        });
    });
</script>
