<div class="page">
    <div class="header-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12" id="cols">
                    @include('products.products_header')
                </div>
            </div>
        </div>
    </div>
    <div class="page-content container-fluid table_content_page" id="addformContainer">
        @include('components.success_error_message')
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('Add Product') }}</h3>
            </div>
            <div class="panel-body container-fluid" style="padding:5px 0px 0px 0px;">
                <form id="add_product_form" class="ajax-form add_form" method="post"
                    action="{{ route('product.store') }}" enctype="multipart/form-data">
                    @csrf
                    <div class="col-md-12">
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Brand') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_product_brand form-control " name="product_brand"
                                        id="product_brand">
                                        <option></option>
                                        @foreach ($brandName as $brand)
                                            <option value="{{ $brand->brand_id }}">
                                                {{ $brand->brand_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ 'Product Name' }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="product_name" id="product_name"
                                        placeholder="{{ __('Enter Product Name') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Unit Size') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="unit_size" id="unit_size"
                                        placeholder="Enter Unit Size">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Inner Case Size') }} <span
                                        class="required">*</span>({{ __('Bunch') }})</label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control" name="inner_case_size"
                                        id="inner_case_size" placeholder="{{ __('Enter Inner Case Size') }}"
                                        value="0">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product MRP') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="product_mrp" id="product_mrp"
                                        placeholder="{{ __('Enter Mrp') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Barcode') }} </label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="barcode" id="barcode"
                                        placeholder="{{ __('Enter Product Code') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('HSN/SAC Code') }} </label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control" name="product_hsn_sac_code"
                                        minlength="4" maxlength="8" id="product_hsn_sac_code"
                                        placeholder="{{ __('Enter Hsn/Sac Code') }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Type') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_product_type form-control " name="product_type"
                                        id="pm_pt_id">
                                        <option></option>

                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Uom') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_product_for_use form-control" name="uom"
                                        id="uom">
                                        <option></option>
                                        @foreach ($uomName as $uom)
                                            <option value="{{ $uom->uom_id }}">
                                                {{ $uom->uom_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Outer Case Size') }} <span
                                        class="required">*</span>({{ __('Box') }})</label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="outer_case_size"
                                        id="outer_case_size" placeholder="Enter Outer Case Size" value="0">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product GST') }} <span
                                        class="required">*</span></label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="product_gst" id="product_gst"
                                        placeholder="{{ __('Enter Product Gst') }}" value="0">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Weight (Kg)') }} </label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control " name="weight" id="weight"
                                        placeholder="{{ __('Product Weight in Kg') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Image') }} </label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <div class="input-group input-group-file product_image_btn"
                                        data-plugin="inputGroupFile">
                                        {{-- <input type="text" id="product_image" name="product_image"
                                            class="form-control" placeholder="Product Image"> --}}
                                        <span class="input-group-append">
                                            <span class="btn btn-primary btn-file">
                                                <i class="icon md-upload" aria-hidden="true"></i>
                                                <input type="file" id="product_images" class="form-control"
                                                    name="product_images[]" accept="image/*" multiple>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row" style="margin-left: 27px;">
                                {{-- <label class="col-sm-4 col-md-4 col-form-label">Preview Image</label> --}}
                                <div class="col-sm-12 col-md-12" id="image_preview">

                                </div>
                            </div>

                        </div>
                    </div>
                    @php
                        $saveLabel = __('Save');
                    @endphp
                    @include('components.button', [
                        'label' => $saveLabel,
                        'route' => route('product.index'),
                    ])
                </form>
            </div>

        </div>
    </div>
</div>

<script type="text/javascript">
    // product images preview
    $(document).ready(function() {
        $('#product_images').on('change', function() {
            var files = $(this).get(0).files;
            $('#image_preview').empty(); // Clear the preview area

            for (var i = 0; i < files.length; i++) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#image_preview').append('<img src="' + e.target.result +
                        '" width="100" height="100" style="margin: 10px;">');
                }

                reader.readAsDataURL(files[i]);
            }
        });
    });
    mark_sidebar_active("products");
    //check for form validation
    $(document).ready(function() {
        $('#add_product_form').formValidation({
            framework: "bootstrap4",
            button: {
                disabled: 'disabled'
            },
            icon: null,
            fields: {
                product_brand: {
                    validators: {
                        notEmpty: {
                            message: 'Product Brand is required'
                        }
                    }
                },
                product_name: {
                    validators: {
                        notEmpty: {
                            message: 'Product Name is required'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9\s()\-/&]+$/, // Allow letters, digits, spaces, hyphens, slashes, parentheses, and ampersands
                            message: 'Only letters, digits, spaces, hyphens, slashes, parentheses, and ampersands are allowed'
                        }
                    }
                },
                unit_size: {
                    validators: {
                        notEmpty: {
                            message: 'Unit Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                inner_case_size: {
                    validators: {
                        notEmpty: {
                            message: 'Inner Case Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_mrp: {
                    validators: {
                        notEmpty: {
                            message: 'Product MRP is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_type: {
                    validators: {
                        notEmpty: {
                            message: 'Product Type is required'
                        }
                    }
                },
                uom: {
                    validators: {
                        notEmpty: {
                            message: 'Uom is required'
                        }
                    }
                },
                outer_case_size: {
                    validators: {
                        notEmpty: {
                            message: 'Outer Case Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_gst: {
                    validators: {
                        notEmpty: {
                            message: 'Product GST is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
            },
            err: {
                clazz: 'invalid-feedback'
            },
            control: {
                // The CSS class for valid control
                valid: 'is-valid',
                // The CSS class for invalid control
                invalid: 'is-invalid'
            },
            row: {
                invalid: 'has-danger'
            }
        });
    });
</script>

<script>
    $(".select2_product_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Product Type') }}",
        allowClear: true
    });
    $(".select2_product_brand").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Product Brand') }}",
        allowClear: true
    });
    $(".select2_product_for_use").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Uom') }}",
        allowClear: true
    });
    $(".select2_product_tax_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Tax Type') }}",
        allowClear: true
    });
    $(".select2_product_market_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Unit Type') }}",
        allowClear: true
    });
</script>
<script>
    // get district data on state change
    $(document).ready(function() {
        $('#product_brand').change(function() {
            var brandId = $(this).val();
            if (brandId) {
                $.ajax({
                    url: "{{ route('getTypeByBrandId') }}",
                    method: 'GET',
                    data: {
                        brand_id: brandId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#pm_pt_id').empty();
                        var options = '<option value="">Select Type</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#pm_pt_id').html(options);
                    }
                });
            } else {
                $('#pm_pt_id').empty();
            }
        });
    });
</script>
