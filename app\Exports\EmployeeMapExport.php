<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class EmployeeMapExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths
{
    protected $employee;
    protected $customers;
    protected $beatData;
    protected $beatRetailers;
    protected $rowCount = 0;
    protected $sectionStarts = [];
    protected $headerRows = [];

    public function __construct($employee, $customers, $beatData, $beatRetailers)
    {
        $this->employee = $employee;
        $this->customers = $customers;
        $this->beatData = $beatData;
        $this->beatRetailers = $beatRetailers;
    }

    public function collection()
    {
        $data = [];
        $this->rowCount = 1;

        // Report Header
        $data[] = ['Employee Map Report - ' . $this->employee->emp_code];
        $data[] = [''];
        $this->sectionStarts[] = $this->rowCount;
        $this->rowCount += 2;

        // Employee Basic Details Header
        $data[] = [
            'Code',
            'Name',
            'Mobile',
            'Email',
            'Status',
            'Designation',
        ];
        $this->headerRows[] = $this->rowCount; // Track header row
        $data[] = [
            $this->employee->emp_code,
            $this->employee->emp_name,
            $this->employee->emp_mobile,
            $this->employee->emp_email,
            $this->employee->emp_status == 1 ? 'Active' : 'Inactive',
            $this->employee->dm_name,
        ];
        $this->sectionStarts[] = $this->rowCount;
        $this->rowCount += 2;

        // Employee Additional Details Header
        $data[] = [
            'Market Type',
            'State',
            'District',
            'Reporting To',
            'Team Members'
        ];
        $this->headerRows[] = $this->rowCount; // Track header row
        $data[] = [
            $this->employee->mtm_type,
            $this->employee->stateName,
            $this->employee->distName,
            (isset($this->employee->repoEmpName) && isset($this->employee->repoEmpCode)
                ? $this->employee->repoEmpName . ' (' . $this->employee->repoEmpCode . ')' : 'N/A'),
            (isset($this->employee->team_members) && !empty($this->employee->team_members)
                ? $this->employee->team_members : 'N/A')
        ];
        $this->sectionStarts[] = $this->rowCount;
        $this->rowCount += 2;

        $data[] = [''];
        $this->rowCount++;

        // Customer Data Sections
        $customerTypes = [
            'SS' => [],
            'Distributor' => [],
            'Dealer' => [],
        ];

        foreach ($this->customers as $customer) {
            $type = $this->mapCustomerType($customer->cm_type);
            if (array_key_exists($type, $customerTypes)) {
                $customerTypes[$type][] = $customer;
            }
        }

        foreach ($customerTypes as $type => $customers) {
            if (count($customers) > 0) {
                $data[] = [$type . ' Data:'];
                $this->sectionStarts[] = $this->rowCount;
                $data[] = ['Code', 'Name', 'Area'];
                $this->headerRows[] = $this->rowCount + 1; // Track header row
                $this->rowCount += 2;

                foreach ($customers as $customer) {
                    $data[] = [
                        $customer->cm_code,
                        $customer->cm_name,
                        $customer->cm_area ?? 'N/A',
                    ];
                    $this->rowCount++;
                }

                $data[] = [''];
                $this->rowCount++;
            }
        }

        // Beat Information Header
        $data[] = ['Beat Information:'];
        $this->sectionStarts[] = $this->rowCount;
        $data[] = ['Beat Code', 'Beat Name', 'Town Name'];
        $this->headerRows[] = $this->rowCount + 1; // Track header row
        $this->rowCount += 2;

        foreach ($this->beatData as $beat) {
            $data[] = [
                $beat->beat_code,
                $beat->beat_name,
                $beat->town['town_name'] ?? 'N/A',
            ];
            $this->rowCount++;
        }

        // Beat Data Section with Retailers
        foreach ($this->beatData as $beat) {
            if (isset($this->beatRetailers[$beat->beat_code]) && count($this->beatRetailers[$beat->beat_code]) > 0) {
                $data[] = [''];
                $data[] = ['Retailers in Beat ' . $beat->beat_name . ' (' . $beat->beat_code . ') ' . ':'];
                $this->sectionStarts[] = $this->rowCount + 1;
                $data[] = ['Code', 'Name', 'Mobile', 'Area'];
                $this->headerRows[] = $this->rowCount + 2; // Track header row
                $this->rowCount += 3;

                foreach ($this->beatRetailers[$beat->beat_code] as $retailer) {
                    $data[] = [
                        $retailer->cm_code,
                        $retailer->cm_name,
                        $retailer->cm_mobile ?? 'N/A',
                        $retailer->cm_area ?? 'N/A',
                    ];
                    $this->rowCount++;
                }
            }

            $data[] = [''];
            $this->rowCount++;
        }

        return collect($data);
    }

    private function mapCustomerType($cmType)
    {
        $types = [
            1 => 'Company',
            2 => 'SS',
            3 => 'Distributor',
            4 => 'Dealer',
            5 => 'Retailer'
        ];

        return $types[$cmType] ?? 'Unknown';
    }

    public function headings(): array
    {
        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 30,
            'C' => 25,
            'D' => 20,
            'E' => 25,
            'F' => 20,
        ];
    }

    public function styles(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet)
    {
        $styles = [];

        // Default style for all cells
        $sheet->getStyle('A1:F' . $this->rowCount)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Style for section headers (section titles)
        foreach ($this->sectionStarts as $row) {
            $sheet->getStyle('A' . $row . ':F' . $row)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E0E0E0'],
                ],
                'borders' => [
                    'outline' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ]);
        }

        // Style for column headers
        foreach ($this->headerRows as $row) {
            $sheet->getStyle('A' . $row . ':F' . $row)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F0F0F0'],
                ],
            ]);
        }

        // Title style
        $sheet->getStyle('A1:F1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 14],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'C0C0C0'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        // Merge cells for the title
        $sheet->mergeCells('A1:E1');

        return $styles;
    }
}
