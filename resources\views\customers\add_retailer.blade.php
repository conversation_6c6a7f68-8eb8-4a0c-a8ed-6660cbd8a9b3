<div class="page">
    <div class="header-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12" id="cols">
                    @include('customers.customers_header')
                </div>
            </div>
        </div>
    </div>
    <div class="page-content container-fluid table_content_page" id="addformContainer">
        @include('components.success_error_message')
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('Add Retailer') }}</h3>
            </div>
            <div class="panel-body container-fluid" style="padding:5px 0px 0px 0px !important;">
                <form id="add_retailer_form" class="ajax-form add_form" method="post"
                    action="{{ route('save_customer', ['type' => 'retailer']) }}" enctype="multipart/form-data">
                    @csrf
                    <div class="col-md-12">
                        <div class="col-sm-12 col-md-6">
                            {{-- <div class="form-group row">
                                <label class="col-md-4 col-form-label">Code <span class="required">*</span></label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_code" id="cm_code"
                                        placeholder="RT0001" oninput="this.value = this.value.toUpperCase()">
                                    <div class="invalid-feedback">
                                        <!-- Error message will be displayed here -->
                                    </div>
                                </div>
                            </div> --}}
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Mobile No') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control" name="cm_mobile" id="cm_mobile"
                                        placeholder="Enter Mobile Number" maxlength="10"
                                        onkeypress="return /[0-9]/i.test(event.key)">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('State') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-7 col-md-7">
                                    <select class="select2_state_name chosen-select form-control" id="state_name"
                                        name="state_name">
                                        <option value=""></option>
                                        @foreach ($state_list as $state)
                                            <option value="{{ $state->state_id }}">{{ $state->state_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div style="padding:0;" class="col-sm-1 col-md-1">
                                    <a data-target="#add_customer_state_modal" data-toggle="modal"
                                        class="btn btn-primary"
                                        style="width: 100%;height: 32px;border-top-left-radius: 0;color: #fff;
                                border-bottom-left-radius: 0;"><i
                                            class="icon fa-plus" aria-hidden="true"
                                            style="margin:-2px 0px -3px -4px !important;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('District') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-7 col-md-7">
                                    <select class="select2_district_name chosen-select form-control" id="district_name"
                                        name="district_name">
                                        <option value=""></option>
                                    </select>
                                </div>
                                <div style="padding:0;" class="col-sm-1 col-md-1">
                                    <a data-target="#add_customer_district_modal" data-toggle="modal"
                                        class="btn btn-primary"
                                        style="width: 100%;height: 32px;border-top-left-radius: 0;color: #fff;
                                border-bottom-left-radius: 0;"><i
                                            class="icon fa-plus" aria-hidden="true"
                                            style="margin:-2px 0px -3px -4px !important;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Town') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-7 col-md-7">
                                    <select class="select2_town_name chosen-select form-control" id="town_name"
                                        name="cm_town_id">
                                        <option value=""></option>

                                    </select>
                                </div>
                                <div style="padding:0;" class="col-sm-1 col-md-1">
                                    <a data-target="#add_customer_town_modal" data-toggle="modal"
                                        class="btn btn-primary"
                                        style="width: 100%;height: 32px;border-top-left-radius: 0;color: #fff;
                                border-bottom-left-radius: 0;"><i
                                            class="icon fa-plus" aria-hidden="true"
                                            style="margin:-2px 0px -3px -4px !important;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Beat') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_beat_assign chosen-select form-control" id="beat_assign"
                                        name="beat_assign">
                                        <option value=""></option>
                                        @foreach ($beat_list as $beat)
                                            <option value="{{ $beat->beat_code }}">{{ $beat->beat_name }}
                                                ({{ $beat->beat_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Grade Type') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="gradeType chosen-select form-control" id="grade_type"
                                        name="grade_type">
                                        <option value=""></option>
                                        @foreach ($gradeType as $grade)
                                            <option value="{{ $grade->gm_id }}">{{ $grade->gm_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Outstanding Amount') }} </label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_outstanding_amount"
                                        id="cm_outstanding_amount" value="0">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Gst Number') }} </label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_gst" id="cm_gst"
                                        placeholder="{{ __('Enter GST Number') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Pan Number') }} </label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_pan" id="cm_pan"
                                        placeholder="{{ __('Enter Pan Number') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label"
                                    for="occupation">{{ __('Birth Date') }} </label>
                                <input type="date" name="birth_date" class="form-control col-md-8 col-xs-12"
                                    style="background-color:white !important;" placeholder="2019-12-12">
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label"
                                    for="occupation">{{ __('Anniversary Date') }}<span
                                        class="required"></span></label>
                                <input type="date" name="anni_date" class="form-control col-md-8 col-xs-12"
                                    style="background-color:white !important;" placeholder="2019-12-12">
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Location') }} </label>
                                {{-- <div style="padding:0;" class="col-sm-1 col-md-1">
                                    <a data-target="#add_location_modal" data-toggle="modal" class="btn btn-primary"
                                        style="width: 100%;height: 32px;border-top-right-radius: 0;color: #fff;
                                            border-bottom-right-radius: 0;"><i
                                            class="icon fa-plus" aria-hidden="true"
                                            style="margin:-2px 0px -3px -4px !important;"></i>
                                    </a>
                                </div> --}}
                                {{-- when add above plus sign then in below div add margin-left:10px --}}
                                <div style="padding:0;position: sticky;" class="col-sm-4 col-md-4">
                                    <input type="text" class="form-control" name="latitude" id="latitude"
                                        placeholder="{{ __('Latitude') }}">
                                </div>
                                <div style="padding:0;position: sticky;" class="col-sm-4 col-md-4">
                                    <input type="text" class="form-control" name="longitude" id="longitude"
                                        placeholder="{{ __('Longitude') }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Mobile 2') }} </label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control" name="cm_mobile2" id="second_mobile"
                                        placeholder="{{ __('Enter Second Mobile Number') }}" maxlength="10"
                                        onkeypress="return /[0-9]/i.test(event.key)">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Name') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control" name="cm_name" id="cm_name"
                                        placeholder="{{ __('Enter Name') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Email') }} </label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="email" class="form-control " name="cm_email" id="cm_email"
                                        placeholder="{{ __('Enter Email') }}"
                                        oninput="this.value = this.value.toLowerCase()">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Contact Person') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_contact_person"
                                        id="cm_contact_person" placeholder="{{ __('Enter Contact Person Name') }}">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Market Type') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_cm_market_type chosen-select form-control"
                                        id="cm_market_type" name="cm_market_type">
                                        <option value=""></option>
                                        @foreach ($marketType as $marketType)
                                            <option value="{{ $marketType->mtm_id }}">{{ $marketType->mtm_type }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ __('Pincode') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-md-8">
                                    <input type="text" class="form-control " name="cm_pincode" id="cm_pincode"
                                        placeholder="{{ __('Enter Pincode') }}" minlength="6" maxlength="6"
                                        onkeypress="return /^[0-9]{1,6}$/i.test(event.key)">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Category') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="custCategory chosen-select form-control" id="cust_category"
                                        name="cust_category">
                                        <option value=""></option>
                                        @foreach ($customerCategory as $category)
                                            <option value="{{ $category->ccm_id }}">{{ $category->ccm_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Distributor Or Dealer') }}
                                </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_parent_ss form-control" name="parent_ss" id="parent_ss">
                                        <option value=""></option>
                                        @foreach ($ss_list as $ss)
                                            <option value="{{ $ss->cm_code }}">{{ $ss->cm_name }}
                                                ({{ $ss->cm_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            {{-- <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Asign To') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_assign_to form-control" name="assign_to[]" id="assign_to"
                                        multiple="">
                                        @foreach ($emp_list as $emp)
                                            <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                                ({{ $emp->emp_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div> --}}
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Area') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control" name="cm_area" id="cm_area"
                                        placeholder="{{ __('Enter Area') }}">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Address') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <textarea class="form-control" id="cm_address" name="cm_address" placeholder="{{ __('Enter Address') }}"></textarea>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Create For') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_created_emp_code chosen-select form-control"
                                        id="created_emp_code" name="created_emp_code">
                                        <option value=""></option>
                                        @foreach ($emp_list as $emp)
                                            <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Images') }}</label>
                                <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                    <div class="input-group input-group-file product_image_btn"
                                        data-plugin="inputGroupFile">
                                        <span class="input-group-append">
                                            <span class="btn btn-primary btn-file">
                                                <i class="icon md-upload" aria-hidden="true"></i>
                                                <input type="file" id="retailer_images" class="form-control"
                                                    name="retailer_images[]" accept="image/*" multiple>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row" style="margin-left: 27px;">
                                {{-- <label class="col-sm-4 col-md-4 col-form-label">Preview Image</label> --}}
                                <div class="col-sm-12 col-md-12" id="image_preview">

                                </div>
                            </div>
                        </div>
                    </div>
                    @php
                        $saveLabel = __('Save');
                    @endphp
                    @include('components.button', [
                        'label' => $saveLabel,
                        'route' => route('customer_list', ['type' => 'retailer']),
                    ])
                </form>
            </div>
        </div>
    </div>
</div>

@include('scripts.globleValidationConfig')
<script type="text/javascript">
    // product images preview
    $(document).ready(function() {
        $('#retailer_images').on('change', function() {
            var files = $(this).get(0).files;
            $('#image_preview').empty(); // Clear the preview area

            for (var i = 0; i < files.length; i++) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#image_preview').append('<img src="' + e.target.result +
                        '" width="100" height="100" style="margin: 10px;">');
                }

                reader.readAsDataURL(files[i]);
            }
        });
    });

    mark_sidebar_active("customer");
    $(document).ready(function() {
        $('#add_retailer_form').formValidation({
            ...validationConf,
            fields: {
                cm_code: {
                    validators: {
                        ...validationConf.fields.cm_code.validators,
                    }
                },
                cm_name: {
                    validators: {
                        ...validationConf.fields.cm_name.validators,
                    }
                },
                cm_email: {
                    validators: {
                        ...validationConf.fields.cm_email.validators,
                    }
                },
                cm_mobile: {
                    validators: {
                        ...validationConf.fields.cm_mobile.validators,
                    }
                },
                cm_mobile2: {
                    validators: {
                        ...validationConf.fields.cm_mobile2.validators,
                    }
                },
                cm_contact_person: {
                    validators: {
                        ...validationConf.fields.cm_contact_person.validators,
                    }
                },
                cm_address: {
                    validators: {
                        ...validationConf.fields.cm_address.validators,
                    }
                },
                state_name: {
                    validators: {
                        ...validationConf.fields.state_name.validators,
                    }
                },
                district_name: {
                    validators: {
                        ...validationConf.fields.district_name.validators,
                    }
                },
                cm_town_id: {
                    validators: {
                        ...validationConf.fields.cm_town_id.validators,
                    }
                },
                beat_assign: {
                    validators: {
                        ...validationConf.fields.beat_assign.validators,
                    }
                },
                grade_type: {
                    validators: {
                        ...validationConf.fields.grade_type.validators,
                    }
                },
                cm_pincode: {
                    validators: {
                        ...validationConf.fields.cm_pincode.validators,
                    }
                },
                cust_category: {
                    validators: {
                        ...validationConf.fields.cust_category.validators,
                    }
                },
                cm_market_type: {
                    validators: {
                        ...validationConf.fields.cm_market_type.validators,
                    }
                },
                cm_outstanding_amount: {
                    validators: {
                        ...validationConf.fields.cm_outstanding_amount.validators,
                    }
                },
                cm_area: {
                    validators: {
                        ...validationConf.fields.cm_area.validators,
                    }
                },
            },
        });
    });
</script>
<script>
    $(".select2_state_name").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select State') }}",
        allowClear: true
    });

    $(".select2_district_name").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select District') }}",
        allowClear: true
    });
    $(".select2_town_name").select2({
        maximumSelectionLength: 4,
        placeholder: "{{ __('Select Town') }}",
        allowClear: true
    });
    $(".select2_parent_ss").select2({
        //   maximumSelectionLength: 4,
        placeholder: "{{ __('Select Distributor Or Dealer') }}",
        allowClear: true
    });
    $(".select2_assign_to").select2({
        //   maximumSelectionLength: 4,
        placeholder: "{{ __('Select Assign To') }}",
        allowClear: true
    });
    $(".select2_cm_market_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Market Type') }}",
        allowClear: true
    });
    $(".select2_created_emp_code").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Employee') }}",
        allowClear: true
    });
    $(".select2_beat_assign").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Beat') }}",
        allowClear: true
    });
    $(".gradeType").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Grade') }}",
        allowClear: true
    });
    $(".custCategory").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Categoty') }}",
        allowClear: true
    });
</script>

<script>
    // get district data on state change
    $(document).ready(function() {
        $('#state_name').change(function() {
            var stateId = $(this).val();
            if (stateId) {
                $.ajax({
                    url: "{{ route('get_district_by_state_id') }}",
                    method: 'GET',
                    data: {
                        state_id: stateId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#district_name').empty();
                        var options = '<option value="">Select District</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#district_name').html(options);
                    }
                });
            } else {
                $('#district_name').empty();
            }
        });
    });

    // get town data on district change
    $(document).ready(function() {
        $('#district_name').change(function() {
            var districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: "{{ route('get_town_by_dist_id') }}",
                    method: 'GET',
                    data: {
                        district_id: districtId
                    },
                    dataType: 'json',
                    success: function(data) {
                        $('#town_name').empty();
                        var options = '<option value="">Select Town</option>';
                        var sortedNames = Object.values(data.data).sort();
                        $.each(sortedNames, function(index, name) {
                            // Find the corresponding ID for the name
                            var id = Object.keys(data.data).find(key => data.data[
                                key] === name);
                            options += '<option value="' + id + '">' + name +
                                '</option>';
                        });
                        $('#town_name').html(options);
                    }
                });
            } else {
                $('#town_name').empty();
            }
        });
    });
</script>
