{"__meta": {"id": "X9f277ad9e8cd0a93b47b6f121560dfcc", "datetime": "2025-06-03 13:15:01", "utime": **********.870628, "method": "GET", "uri": "/sfm_v2_laravel/public/fetchRoutePlanApprNotifications", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.344433, "end": **********.870655, "duration": 0.5262219905853271, "duration_str": "526ms", "measures": [{"label": "Booting", "start": **********.344433, "relative_start": 0, "end": **********.76153, "relative_end": **********.76153, "duration": 0.4170968532562256, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761551, "relative_start": 0.4171178340911865, "end": **********.870657, "relative_end": 1.9073486328125e-06, "duration": 0.10910606384277344, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36296512, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET fetchRoutePlanApprNotifications", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@fetchRoutePlanApprNotifications", "namespace": null, "prefix": "", "where": [], "as": "fetchRoutePlanApprNotifications", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=457\" onclick=\"\">app/Http/Controllers/DashboardController.php:457-493</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00601, "accumulated_duration_str": "6.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.825558, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.832366, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 82.529}, {"sql": "select * from `beat_datewise_master` where `bdm_status` = 0 order by `created_at` desc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 464}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 460}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.848042, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:464", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 464}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=464", "ajax": false, "filename": "DashboardController.php", "line": "464"}, "connection": "gopi_new", "explain": null, "start_percent": 82.529, "width_percent": 17.471}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/fetchRoutePlanApprNotifications", "status_code": "<pre class=sf-dump id=sf-dump-359582780 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-359582780\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1661783426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1661783426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1015464130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1015464130\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6Ijg0VW9VSzFZQm9YdHVmbWdpTVN6U0E9PSIsInZhbHVlIjoiTVk3cTVxeFRrcXN0bXRSbHF2NGM4Yklyc202aHg1OHlyZUZORjU2UnVGSDd2cnUydDR0SUw1WG9RazRQVVlYZU9wTHFDcFNXMXRFSEtHOStDV1NIeVBaM203ZXk0NEhheUtlN3NuUXBqSGlOUmhqZmpEcGxra2ZYSnRyT0RWNnQiLCJtYWMiOiIxNmNiZWE0ZDExMTMyMGFhOTZkZmQ5ZjQwNzIyNDc1OWM0MDZkNTMyY2M0YzJjZTA3YTU4MDMxZjc4OWJhNTIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imd3ZHpDcTJOUEFJM3NWK0toNktrYnc9PSIsInZhbHVlIjoiOWkyNTFiOTV4K0RSazhUWlVvWURzNnd3L1NXQjhjQVFXSVNYRW01YmdScGpxcnJ3N0RFbGhhM0VzUmVzWC9aeU5mdlp4RkNpYVg3d1M2b3FEQkZ4ekZMVGxIWktPVU12NEpqZ2p1NWczK0FyZzYxL0ZOTmdYUG8vb0xzemp5NXQiLCJtYWMiOiI2NTQxNmFlMTU5NmQ1MGZmZjExYTYyNzM0ODRkNmNjMWVlYWI2OTIxZWQ5YWFmNTI2NjBmYTdkZmEzY2ZkNWUxIiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6Ijc0RjVOVEV5cFNoTFF1V0I1U29RM1E9PSIsInZhbHVlIjoiaVB2YVZXcVR2bTVXL3oxV0pmUjNjdEVnMCtIYnUvK2tJOTFCVzluenRMYTFPalJ1SWxlY0VEOWVtT1pTaFNLSGROK0RFWDN5R3Ivc2VjNVNrZWRham02dzlmMDJxN1h0bWRuMkkvUlJ6anc4Q3hxTzNzWlNzbHV0MlkwL1NOaWwiLCJtYWMiOiI1MWE5OTFlYjM0MTRiNzVjY2Q1MzIzNDZmYTA0MzRlMGJlZDE4YmZmMDc5NzhiNDk4NjRkZTkyZjIwZTkyNzJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1091691815 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:45:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6IkJEbzFGYWpVZUM5RFhDY2k3eTkvTVE9PSIsInZhbHVlIjoiN3ZoKzNzcVVMdS9MOEppUVVCODJQbTdzZkpCOHFtcUZWNVp4cnhQdkRXaTlsOU0yazZ2ZkxVOHdnWURRbm56Wno1bnp6NjErM1dBVHBienlOWmtDeER4Ni9QT1d2ZHFNMk5mOVl2MmxUTmV3VW5VMzBkZWRvbUpTMlVsdm1jTGgiLCJtYWMiOiJjM2ViNTVmZmY5NWU5MDNkZjU4ZDU1NWE1ZjMwYmEyZmMyYjJkMDY5MGE2OTNiMTY0ZGFiNzkyMmI0ZjA5MzRkIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:45:01 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IlV3NjhpY1NDaEtFbW9UY2dsZFp2alE9PSIsInZhbHVlIjoiakFQUWhBMktTRjFseTF4UEN3L1I0TnBkU1l5bVh6dmNqV0o3VFlDdzBURjBJek51cGFDL0sxZCtUYnhDQk0xSE8vNXRvOGkxYmpqVFdESWJ3V09hS1FFNU9aMzA3dGRTUnd3TmdqT3NtNGU2aTF6d3ZNTzBleDFkbmpRV09wKzciLCJtYWMiOiIxMDM5MjIwYzU2OGU2OWEzMjg1NDkwOTVhYjc1NDExYmFhYjNjMTY0M2QyMGE1MGM3ZjFlYmY5OThjOTY5YzM1IiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:45:01 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJEbzFGYWpVZUM5RFhDY2k3eTkvTVE9PSIsInZhbHVlIjoiN3ZoKzNzcVVMdS9MOEppUVVCODJQbTdzZkpCOHFtcUZWNVp4cnhQdkRXaTlsOU0yazZ2ZkxVOHdnWURRbm56Wno1bnp6NjErM1dBVHBienlOWmtDeER4Ni9QT1d2ZHFNMk5mOVl2MmxUTmV3VW5VMzBkZWRvbUpTMlVsdm1jTGgiLCJtYWMiOiJjM2ViNTVmZmY5NWU5MDNkZjU4ZDU1NWE1ZjMwYmEyZmMyYjJkMDY5MGE2OTNiMTY0ZGFiNzkyMmI0ZjA5MzRkIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:45:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IlV3NjhpY1NDaEtFbW9UY2dsZFp2alE9PSIsInZhbHVlIjoiakFQUWhBMktTRjFseTF4UEN3L1I0TnBkU1l5bVh6dmNqV0o3VFlDdzBURjBJek51cGFDL0sxZCtUYnhDQk0xSE8vNXRvOGkxYmpqVFdESWJ3V09hS1FFNU9aMzA3dGRTUnd3TmdqT3NtNGU2aTF6d3ZNTzBleDFkbmpRV09wKzciLCJtYWMiOiIxMDM5MjIwYzU2OGU2OWEzMjg1NDkwOTVhYjc1NDExYmFhYjNjMTY0M2QyMGE1MGM3ZjFlYmY5OThjOTY5YzM1IiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:45:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091691815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1666620645 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666620645\", {\"maxDepth\":0})</script>\n"}}