<?php
    // getConfigDetails helper function from app/Helper/customHelper.php
    $configDetails = getConfigDetails();
?>
<nav class="site-navbar navbar navbar-default navbar-fixed-top navbar-mega" role="navigation">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler hamburger hamburger-close navbar-toggler-left hided"
            data-toggle="menubar">
            <span class="sr-only">Toggle navigation</span>
            <span class="hamburger-bar"></span>
        </button>
        <button type="button" class="navbar-toggler collapsed" data-target="#site-navbar-collapse"
            data-toggle="collapse">
            <i class="icon md-more" aria-hidden="true"></i>
        </button>
    </div>

    <!--  <div class="navbar-container container-fluid"> -->
    <!-- Navbar Collapse -->
    <div class="collapse navbar-collapse navbar-collapse-toolbar" id="site-navbar-collapse">
        <!-- Navbar Toolbar -->
        <ul class="nav navbar-toolbar">
            <li class="nav-item hidden-float" id="toggleMenubar">
                <a class="nav-link" data-toggle="menubar" href="#" role="button">
                    <i class="icon hamburger hamburger-arrow-left">
                        <span class="sr-only">Toggle menubar</span>
                        <span class="hamburger-bar"></span>
                    </i>
                </a>
            </li>
            <li class="nav-item hidden-sm-down" id="toggleFullscreen">
                <a href="<?php echo e(route('dashboard')); ?>">
                    <div class="navbar-brand navbar-brand-center site-gridmenu-toggle">
                        <span><?php echo e($configDetails['compName']); ?></span>
                    </div>
                </a>
            </li>
        </ul>
        <!-- End Navbar Toolbar -->

        <!-- Navbar Toolbar Right -->
        <ul class="nav navbar-toolbar navbar-right navbar-toolbar-right">

            
            <?php if($configDetails['langShow'] == '1'): ?>
                <li class="nav-item dropdown" id="notification_dashboard">
                    <a class="nav-link" data-toggle="dropdown" title="Notifications" aria-expanded="false"
                        data-animation="scale-up" role="button">
                        <i class="fas fa-language" aria-hidden="true"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                        <select class="form-control changeLang" onchange="changeLanguage(this.value)">
                            <option value="en" <?php echo e(session()->get('locale') == 'en' ? 'selected' : ''); ?>>
                                <?php echo e(__('English')); ?></option>
                            <option value="gu" <?php echo e(session()->get('locale') == 'gu' ? 'selected' : ''); ?>>
                                <?php echo e(__('Gujarati')); ?>

                            </option>
                        </select>
                    </div>
                </li>
            <?php endif; ?>

            <?php if($configDetails['beatShow'] == '1'): ?>
                <li class="nav-item dropdown" id="notification_dashboard">
                    <a class="nav-link" data-toggle="dropdown" title="Beat Notifications" aria-expanded="false"
                        data-animation="scale-up" role="button" data-toggle="tooltip" title="View Beat Notifications">
                        <i class="icon wb-bell" aria-hidden="true"></i>
                        <span class="badge badge-pill badge-danger up" id="beatNotificationCount">0</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                        <div class="dropdown-menu-header">
                            <h5><?php echo e(__('Beat Notifications')); ?></h5>
                            <span class="badge badge-round badge-danger">New <span
                                    id="beatNewNotificationCount">0</span></span>
                        </div>

                        <div class="list-group" id="beatNotificationList">
                            <!-- Notification items will be dynamically added here -->
                        </div>
                    </div>
                </li>
            <?php endif; ?>

            <?php if($configDetails['routeShow'] == '1'): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" title="Beat Route Plan Notifications"
                        aria-expanded="false" data-animation="scale-up" role="button">
                        <i class="icon wb-bell" aria-hidden="true"></i>
                        <span class="badge badge-pill badge-danger up" id="beatRouteNotificationCount">0</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                        <div class="dropdown-menu-header">
                            <h5><?php echo e(__('Beat Route Requests')); ?></h5>
                            <span class="badge badge-round badge-danger">New <span
                                    id="beatRouteNewnotificationCount">0</span></span>
                        </div>

                        <div class="list-group" id="beatRouteNotificationList">
                            <!-- Notification items will be dynamically added here -->
                        </div>
                    </div>
                </li>
            <?php endif; ?>

            <?php if($configDetails['leaveShow'] == '1'): ?>
                <li class="nav-item dropdown" id="notification_dashboard">
                    <a class="nav-link" data-toggle="dropdown" title="Leave Notifications" aria-expanded="false"
                        data-animation="scale-up" role="button">
                        <i class="icon wb-bell" aria-hidden="true"></i>
                        <span class="badge badge-pill badge-danger up" id="notificationCount">0</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                        <div class="dropdown-menu-header">
                            <h5><?php echo e(__('Leave Notifications')); ?></h5>
                            <span class="badge badge-round badge-danger">New <span
                                    id="newnotificationCount">0</span></span>
                        </div>

                        <div class="list-group" id="notificationList">
                            <!-- Notification items will be dynamically added here -->
                        </div>

                        
                    </div>
                </li>
            <?php endif; ?>

            <li class="nav-item dropdown">
                <a class="nav-link navbar-avatar" data-toggle="dropdown" href="#" aria-expanded="false"
                    data-animation="scale-up" role="button" style="padding-top:6px !important;">
                    <span class="avatar avatar-online">
                        <img src="<?php echo e(asset($configDetails['logoPath'])); ?>" alt="avatar"
                            style="width: 100px; height: 30px; object-fit: cover;">
                        <i></i>
                    </span>
                </a>
                <div class="dropdown-menu" role="menu">
                    <?php if(Auth::check()): ?>
                        <a class="dropdown-item" href="<?php echo e(route('profile_setting')); ?>" class="dropdown-item"
                            role="menuitem">
                            <i class="icon fa-solid fa-user" aria-hidden="true"></i> <?php echo e(Auth::user()->name); ?>

                        </a>
                        <hr>
                    <?php endif; ?>
                    <a class="dropdown-item" href="<?php echo e(route('logout')); ?>" role="menuitem"><i class="icon md-power"
                            aria-hidden="true"></i> <?php echo e(__('Logout')); ?></a>
                </div>
            </li>
        </ul>
        <!-- End Navbar Toolbar Right -->
    </div>
    <!-- End Navbar Collapse -->

    <!-- Site Navbar Seach -->
    <div class="collapse navbar-search-overlap" id="site-navbar-search">
        <form role="search">
            <div class="form-group">
                <div class="input-search">
                    <i class="input-search-icon md-search" aria-hidden="true"></i>
                    <input type="text" class="form-control" name="site-search" placeholder="Search...">
                    <button type="button" class="input-search-close icon md-close" data-target="#site-navbar-search"
                        data-toggle="collapse" aria-label="Close"></button>
                </div>
            </div>
        </form>
    </div>
    <!-- End Site Navbar Seach -->
    <!--  </div> -->
</nav>
<script type="text/javascript">
    var url = "<?php echo e(route('changeLang')); ?>";

    $(".changeLang").change(function() {
        window.location.href = url + "?lang=" + $(this).val();
    });
</script>
<script>
    $(document).ready(function() {
        // Function to fetch notification count and list
        function fetchBeatNotifications() {
            $.ajax({
                url: "<?php echo e(route('fetchBeatNotifications')); ?>",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    // Ensure the response is well-formed
                    if (response && response.beatNotificationCount !== undefined && response
                        .beatNotifications) {
                        // Update notification count
                        const {
                            beatNotificationCount,
                            beatNotifications
                        } = response;

                        // Update UI elements
                        $('#beatNotificationCount').text(beatNotificationCount);
                        $('#beatNewNotificationCount').text(beatNotificationCount);

                        $('#beatNotificationList').empty(); // Clear existing beatNotifications

                        // Check if there are any notifications
                        if (beatNotifications.length > 0) {
                            $.each(beatNotifications, function(index, notification) {
                                // Split message by newline character for display
                                var messageLines = notification.message.split("\n");

                                // Add notification to the list
                                $('#beatNotificationList').append(`
                            <a class="list-group-item dropdown-item" href="<?php echo e(route('newBeatRequests')); ?>" role="menuitem">
                                <div class="media">
                                    <div class="pr-10">
                                        <i class="icon wb-user bg-green-600 white icon-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="media-body">
                                        <h6 class="media-heading">${messageLines[0]}</h6>
                                        <span class="media-heading">${messageLines[1]}</span><br>
                                        <time class="media-meta">${notification.time}</time>
                                    </div>
                                </div>
                            </a>
                        `);
                            });
                        } else {
                            // Display no notifications message
                            $('#beatNotificationList').append(
                                '<p class="dropdown-item">No New Beat Notifications</p>');
                        }
                    } else {
                        console.error('Invalid response format');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching beatNotifications:', error);
                }
            });
        }

        function fetchBeatRouteNotifications() {
            $.ajax({
                url: "<?php echo e(route('fetchRoutePlanApprNotifications')); ?>",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    $('#beatRouteNotificationCount').text(response.beatRouteNotificationCount);
                    $('#beatRouteNewnotificationCount').text(response.beatRouteNotificationCount);
                    $('#beatRouteNotificationList')
                        .empty(); // Clear existing beatRouteNotifications

                    if (response.beatRouteNotifications.length > 0) {
                        $.each(response.beatRouteNotifications, function(index, notification) {
                            // Split the message by newline character to display on separate lines
                            var messageLines = notification.message.split("\n");
                            $('#beatRouteNotificationList').append(`
                            <a class="list-group-item dropdown-item" href="<?php echo e(route('newRoutePlanRequests')); ?>" role="menuitem">
                                <div class="media">
                                    <div class="pr-10">
                                        <i class="icon wb-user bg-green-600 white icon-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="media-body">
                                        <h6 class="media-heading">${messageLines[0]}</h6>
                                        <spanclass="media-heading">${messageLines[1]}</span><br>
                                        <time class="media-meta">${notification.time}</time>
                                    </div>
                                </div>
                            </a>
                        `);
                        });
                    } else {
                        $('#beatRouteNotificationList').append(
                            '<p class="dropdown-item">No New Route Plan Notification</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching beatRouteNotifications:', error);
                }
            });
        }

        function fetchNotifications() {
            $.ajax({
                url: "<?php echo e(route('fetchNotifications')); ?>",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    $('#notificationCount').text(response.notificationCount);
                    $('#newnotificationCount').text(response.notificationCount);
                    $('#notificationList').empty(); // Clear existing notifications
                    if (response.notifications.length > 0) {
                        $.each(response.notifications, function(index, notification) {
                            // Split the message by newline character to display on separate lines
                            var messageLines = notification.message.split("\n");
                            $('#notificationList').append(`
                            <a class="list-group-item dropdown-item" href="<?php echo e(route('leave.index')); ?>" role="menuitem">
                                <div class="media">
                                    <div class="pr-10">
                                        <i class="icon wb-user bg-green-600 white icon-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="media-body">
                                        <h6 class="media-heading">${messageLines[0]}</h6>
                                        <spanclass="media-heading">${messageLines[1]}</span><br>
                                        <time class="media-meta">${notification.time}</time>
                                    </div>
                                </div>
                            </a>
                        `);
                        });
                    } else {
                        $('#notificationList').append(
                            '<p class="dropdown-item">No New Leave Notification</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching notifications:', error);
                }
            });
        }

        // Call the function initially when the page loads
        fetchNotifications();
        fetchBeatRouteNotifications();
        fetchBeatNotifications();

        // Call the function every 30 seconds to keep the notifications updated
        // setInterval(fetchNotifications, 30000); // 30 seconds
    });
</script>
<script>
    $(document).ready(function() {
        // Initialize all tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\sfm_v2_laravel\resources\views/layouts/header.blade.php ENDPATH**/ ?>