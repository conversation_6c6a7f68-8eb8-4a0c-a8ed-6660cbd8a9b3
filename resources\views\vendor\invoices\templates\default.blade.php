<!DOCTYPE html>
<html lang="en">

<head>
    <title>{{ $invoice->name }}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <style type="text/css" media="screen">
        html {
            font-family: sans-serif;
            line-height: 1.15;
            margin: 0;
        }

        .custom-data {
            float: right;
            margin-top: 0px;
            font-size: 11.5px;
            line-height: 5%
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 10px;
            margin: 36pt;
            border: 1px solid black;

        }

        h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        strong {
            font-weight: bolder;
        }

        img {
            vertical-align: middle;
            border-style: none;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th,
        td {
            padding: 0.25rem;
            vertical-align: top;
            border-top: 1px solid black;
            /* Add border to cells */
        }

        th {
            text-align: center;
            padding: 0.5rem;
        }

        td {
            text-align: left;
            padding: 0.1rem;
        }

        .mt-5 {
            margin-top: 3rem !important;
        }

        .pr-0,
        .px-0 {
            padding-right: 0 !important;
        }

        .pl-0,
        .px-0 {
            padding-left: 0 !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        * {
            font-family: "DejaVu Sans";
        }

        body,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        table,
        th,
        tr,
        td,
        p,
        div {
            line-height: 1.0;
        }

        .party-header {
            font-size: 1.1rem;
            font-weight: 400;
        }

        .total-amount {
            font-size: 12px;
            font-weight: 700;
        }

        .border-0 {
            border: none !important;
        }

        .cool-gray {
            color: #6B7280;
        }

        p {
            font-size: 12px;
            margin-left: 5px;
        }

        td.pl-0 {
            font-size: 11px;
        }

        tr td p {
            font-size: 12px;
        }

        #abcd {
            border-collapse: collapse;
            width: 100%;
        }

        table {
            border-spacing: 0;
            /* Remove any spacing between cells */
        }
    </style>
</head>
@php
    $totalMrp = 0;
    $totalQty = 0;
    $totalPrice = 0;
    $totalBox = 0;
@endphp

<body>
    {{-- Header --}}
    {{-- @if ($invoice->logo)
        <img src="{{ $invoice->getLogo() }}" alt="logo" height="100">
    @endif --}}

    <div style=" font-size: 16px; color: black;font-weight:bold">
        <center>
            {{ $invoice->name }}</center>
    </div>
    <table class="table mt-1">
        <tbody>
            <tr>
                {{-- <td class="border-0 pl-0" width="70%">
                    <h4 class="text-uppercase">
                        <strong>{{ $invoice->name }}</strong>
                    </h4>
                </td> --}}
                <td class="border-0 pl-0">
                    @if ($invoice->status)
                        <h4 class="text-uppercase cool-gray">
                            <strong>{{ $invoice->status }}</strong>
                        </h4>
                    @endif
                    {{-- <p>{{ __('Order No') }}: <strong>{{ $invoice->getSerialNumber() }}</strong></p>
                    <p style="margin-top: -15px;">{{ __('Order Date') }}: <strong>{{ $invoice->getDate() }}</strong></p>
                    <p style="margin-top: -15px;">{{ __('Due Date') }}: <strong>{{ date('d/m/Y') }}</strong></p> --}}
                    {{-- <p>{{ __('Order By') }}:</p> --}}

                </td>
                <td class="border-0 pl-0">

                    <div class="custom-data">
                        {!! $invoice->getCustomData() !!}
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    {{-- Seller - Buyer --}}
    <table class="table" id="abcd" style="margin-top:94px;">
        <thead>
            <tr>
                <th class="border-1 pl-0 party-header" width="50%" style="border-right:1px solid black">
                    {{ __('invoices::invoice.seller') }}
                </th>

                <th class="border-1 pl-0 party-header" width="50%">
                    {{ __('invoices::invoice.buyer') }}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="px-0" style="border-right:1px solid black">
                    @if ($invoice->seller->name)
                        <p class="seller-name">
                            <strong>{{ $invoice->seller->name }}</strong>
                        </p>
                    @endif

                    @if ($invoice->seller->address)
                        <p class="seller-address" style="margin-top: -13px;">
                            {{ __('invoices::invoice.address') }}:<strong> {{ $invoice->seller->address }}</strong>
                        </p>
                    @endif

                    @if ($invoice->seller->code)
                        <p class="seller-code" style="margin-top: -13px;">
                            {{ __('invoices::invoice.code') }}: <strong>{{ $invoice->seller->code }}</strong>
                        </p>
                    @endif

                    @if ($invoice->seller->vat)
                        <p class="seller-vat" style="margin-top: -13px;">
                            {{ __('invoices::invoice.vat') }}:<strong> {{ $invoice->seller->vat }}</strong>
                        </p>
                    @endif

                    @if ($invoice->seller->phone)
                        <p class="seller-phone" style="margin-top: -13px;">
                            {{ __('invoices::invoice.phone') }}:<strong> {{ $invoice->seller->phone }}</strong>
                        </p>
                    @endif

                    @foreach ($invoice->seller->custom_fields as $key => $value)
                        <p class="seller-custom-field" style="margin-top: -13px;">
                            {{ ucfirst($key) }}:<strong> {{ $value }}</strong>
                        </p>
                    @endforeach
                </td>
                <td class="px-0">
                    @if ($invoice->buyer->name)
                        <p class="buyer-name">
                            <strong>{{ $invoice->buyer->name }}</strong>
                        </p>
                    @endif

                    @if ($invoice->buyer->address)
                        <p class="buyer-address" style="margin-top: -13px;">
                            {{ __('invoices::invoice.address') }}:<strong> {{ $invoice->buyer->address }}</strong>
                        </p>
                    @endif

                    @if ($invoice->buyer->code)
                        <p class="buyer-code" style="margin-top: -13px;">
                            {{ __('invoices::invoice.code') }}:<strong> {{ $invoice->buyer->code }}</strong>
                        </p>
                    @endif

                    @if ($invoice->buyer->vat)
                        <p class="buyer-vat" style="margin-top: -13px;">
                            {{ __('invoices::invoice.vat') }}:<strong> {{ $invoice->buyer->vat }}</strong>
                        </p>
                    @endif

                    @if ($invoice->buyer->phone)
                        <p class="buyer-phone" style="margin-top: -13px;">
                            {{ __('invoices::invoice.phone') }}:<strong> {{ $invoice->buyer->phone }}</strong>
                        </p>
                    @endif

                    @foreach ($invoice->buyer->custom_fields as $key => $value)
                        <p class="buyer-custom-field" style="margin-top: -13px;">
                            {{ ucfirst($key) }}:<strong> {{ $value }}</strong>
                        </p>
                    @endforeach
                </td>
            </tr>
        </tbody>
    </table>

    {{-- Table --}}
    <table class="table table-items" style="margin-top: 0px;">
        <thead>
            <tr>
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">#</th>
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                    {{ __('invoices::invoice.description') }}</th>
                {{-- @if ($invoice->hasItemUnits)
                    <th scope="col" class="text-center border-0">{{ __('invoices::invoice.units') }}</th>
                @endif --}}
                {{--  --}}
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                    {{ __('Mrp') }}</th>
                {{-- <th scope="col" class="text-right border-0">{{ __('Bunch') }}</th> --}}
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                    {{ __('Box / Bundle') }}</th>
                {{-- <th scope="col" class="text-right border-0">{{ __('Pcs') }}</th> --}}
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                    {{ __('Total Qty') }}</th>
                <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                    {{ __('invoices::invoice.price') }}</th>
                @if ($invoice->hasItemDiscount)
                    <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                        {{ __('invoices::invoice.discount') }}</th>
                @endif



                {{-- <th scope="col" class="text-right border-0">{{ __('Hsn / Sac') }}</th> --}}

                {{-- <th scope="col" class="text-right border-0">{{ __('GST') }}</th> --}}

                @if ($invoice->hasItemTax)
                    <th scope="col" class="text-center border-1" style="border-right:1px solid black">
                        {{ __('invoices::invoice.tax') }}</th>
                @endif
                <th scope="col" class="text-center border-1 pr-0">{{ __('invoices::invoice.sub_total') }}</th>
            </tr>
        </thead>
        <tbody>
            {{-- Items --}}
            @foreach ($invoice->items as $index => $item)
                <tr>
                    <td class="text-center" style="border-right:1px solid black">{{ $index + 1 }}</td>
                    <td class="text-center" style="border-right:1px solid black">
                        {{ $item->title }}

                        @if ($item->description)
                            <p class="text-center">{{ $item->description }}</p>
                        @endif
                    </td>
                    {{-- @if ($invoice->hasItemUnits)
                        <td class="text-center">{{ $item->units }}</td>
                    @endif --}}
                    {{--  --}}
                    @php
                        $totalMrp += $item->mrp;
                        $totalBox += $item->box;
                        $totalQty += $item->quantity;
                        $totalPrice += $item->price_per_unit;
                    @endphp
                    <td class="text-center" style="border-right:1px solid black">
                        {{ $invoice->formatCurrency($item->mrp) }}
                    </td>
                    {{-- <td class="text-center">{{ $item->bunch }}</td> --}}
                    <td class="text-center" style="border-right:1px solid black">{{ $item->box }}</td>
                    {{-- <td class="text-center">{{ $item->pcs }}</td> --}}

                    <td class="text-center" style="border-right:1px solid black">{{ $item->quantity }}</td>
                    <td class="text-center" style="border-right:1px solid black">
                        {{ $invoice->formatCurrency($item->price_per_unit) }}
                    </td>
                    @if ($invoice->hasItemDiscount)
                        <td class="text-center" style="border-right:1px solid black">
                            {{ $invoice->formatCurrency($item->discount) }}
                        </td>
                    @endif



                    {{-- <td class="text-right">
                        {{ $item->hsn }}
                    </td> --}}

                    {{-- <td class="text-right">
                        {{ $item->gst }}
                    </td> --}}

                    @if ($invoice->hasItemTax)
                        <td class="text-center" style="border-right:1px solid black">
                            {{ $invoice->formatCurrency($item->tax) }}
                        </td>
                    @endif

                    <td class="text-center pr-0">
                        {{ $invoice->formatCurrency($item->sub_total_price) }}
                    </td>
                </tr>
            @endforeach
            {{-- Summary --}}
            @if ($invoice->hasItemOrInvoiceDiscount())
                <tr>
                    <td colspan="{{ $invoice->table_columns - 2 }}" class="border-0"></td>
                    <td class="text-center pl-0">{{ __('invoices::invoice.total_discount') }}</td>
                    <td class="text-center pr-0">
                        {{ $invoice->formatCurrency($invoice->total_discount) }}
                    </td>
                </tr>
            @endif
            @if ($invoice->taxable_amount)
                <tr>
                    <td colspan="{{ $invoice->table_columns + 0 }}" class="border-0"></td>
                    <td class="text-center pl-0">{{ __('Packaging Charge') }}</td>
                    <td class="text-center pr-0 total-amount">
                        {{ $invoice->formatCurrency($invoice->taxable_amount) }}
                    </td>
                </tr>
            @endif
            @if ($invoice->tax_rate)
                <tr>
                    <td colspan="{{ $invoice->table_columns - 2 }}" class="border-0"></td>
                    <td class="text-center pl-0">{{ __('invoices::invoice.tax_rate') }}</td>
                    <td class="text-center pr-0">
                        {{ $invoice->tax_rate }}%
                    </td>
                </tr>
            @endif
            @if ($invoice->hasItemOrInvoiceTax())
                <tr>
                    <td colspan="{{ $invoice->table_columns - 2 }}" class="border-0"></td>
                    <td class="text-center pl-0">{{ __('invoices::invoice.total_taxes') }}</td>
                    <td class="text-center pr-0">
                        {{ $invoice->formatCurrency($invoice->total_taxes) }}
                    </td>
                </tr>
            @endif
            @if ($invoice->shipping_amount)
                <tr>
                    <td colspan="{{ $invoice->table_columns + 1 }}" class="border-0"></td>
                    <td class="text-center pl-0">{{ __('invoices::invoice.shipping') }}</td>
                    <td class="text-center pr-0">
                        {{ $invoice->formatCurrency($invoice->shipping_amount) }}
                    </td>
                </tr>
            @endif
            {{-- <tr>
                <td colspan="{{ $invoice->table_columns + 0 }}" class="border-0"></td>
                <td class="text-right pl-0">{{ __('Total Qty') }}</td>
                <td class="text-right pr-0 total-amount">
                    {{ $totalQty }}
                </td>
            </tr> --}}
            <tr>
                {{-- <td colspan="{{ $invoice->table_columns - 7 }}" class="border-0"></td> --}}
                {{-- <td class="text-center pl-0">{{ __('Total Qty') }}</td> --}}
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{-- index blank field --}}
                    -
                </td>
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{-- description blank field --}}--
                </td>
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{-- {{ number_format($totalMrp, 2) }} ₹ --}}
                </td>
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{ $totalBox }}
                </td>
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{ $totalQty }}
                </td>
                <td class="text-center pr-0 total-amount"
                    style="border-right:1px solid black;border-bottom:1px solid black">
                    {{ number_format($totalPrice, 2) }} ₹
                </td>

                {{-- <td colspan="{{ $invoice->table_columns + 0 }}" class="border-0"></td> --}}
                {{-- <td class="text-right pl-0">{{ __('invoices::invoice.total_amount') }}</td> --}}
                <td class="text-center pr-0 total-amount" style="border-bottom:1px solid black">
                    {{ $invoice->formatCurrency($invoice->total_amount + $invoice->taxable_amount) }}
                </td>
            </tr>

        </tbody>
    </table>

    {{-- @if ($invoice->notes)
        <p>
            {{ __('invoices::invoice.notes') }}: {!! $invoice->notes !!}
        </p>
    @endif --}}

    <p style="margin-top:2px;">
        {{ __('invoices::invoice.amount_in_words') }}: {{ $invoice->getTotalAmountInWords() }}
    </p>

    <div
        style="position: fixed;margin-left:5px; bottom: 90px; font-size: 12px; color: black;max-width:300px;word-wrap: break-word;font-weight:bold">
        Authorised Sign: <br> {{ $invoice->seller->onName }}
    </div>
    <hr style="position: fixed; bottom: 79px;width:695px;  color:black;">
    <div
        style="position: fixed;margin-left:5px; bottom: 65px;  font-size: 14px; color: black;max-width:500px;word-wrap: break-word;font-weight:bold">
        Note:{{ $invoice->notes }}
    </div>
    <div style="position: fixed; bottom: 20px; right: 20px; font-size: 11px; color: black;">
        Generated by <a href="https://myebooks.online" target="_blank"
            style=" text-decoration: none;">myebooks.online</a>
    </div>


    <script type="text/php">
            if (isset($pdf) && $PAGE_COUNT > 1) {
                $text = "{{ __('invoices::invoice.page') }} {PAGE_NUM} / {PAGE_COUNT}";
                $size = 10;
                $font = $fontMetrics->getFont("Verdana");
                $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
                $x = ($pdf->get_width() - $width);
                $y = $pdf->get_height() - 35;
                $pdf->page_text($x, $y, $text, $font, $size);
            }
        </script>
</body>

</html>
