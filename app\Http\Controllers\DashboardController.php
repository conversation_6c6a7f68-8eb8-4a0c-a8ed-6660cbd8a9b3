<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\BeatDatewiseMaster;
use App\Models\BeatMaster;
use App\Models\CallMaster;
use App\Models\CustomerMaster;
use App\Models\EmployeeMaster;
use App\Models\LeaveApplication;
use App\Models\OrderMaster;
use App\Models\ProductMaster;
use App\Models\StateMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use DateTime;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    private $start_date;
    private $end_date;
    private $today;

    public function __construct()
    {
        $this->today = Carbon::today();
        $this->start_date = $this->today->startOfDay()->toDateTimeString();
        $this->end_date = $this->today->endOfDay()->toDateTimeString();
    }

    private function getCommonData()
    {
        $currentDate = $this->today->format('d-m-Y');
        $currentDay = $this->today->format('l');

        $totalEmpCount = EmployeeMaster::where('emp_status', 1)->count();
        // $totalEmpCount = Cache::remember('total_emp_count', 60, function () {
        //     return EmployeeMaster::where('emp_status', 1)->count();
        // });
        $totalRetailerCount = CustomerMaster::where('cm_type', 5)->where('cm_status', 1)->count(); //5 - retailer
        $totalProductCount = ProductMaster::where('product_status', 1)->count();
        // $totalProductCount = Cache::remember('total_product_count', 60, function () {
        //     return ProductMaster::where('product_status', 1)->count();
        // });
        $totalDistCount = CustomerMaster::where('cm_type', 3)->where('cm_status', 1)->count(); //4 - dealer

        $ss_order = $this->customerOrderCount(2);
        $distributor_order = $this->customerOrderCount(3);
        $dealer_order = $this->customerOrderCount(4);
        $retailer_order = $this->customerOrderCount(5);

        return compact(
            'totalEmpCount',
            'totalRetailerCount',
            'totalProductCount',
            'totalDistCount',
            'ss_order',
            'distributor_order',
            'dealer_order',
            'retailer_order',
            'currentDate',
            'currentDay'
        );
    }

    private function customerOrderCount($cmType)
    {
        // dd($this->start_date, $this->end_date);
        // return Cache::remember("customer_count_{$cmType}", 1, function () use ($cmType) {
        return DB::table('call_master')
            ->select('client_code')
            ->join('customer_master', 'call_master.client_code', '=', 'customer_master.cm_code')
            ->where('customer_master.cm_type', $cmType)
            ->whereBetween('call_master.created_at', [$this->start_date, $this->end_date])
            ->count();
        // });
    }

    public function index()
    {
        $commonData = $this->getCommonData();

        // Retrieve logged-in employees with their attendance data
        $loggedEmployeeAttendance = EmployeeMaster::where('emp_status', 1)->with([
            'attendance' => function ($query) {
                $query->whereBetween('created_at', [$this->start_date, $this->end_date])
                    ->select('atd_id', 'emp_code', 'atd_type', 'atd_login_time', 'atd_logout_time', 'atd_remarks');
            },
            'total_call' => function ($query) {
                $query->whereBetween('created_at', [$this->start_date, $this->end_date])
                    ->select('emp_code', DB::raw('MAX(created_at) as last_call_time'), DB::raw('MIN(created_at) as first_call_time'), DB::raw('SUM(CASE WHEN call_status != 4 THEN grand_total ELSE 0 END) as total_grand_total'))
                    ->groupBy('emp_code');
            },
            'designation',
            'empStateRelation.state'
        ])
        ->withCount([
            'total_call' => function ($query) {
                $query->whereBetween('created_at', [$this->start_date, $this->end_date]); // Filter by today's date
            },
            'total_call as total_product_call' => function ($query) {
                $query->whereBetween('created_at', [$this->start_date, $this->end_date])
                    ->where('product_order_type', 1);
            }
        ])->get();

        // Combine the logged-in employees and their attendance data
        $dashboardList = collect([]);

        foreach ($loggedEmployeeAttendance as $employee) {
            if ($employee->attendance && $employee->attendance->count() > 0) {
                $dashboardList->prepend($employee);
            } else {
                $dashboardList->push($employee);
            }
        }

        $allStates = Cache::remember('all_states', 20, function () {
            return StateMaster::pluck('state_name', 'state_id');
        });
        // get states 
        $dashboardList->map(function ($attendance) use ($allStates) {
            $stateIds = $attendance->empStateRelation->pluck('state_id');
            $stateNames = $stateIds->map(fn($id) => $allStates[$id] ?? null)->filter()->implode(', ');
            $attendance['state_names'] = $stateNames;
            return $attendance;
        });

        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view(
                'admin.dashboard',
                array_merge($commonData, ['dashboardList' => $dashboardList])
            )
            . view('layouts.scripts')
            . view('layouts.footer');
    }

    public function dashboard_charts()
    {
        $commonData = $this->getCommonData();

        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view(
                'admin.dashboard_charts',
                array_merge($commonData, [])
            )
            . view('layouts.scripts')
            . view('layouts.footer');
    }

    public function getTcPcChartData($period)
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfWeek = Carbon::now()->endOfWeek();
        if ($period == 'all') {
            $tcPcCharts = CallMaster::selectRaw('DATE(created_at) as date, COUNT(*) as total_calls, SUM(case when product_order_type = 1 then 1 else 0 end) as productive_calls')
                ->groupBy('date')
                ->get();
        } else if ($period == 'today') {
            $tcPcCharts = CallMaster::selectRaw('DATE(created_at) as date, COUNT(*) as total_calls, SUM(case when product_order_type = 1 then 1 else 0 end) as productive_calls')
                ->whereBetween('created_at', [$this->start_date, $this->end_date])
                ->groupBy('date')
                ->get();
        } else if ($period == 'weekWise') {
            $tcPcCharts = CallMaster::selectRaw('DATE(created_at) as date, COUNT(*) as total_calls, SUM(case when product_order_type = 1 then 1 else 0 end) as productive_calls')
                ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
                ->groupBy('date')
                ->get();
        } else if ($period == 'monthWise') {
            $tcPcCharts = CallMaster::selectRaw('MONTH(created_at) as date, YEAR(created_at) as year, COUNT(*) as total_calls, SUM(case when product_order_type = 1 then 1 else 0 end) as productive_calls')
                ->groupBy(DB::raw('MONTH(created_at)'), DB::raw('YEAR(created_at)'))
                ->get()
                ->map(function ($item) {
                    $dateObj = DateTime::createFromFormat('!m', $item->date);
                    $monthName = $dateObj->format('F'); // Convert month number to month name
                    $item->date = $monthName . ' ' . $item->year;
                    unset($item->month);
                    unset($item->year);
                    return $item;
                });
        }

        return response()->json($tcPcCharts);
    }

    public function getTopTenSkuChartData($period)
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfWeek = Carbon::now()->endOfWeek();

        // Define the base query structure
        $query = OrderMaster::select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
            ->groupBy('product_code')
            ->orderByDesc('total_basic_rate')
            ->take(10)
            ->with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                }
            ]);

        // Apply filters based on the period
        switch ($period) {
            case 'today':
                $query->whereHas('call', function ($query) {
                    $query->whereBetween('created_at', [$this->start_date, $this->end_date]);
                });
                break;

            case 'yesterday':
                $query->whereHas('call', function ($query) {
                    $query->whereDate('created_at', Carbon::yesterday());
                });
                break;

            case 'this_week':
                $query->whereHas('call', function ($query) use ($startOfWeek, $endOfWeek) {
                    $query->whereBetween('created_at', [$startOfWeek, $endOfWeek]);
                });
                break;

            case 'this_month':
                $query->whereHas('call', function ($query) use ($startOfMonth) {
                    $query->whereDate('created_at', '>=', $startOfMonth);
                });
                break;

            case 'last_month':
                $lastMonth = Carbon::now()->subMonth();
                $startOfLastMonth = $lastMonth->startOfMonth();
                $endOfLastMonth = $lastMonth->endOfMonth();
                $query->whereHas('call', function ($query) use ($startOfLastMonth, $endOfLastMonth) {
                    $query->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth]);
                });
                break;

            case 'this_year':
                $query->whereHas('call', function ($query) {
                    $query->whereYear('created_at', Carbon::now()->year);
                });
                break;

            case 'last_year':
                $lastYear = Carbon::now()->subYear();
                $query->whereHas('call', function ($query) use ($lastYear) {
                    $query->whereYear('created_at', $lastYear->year);
                });
                break;

            case 'all':
            default:
                // No specific filter applied, we already set up the query without whereHas
                break;
        }

        $topTenSkuChart = $query->get();

        return response()->json($topTenSkuChart);
    }

    public function getLastTenSlowMoverChartData($period)
    {
        //$period =today,yesterday,this_week,,this_month,last_month,this_year,last_year
        $startOfWeek = Carbon::now()->startOfWeek();
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfWeek = Carbon::now()->endOfWeek();
        if ($period == 'all') {
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'today') {
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) {
                    $query->whereBetween('created_at', [$this->start_date, $this->end_date]);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'yesterday') {
            $yesterday = Carbon::yesterday();
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) use ($yesterday) {
                    $query->whereDate('created_at', $yesterday);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'this_week') {
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) use ($startOfWeek, $endOfWeek) {
                    $query->whereBetween('created_at', [$startOfWeek, $endOfWeek]);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'this_month') {
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) use ($startOfMonth) {
                    $query->whereDate('created_at', '>=', $startOfMonth);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'last_month') {
            $lastMonth = Carbon::now()->subMonth();
            $startOfLastMonth = $lastMonth->startOfMonth();
            $endOfLastMonth = $lastMonth->endOfMonth();
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) use ($startOfLastMonth, $endOfLastMonth) {
                    $query->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth]);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'this_year') {
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) {
                    $query->whereYear('created_at', $this->today->year);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        } else if ($period == 'last_year') {
            $lastYear = Carbon::now()->subYear();
            $slowMoversChartData = OrderMaster::with([
                'product' => function ($query) {
                    $query->select('product_code', 'product_name');
                },
                'call' => function ($query) {
                    $query->select('call_code', 'created_at');
                }
            ])
                ->select('product_code', DB::raw('SUM(total_basic_rate) as total_basic_rate'))
                ->whereHas('call', function ($query) use ($lastYear) {
                    $query->whereYear('created_at', $lastYear->year);
                })
                ->groupBy('product_code')
                ->orderBy('total_basic_rate', 'asc')
                ->take(10)
                ->get();
        }

        return response()->json($slowMoversChartData);
    }


    public function fetchNotifications()
    {
        // Fetch new leave requests
        $newLeaveRequests = LeaveApplication::with('employee')
        ->select('la_code', 'emp_code', 'created_at')
            ->where('la_notification_status', 0)
            ->orderBy('created_at', 'desc')
            ->get();

        $notifications = [];
        foreach ($newLeaveRequests as $leave) {
            $notificationMessage = $leave->employee->emp_name;
            // Check if created_at is null
            if ($leave->created_at) {
                $notificationTime = $leave->created_at->diffForHumans();
            } else {
                // Provide a default message or handling for null created_at
                $notificationTime = "Unknown time";
            }

            // Construct the notification message with the employee name on the second line
            $message = "New Leave Request\n{$notificationMessage}";

            $notifications[] = [
                'message' => $message, // Customize the message as needed
                'time' => $notificationTime, // Format the time as needed
            ];
        }
        $notificationCount = count($notifications);
        // dd($notificationCount);

        return response()->json([
            'notificationCount' => $notificationCount,
            'notifications' => $notifications,
        ]);
    }

    public function fetchRoutePlanApprNotifications()
    {
        // Fetch new beat requests
        $newBeatRouteRequests = Cache::remember('new_beat_requests', 10, function () {
            return BeatDatewiseMaster::with('employee')
                ->where('bdm_status', 0)
                ->orderBy('created_at', 'desc')
                ->get();
        });

        $beatRouteNotifications = [];
        foreach ($newBeatRouteRequests as $beatRoute) {
            $notificationMessage = $beatRoute->employee->emp_name;
            // Check if created_at is null
            if ($beatRoute->created_at) {
                $notificationTime = $beatRoute->created_at->diffForHumans();
            } else {
                // Provide a default message or handling for null created_at
                $notificationTime = "Unknown time";
            }

            // Construct the notification message with the employee name on the second line
            $message = "New Route Plan Request\n{$notificationMessage}";

            $beatRouteNotifications[] = [
                'message' => $message, // Customize the message as needed
                'time' => $notificationTime, // Format the time as needed
            ];
        }
        $beatRouteNotificationCount = count($beatRouteNotifications);
        // dd($beatRouteNotificationCount);

        return response()->json([
            'beatRouteNotificationCount' => $beatRouteNotificationCount,
            'beatRouteNotifications' => $beatRouteNotifications,
        ]);
    }

    public function fetchBeatNotifications()
    {
        // Fetch new beat requests with employee details (optimized with select for required fields)
        $newBeatRequests = BeatMaster::with('employee:emp_code,emp_name') // Select only necessary columns
            ->where('beat_status', 0) // Filter by pending status
            ->orderBy('created_at', 'desc') // Sort by creation date
            ->get();

        // Map notifications data directly using collection methods
        $beatNotifications = $newBeatRequests->map(function ($beat) {
            $notificationMessage = $beat->employee->emp_name;
            $notificationTime = $beat->created_at ? $beat->created_at->diffForHumans() : 'Unknown time';
            $message = "New Beat Request\n{$notificationMessage}";

            return [
                'message' => $message,  // Notification message with employee name
                'time' => $notificationTime,  // Formatted time
            ];
        });

        // Get count of notifications
        $beatNotificationCount = $beatNotifications->count();

        return response()->json([
            'beatNotificationCount' => $beatNotificationCount,
            'beatNotifications' => $beatNotifications,
        ]);
    }
}
