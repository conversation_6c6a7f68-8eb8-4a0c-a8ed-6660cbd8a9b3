{"__meta": {"id": "X8113364267844a2b2ea9bed0fc683f15", "datetime": "2025-06-03 13:14:45", "utime": **********.193528, "method": "GET", "uri": "/sfm_v2_laravel/public/fetchNotifications", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748936684.508908, "end": **********.193554, "duration": 0.6846458911895752, "duration_str": "685ms", "measures": [{"label": "Booting", "start": 1748936684.508908, "relative_start": 0, "end": 1748936684.950796, "relative_end": 1748936684.950796, "duration": 0.44188785552978516, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1748936684.950814, "relative_start": 0.4419059753417969, "end": **********.193557, "relative_end": 3.0994415283203125e-06, "duration": 0.24274301528930664, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36678792, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET fetchNotifications", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@fetchNotifications", "namespace": null, "prefix": "", "where": [], "as": "fetchNotifications", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=420\" onclick=\"\">app/Http/Controllers/DashboardController.php:420-455</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09207, "accumulated_duration_str": "92.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.035467, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.04092, "duration": 0.01653, "duration_str": "16.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 17.954}, {"sql": "select `la_code`, `emp_code`, `created_at` from `leave_application` where `la_notification_status` = 0 order by `created_at` desc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 427}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0674849, "duration": 0.03233, "duration_str": "32.33ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:427", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 427}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=427", "ajax": false, "filename": "DashboardController.php", "line": "427"}, "connection": "gopi_new", "explain": null, "start_percent": 17.954, "width_percent": 35.115}, {"sql": "select * from `employee_master` where `employee_master`.`emp_code` in ('EMP0001', 'EMP0002', 'EMP0003', 'EMP0004', 'EMP0006', 'EMP0008', 'EMP0010', 'EMP0011', 'EMP0022', 'EMP0024', 'EMP0030', 'EMP0031', 'EMP0032', 'EMP0041', 'EMP0044', 'EMP0048', 'EMP0050', 'EMP0051', 'EMP0055', 'EMP0057', 'EMP0060', 'EMP0062', 'NXC1234')", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0002", "EMP0003", "EMP0004", "EMP0006", "EMP0008", "EMP0010", "EMP0011", "EMP0022", "EMP0024", "EMP0030", "EMP0031", "EMP0032", "EMP0041", "EMP0044", "EMP0048", "EMP0050", "EMP0051", "EMP0055", "EMP0057", "EMP0060", "EMP0062", "NXC1234"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 427}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.109077, "duration": 0.04321, "duration_str": "43.21ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:427", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 427}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=427", "ajax": false, "filename": "DashboardController.php", "line": "427"}, "connection": "gopi_new", "explain": null, "start_percent": 53.068, "width_percent": 46.932}]}, "models": {"data": {"App\\Models\\LeaveApplication": {"value": 73, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FLeaveApplication.php&line=1", "ajax": false, "filename": "LeaveApplication.php", "line": "?"}}, "App\\Models\\EmployeeMaster": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmployeeMaster.php&line=1", "ajax": false, "filename": "EmployeeMaster.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 97, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/fetchNotifications", "status_code": "<pre class=sf-dump id=sf-dump-257329188 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-257329188\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1130080631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1130080631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1683771397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683771397\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6Ijg0VW9VSzFZQm9YdHVmbWdpTVN6U0E9PSIsInZhbHVlIjoiTVk3cTVxeFRrcXN0bXRSbHF2NGM4Yklyc202aHg1OHlyZUZORjU2UnVGSDd2cnUydDR0SUw1WG9RazRQVVlYZU9wTHFDcFNXMXRFSEtHOStDV1NIeVBaM203ZXk0NEhheUtlN3NuUXBqSGlOUmhqZmpEcGxra2ZYSnRyT0RWNnQiLCJtYWMiOiIxNmNiZWE0ZDExMTMyMGFhOTZkZmQ5ZjQwNzIyNDc1OWM0MDZkNTMyY2M0YzJjZTA3YTU4MDMxZjc4OWJhNTIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpaT3NYNDdKWDE0NG0vajUrNy9TOWc9PSIsInZhbHVlIjoiQ0JYZ1ZBWVgwY3dMbW83dzBWemxMbWMrdXlUMFRrQTVjd1dNUzl2QjFtRXU2OEdHSUZoMU5maHVOTjdwMmNQM1JJOWxOdjhNbnhwYmw5M3dndXkvSGdteEJZb3ZtRXBVdlUwR3JJZzVRR0pTZGdPT2dkSHZ4VlVEQmFnOXhwaFQiLCJtYWMiOiI5ZWRiOTY4ZDk4OTIwMTBkY2M3ODMwOTAyNTJhZWY3ZmU2NzI5OWIyZjI1Mjg2Mjc4YTlhMjFmZTlmMzIwNzVhIiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6IjI2R1licnQ4elBoTlVjMGpCVVJTYWc9PSIsInZhbHVlIjoiZStjdkpWOXJCQjA5endHSUM0VktHME5Qelc2anZ6aVRRUmxKYTZ2Sk9hejNZMXdUczVXZm0vWTlUYkt1SXVZTkdodGJVRTlsK3hRMXQ5RUhjTnNOSXd2b1dQN0ZNNjRsNkd4N2JLakF1YlYyUkF4aElxSGE0UDdveEE0WmI3R0QiLCJtYWMiOiJlYTVlMWM2MTViNWExYWFiZDc3MWY5ZjRiNTgxMDgwMDM1ZWY3OGU3MTUxYWJlYjc0NWMxMjczNDQ4Mjk2Mzc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-817974578 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817974578\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:44:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6Ik1hVDBuNmkwZUdQcVNoSlVWdmN1b2c9PSIsInZhbHVlIjoiWjUrcWdzOE40cE4xcE5reEdhZVlmTkVPQnNoMFBERXN5dVBPMGpnekwyL3Y1N0RQUTAwUTVUaFNJSE16bEVhZU03THJ4TjdDMVY1UU1wbjJhY2F0OXZKU2tmdEFsbGcwL2thekk0UmJ3c3c2VDJQd0JMaHZ1NUErL21kVXR1dFoiLCJtYWMiOiIzNjkzNTkxOGM2YTY1OTdjNmFiY2MxYWJlNzY1YzdkZjhkODc5NzA0Yzg4YzZhZWQ5ZmU0ZjZiOTMwZDg2OGRjIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:44:45 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IkVrU2pSNzFCUnVaUnBEekdXUDZTL2c9PSIsInZhbHVlIjoiOWRnOWgzSlhsVkxsOHZhMEJ1dlNHSGRGUGhuK3Z3UXNpaGpPZUFaOGlZWGo1c203UHpUMGltZzk3VW5BRkdzNzdiWldLY1htc0pPSTlOS2JIVDVXVk5BeDRjMVJSYW9YYzZUL3lyYmV5QW1LcThsTmdhT1FyZE1VWlQwSDlLamEiLCJtYWMiOiI2MGU0Zjc3NzVkNGFjNDg2MjA1Nzk2YmM0YzA4NGIxZDZjODU2M2EwZmIyZTI5ZWVjZmMxMmNhNjU5NTA3MTIyIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:44:45 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1hVDBuNmkwZUdQcVNoSlVWdmN1b2c9PSIsInZhbHVlIjoiWjUrcWdzOE40cE4xcE5reEdhZVlmTkVPQnNoMFBERXN5dVBPMGpnekwyL3Y1N0RQUTAwUTVUaFNJSE16bEVhZU03THJ4TjdDMVY1UU1wbjJhY2F0OXZKU2tmdEFsbGcwL2thekk0UmJ3c3c2VDJQd0JMaHZ1NUErL21kVXR1dFoiLCJtYWMiOiIzNjkzNTkxOGM2YTY1OTdjNmFiY2MxYWJlNzY1YzdkZjhkODc5NzA0Yzg4YzZhZWQ5ZmU0ZjZiOTMwZDg2OGRjIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:44:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IkVrU2pSNzFCUnVaUnBEekdXUDZTL2c9PSIsInZhbHVlIjoiOWRnOWgzSlhsVkxsOHZhMEJ1dlNHSGRGUGhuK3Z3UXNpaGpPZUFaOGlZWGo1c203UHpUMGltZzk3VW5BRkdzNzdiWldLY1htc0pPSTlOS2JIVDVXVk5BeDRjMVJSYW9YYzZUL3lyYmV5QW1LcThsTmdhT1FyZE1VWlQwSDlLamEiLCJtYWMiOiI2MGU0Zjc3NzVkNGFjNDg2MjA1Nzk2YmM0YzA4NGIxZDZjODU2M2EwZmIyZTI5ZWVjZmMxMmNhNjU5NTA3MTIyIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:44:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}