{"__meta": {"id": "Xb73e19fa7ccbab5982bab20dc64cca02", "datetime": "2025-06-03 13:13:01", "utime": **********.239606, "method": "GET", "uri": "/sfm_v2_laravel/public/dashboard", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748936580.76773, "end": **********.239624, "duration": 0.4718940258026123, "duration_str": "472ms", "measures": [{"label": "Booting", "start": 1748936580.76773, "relative_start": 0, "end": **********.060242, "relative_end": **********.060242, "duration": 0.2925119400024414, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060254, "relative_start": 0.2925240993499756, "end": **********.239626, "relative_end": 1.9073486328125e-06, "duration": 0.17937183380126953, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 37230752, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "layouts.stylesheets", "param_count": null, "params": [], "start": **********.213889, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/stylesheets.blade.phplayouts.stylesheets", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fstylesheets.blade.php&line=1", "ajax": false, "filename": "stylesheets.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.217371, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.sidebar", "param_count": null, "params": [], "start": **********.222028, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/sidebar.blade.phplayouts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "admin.dashboard", "param_count": null, "params": [], "start": **********.224358, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard.blade.phpadmin.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "admin.dashboard_box_content", "param_count": null, "params": [], "start": **********.225414, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard_box_content.blade.phpadmin.dashboard_box_content", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard_box_content.blade.php&line=1", "ajax": false, "filename": "dashboard_box_content.blade.php", "line": "?"}}, {"name": "layouts.scripts", "param_count": null, "params": [], "start": **********.23113, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/scripts.blade.phplayouts.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.232285, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dashboard", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=83\" onclick=\"\">app/Http/Controllers/DashboardController.php:83-141</a>"}, "queries": {"nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04103999999999999, "accumulated_duration_str": "41.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.100764, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 1.17}, {"sql": "select count(*) as aggregate from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1089768, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:40", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=40", "ajax": false, "filename": "DashboardController.php", "line": "40"}, "connection": "gopi_new", "explain": null, "start_percent": 1.17, "width_percent": 0.999}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 5 and `cm_status` = 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.113614, "duration": 0.022629999999999997, "duration_str": "22.63ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=44", "ajax": false, "filename": "DashboardController.php", "line": "44"}, "connection": "gopi_new", "explain": null, "start_percent": 2.169, "width_percent": 55.141}, {"sql": "select count(*) as aggregate from `product_master` where `product_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.139821, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=45", "ajax": false, "filename": "DashboardController.php", "line": "45"}, "connection": "gopi_new", "explain": null, "start_percent": 57.31, "width_percent": 1.145}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 3 and `cm_status` = 1", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.143724, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:49", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=49", "ajax": false, "filename": "DashboardController.php", "line": "49"}, "connection": "gopi_new", "explain": null, "start_percent": 58.455, "width_percent": 2.096}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 2 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [2, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1483011, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 60.551, "width_percent": 1.194}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 3 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [3, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.151944, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 61.745, "width_percent": 0.999}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 4 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [4, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.155468, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 62.744, "width_percent": 0.975}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 5 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [5, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 54}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.159389, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 63.718, "width_percent": 1.243}, {"sql": "select `employee_master`.*, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59') as `total_call_count`, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' and `product_order_type` = 1) as `total_product_call` from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": ["2025-06-03 00:00:00", "2025-06-03 23:59:59", "2025-06-03 00:00:00", "2025-06-03 23:59:59", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.165758, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 64.961, "width_percent": 4.02}, {"sql": "select `atd_id`, `emp_code`, `atd_type`, `atd_login_time`, `atd_logout_time`, `atd_remarks` from `attendance` where `attendance`.`emp_code` in ('EMP0001', 'EMP0006', 'EMP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', '<PERSON>MP0038', '<PERSON>MP0039', '<PERSON>MP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', '<PERSON>MP0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', '<PERSON>MP0062', '<PERSON>MP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1715062, "duration": 0.00962, "duration_str": "9.62ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 68.981, "width_percent": 23.441}, {"sql": "select `emp_code`, MA<PERSON>(created_at) as last_call_time, MI<PERSON>(created_at) as first_call_time, SUM(CASE WHEN call_status != 4 THEN grand_total ELSE 0 END) as total_grand_total from `call_master` where `call_master`.`emp_code` in ('<PERSON>MP0001', 'EMP0006', '<PERSON>MP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', '<PERSON>MP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', '<PERSON>MP0043', '<PERSON>MP0044', '<PERSON>MP0045', '<PERSON>MP0046', '<PERSON>MP0047', '<PERSON><PERSON>0048', '<PERSON><PERSON>0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', '<PERSON>MP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', 'EMP0062', 'EMP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' group by `emp_code`", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.184305, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 92.422, "width_percent": 2.9}, {"sql": "select * from `designation_master` where `designation_master`.`dm_id` in (5, 8, 9, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1889548, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 95.322, "width_percent": 0.975}, {"sql": "select * from `emp_state_relation` where `emp_state_relation`.`emp_code` in ('<PERSON>MP0001', '<PERSON>MP0006', '<PERSON>MP0007', 'EMP0008', 'EMP0009', 'EMP0010', 'EMP0012', 'EMP0013', '<PERSON>MP0014', 'EMP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', 'EMP0051', 'EMP0052', 'EMP0053', 'EMP0055', 'EMP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', '<PERSON>MP0061', 'EMP0062', '<PERSON>MP0064', '<PERSON>MP0065', 'NXC1234')", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.193971, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 96.296, "width_percent": 1.852}, {"sql": "select * from `state_master` where `state_master`.`state_id` in (7, 13, 14, 21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.198396, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:108", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=108", "ajax": false, "filename": "DashboardController.php", "line": "108"}, "connection": "gopi_new", "explain": null, "start_percent": 98.148, "width_percent": 0.95}, {"sql": "select `state_name`, `state_id` from `state_master`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 122}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.20246, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:122", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=122", "ajax": false, "filename": "DashboardController.php", "line": "122"}, "connection": "gopi_new", "explain": null, "start_percent": 99.098, "width_percent": 0.902}]}, "models": {"data": {"App\\Models\\EmployeeMaster": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmployeeMaster.php&line=1", "ajax": false, "filename": "EmployeeMaster.php", "line": "?"}}, "App\\Models\\EmpStateRelation": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmpStateRelation.php&line=1", "ajax": false, "filename": "EmpStateRelation.php", "line": "?"}}, "App\\Models\\DesignationMaster": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FDesignationMaster.php&line=1", "ajax": false, "filename": "DesignationMaster.php", "line": "?"}}, "App\\Models\\StateMaster": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FStateMaster.php&line=1", "ajax": false, "filename": "StateMaster.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 101, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-487483493 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-487483493\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-695777638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-695777638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1022219447 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1022219447\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; sfm_session=eyJpdiI6IjloODFXRkxqVFlDcmRsSzVPQy9rTFE9PSIsInZhbHVlIjoiVHJSUzhvOXVkNmpoWHJtZWtkR2NhZEtLcUhHTUlmTjVhczNBejRwTlVJeGVGZ0pKTTRYWTYvVU1VTktzTmxoWWplLzdLc0lqM1pKY2plb0s1T3lnRnpaNkhZSjVZbWY0a2kyRGdSNXdOSDNrSWRqWGNVNFovVTBmbUNBNlUvbm0iLCJtYWMiOiIxMzA4ZDE2Y2VmZDlkZDY5NjRkMTg2MzBjZjY1ODFhMjkzYmM0MzQyZjY3YjZmY2NiZDhjNzEzYjlhYmRlYmNhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjVPcldhTlBGZlRzWmwxOXBDOVpVakE9PSIsInZhbHVlIjoiaSthZ2IwRXFQbTZEeXgxMVczZk9OMW45dm13WE01MFpIYktuVHY5NmpDZHJkcWFqUnFSeUpxUXEzMlc3VDIyeEk2TCtrbUFzRVMwQ2ZLNTVib3pROFl0M2FqaVIya1Z3OVhYbFI2STNEZUFvQW1xZDhXMHVpT3V5WC9ERTZlZHgiLCJtYWMiOiI1YjExZWExZTk2ZWU3MTg1Njk4Nzg4NzdjYmI0NzE2ZGQxMjc3ZTc3MTI1MTRmMjU3OGU5NzNmMmZkZGM2NjgzIiwidGFnIjoiIn0%3D; qserve_session=eyJpdiI6InJFT3VUeGhXK1FycldscnV3RThIM0E9PSIsInZhbHVlIjoia2lrRXNtQ0VqZCt1d1Q3dWUvUURFdmJYZ1RRU3pDQnZLRDRZQU1BUFRpU2Z2MFpwZWxMVmNZOUd6Mm9sRll0VEpwUzNqNzF3NUd3OXZ5QUs4aWVlTmZYRUh3d1VRWTd0U3hFQ1VRRFNhQ29xZGVWaHhTV2tDMVUyRnBCUlhhR0YiLCJtYWMiOiIwMmU2ZjAxZmVjMzBiMjA2NDAxMWMyYjlkNTY2ZGI5OTEzOWEzNGJiZTgxYTA4MDZhODVjM2FlZTc3OTNkNGRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-861182568 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861182568\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1642474614 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:43:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6ImMydUtzYzY2UGgxYnQxckJzY1hvMUE9PSIsInZhbHVlIjoieFRCNDFTL2lEenhiUTBNRUF1ZmpUUGVzcWVQaW9HelM4djVaZ2FCZVN6ajE3TWhWTVo2T3RCOE90YWVFeFNlQ1hXUUdtZC9yek0xeXZtM0JLMHZ4aHdvR3Y5NW04WDhmTU15MktmU1M3d0JVUHhuZEdoa0VmZlpZUTEwcDFFek4iLCJtYWMiOiIwYTczZGUzMDc3NTBmYjhjYTkxNDU1NDg4ZGI2NzBkOGZlMGVjYjllZDRkOTY3NjY0NmY5YzQwZjYzMTc1MmI1IiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:43:01 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IkQrdWdwdEsyUkVJWDdQWWJXNUdBbVE9PSIsInZhbHVlIjoicm9WS283b1pBQjZaOWcvUTZmbnFGdmtEQXdEcUNDVmgwSEwzeFRFK0hzQldjRVcvMlU4VkRGQjV5a1VrdjNNR0ZiWVNDNC96dDczeU55ODJNZjd5M3lWNHl1UWF4Y2trNVd0VDQ4dkhMc3RFVlBoWndjdGc0R1pDaTkrSnpSbXQiLCJtYWMiOiJhOGYyMjMwZTkwMzM5OTJmYWM0MmQ1MjNmMzEzNTJlYWZmNzQ2NWZjNGQ4ZGZhOGFjNWQyODA0YjMyNTA5MjZlIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:43:01 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImMydUtzYzY2UGgxYnQxckJzY1hvMUE9PSIsInZhbHVlIjoieFRCNDFTL2lEenhiUTBNRUF1ZmpUUGVzcWVQaW9HelM4djVaZ2FCZVN6ajE3TWhWTVo2T3RCOE90YWVFeFNlQ1hXUUdtZC9yek0xeXZtM0JLMHZ4aHdvR3Y5NW04WDhmTU15MktmU1M3d0JVUHhuZEdoa0VmZlpZUTEwcDFFek4iLCJtYWMiOiIwYTczZGUzMDc3NTBmYjhjYTkxNDU1NDg4ZGI2NzBkOGZlMGVjYjllZDRkOTY3NjY0NmY5YzQwZjYzMTc1MmI1IiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:43:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IkQrdWdwdEsyUkVJWDdQWWJXNUdBbVE9PSIsInZhbHVlIjoicm9WS283b1pBQjZaOWcvUTZmbnFGdmtEQXdEcUNDVmgwSEwzeFRFK0hzQldjRVcvMlU4VkRGQjV5a1VrdjNNR0ZiWVNDNC96dDczeU55ODJNZjd5M3lWNHl1UWF4Y2trNVd0VDQ4dkhMc3RFVlBoWndjdGc0R1pDaTkrSnpSbXQiLCJtYWMiOiJhOGYyMjMwZTkwMzM5OTJmYWM0MmQ1MjNmMzEzNTJlYWZmNzQ2NWZjNGQ4ZGZhOGFjNWQyODA0YjMyNTA5MjZlIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:43:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642474614\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1669756591 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669756591\", {\"maxDepth\":0})</script>\n"}}