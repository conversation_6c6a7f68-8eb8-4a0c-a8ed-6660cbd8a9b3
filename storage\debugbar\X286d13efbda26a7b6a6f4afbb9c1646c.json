{"__meta": {"id": "X286d13efbda26a7b6a6f4afbb9c1646c", "datetime": "2025-06-03 13:11:58", "utime": **********.497967, "method": "GET", "uri": "/sfm_v2_laravel/public/fetchRoutePlanApprNotifications", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748936517.926372, "end": **********.497992, "duration": 0.571619987487793, "duration_str": "572ms", "measures": [{"label": "Booting", "start": 1748936517.926372, "relative_start": 0, "end": **********.354351, "relative_end": **********.354351, "duration": 0.4279789924621582, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.354369, "relative_start": 0.4279968738555908, "end": **********.497994, "relative_end": 1.9073486328125e-06, "duration": 0.14362502098083496, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36341800, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET fetchRoutePlanApprNotifications", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@fetchRoutePlanApprNotifications", "namespace": null, "prefix": "", "where": [], "as": "fetchRoutePlanApprNotifications", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=456\" onclick=\"\">app/Http/Controllers/DashboardController.php:456-492</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00255, "accumulated_duration_str": "2.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.4549801, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 26.667}, {"sql": "select * from `beat_datewise_master` where `bdm_status` = 0 order by `created_at` desc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 463}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 459}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4694371, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:463", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 463}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=463", "ajax": false, "filename": "DashboardController.php", "line": "463"}, "connection": "gopi_new", "explain": null, "start_percent": 26.667, "width_percent": 73.333}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/fetchRoutePlanApprNotifications", "status_code": "<pre class=sf-dump id=sf-dump-1072316395 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1072316395\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-845946928 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-845946928\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-582840318 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-582840318\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6InMwZENGaFFDL1FCejVLenZJRFkyY0E9PSIsInZhbHVlIjoiYmswc055MmJnUGhDWnJrUFppek9yNngrYnhOd2RXd3U4QjVadDhtdVZIS2E1ejNhRTdDQ2hQK2JOK01oZXhCdUtIZnNveFZTekVESFVLZ2NzWWlmM3k4b3oxNGE5UU5hRzJwSVQyTTZzb1lYMVVsRFpvaWpaQU1CRitxWUpnYUoiLCJtYWMiOiJjZDQ0OTI5MmM2NmFiYmUwODdjY2YyMDNkNDZjNzM4ZjhkNjZmMzEzYzliZDhjM2NkNTJkNTU1ZmQ1MGQzODJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlnWGtLVTZya1Bqamo1dnVBajltMkE9PSIsInZhbHVlIjoiellUQ3hNWDYwVnFLS3B5YXUvdG5NbjRMU1o3c3V3SzNTMHVXY1lHdGpRQUlYMzRjMGxONWlGYTVrOFBXK3lKUkR5OXVrVXVhTFVId1J2WFdiRFY4cVVTVHh3U01tZms4d3pqZUNObjl1RWRSdk1IK21pNm5TOEVFMjBmcnJnVnYiLCJtYWMiOiI2ZDIwNTdmZDZhMmIxOGNjN2I5YmNjNjhhZGFhZDkwZjAxZTE4NDRmMzQxZGE3ZGM4OGEwZTRiNTQ3ODJjNDI0IiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6InlQczZDdURhcXZDV0xRM0tQWlNNeFE9PSIsInZhbHVlIjoiNW40UFNEdW5DalFQOTVSNXZsZWZhd2tSdnZnOWhnTkJlY3VneUZSWUVLUENNV3RqNFFiMEp1NS9tK1h2Z2lLVzVnSXVzamZWR2Irc0RsVTh1TDMvdE5mOE1zUkNxRVZzRDBkRUVHK2MrU2pQakRWZE9lQXBlVXlCRWpZeUdzU3YiLCJtYWMiOiIyMmU5YTI5YzdkNjFmZDdmOTQzZDZkNDVjMmJlMDc1NzlmMzhjNGEzZTkyMWE4MjA4OTdkNGE5OGMyZjliMGFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-704300424 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704300424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1824856312 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:41:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6ImpDOU1GZ09sbklJMHcrbWxXTXdFRXc9PSIsInZhbHVlIjoiRUViZ1BBSURJZmJjWXZkQ3ZWdVNyWGt5MmpyNHdSTnlVeWJ3YmNJaVF2b2Nqakh2WVFlQ0Z4Mm1YVWZzenViVXkxWlRVYTNqM29heVRwdm5VbHBPcEZER1dzVW1GSzVXUFhKanZ4Ym4rbmUxT3F3NjFYMmhrVnR6WnNzbTdFVlMiLCJtYWMiOiIzMGU3MTQ1ZmYxNDM5MzA5OTg3YTgyZDc2YzUzM2VmOWI4YzMxOWE1MTQxODMyMjVmNmY3MTA0OGEyYjdlMDY0IiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:41:58 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6ImxldG9mamlhMUJ2Q1VmRXJIYVNSRVE9PSIsInZhbHVlIjoicm1pT3hxRkplNUJlNzFrUmVpSWxTT0F4djUvWE9TQjFPZlQySUtkdWpTenBReVB5Z1pySWRVeXowM004MnhKQ2dDS1hqK20raDFrUENmZFVrUi84akx5MFhSdVBYTkNDTC8vUU9GSS8veHNYSDg0MEJRVUJhK3ltSXZvenRBTjkiLCJtYWMiOiI1MmYzYTU0ZDhiNzQxMDY1ODk2NzBkMjYzNTYwMzU0YzBmNDk0ZWNhOGNkNGRhZDkzMzExYTUwMmM3ZTY0M2JkIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:41:58 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpDOU1GZ09sbklJMHcrbWxXTXdFRXc9PSIsInZhbHVlIjoiRUViZ1BBSURJZmJjWXZkQ3ZWdVNyWGt5MmpyNHdSTnlVeWJ3YmNJaVF2b2Nqakh2WVFlQ0Z4Mm1YVWZzenViVXkxWlRVYTNqM29heVRwdm5VbHBPcEZER1dzVW1GSzVXUFhKanZ4Ym4rbmUxT3F3NjFYMmhrVnR6WnNzbTdFVlMiLCJtYWMiOiIzMGU3MTQ1ZmYxNDM5MzA5OTg3YTgyZDc2YzUzM2VmOWI4YzMxOWE1MTQxODMyMjVmNmY3MTA0OGEyYjdlMDY0IiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:41:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6ImxldG9mamlhMUJ2Q1VmRXJIYVNSRVE9PSIsInZhbHVlIjoicm1pT3hxRkplNUJlNzFrUmVpSWxTT0F4djUvWE9TQjFPZlQySUtkdWpTenBReVB5Z1pySWRVeXowM004MnhKQ2dDS1hqK20raDFrUENmZFVrUi84akx5MFhSdVBYTkNDTC8vUU9GSS8veHNYSDg0MEJRVUJhK3ltSXZvenRBTjkiLCJtYWMiOiI1MmYzYTU0ZDhiNzQxMDY1ODk2NzBkMjYzNTYwMzU0YzBmNDk0ZWNhOGNkNGRhZDkzMzExYTUwMmM3ZTY0M2JkIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:41:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824856312\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1581605377 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581605377\", {\"maxDepth\":0})</script>\n"}}