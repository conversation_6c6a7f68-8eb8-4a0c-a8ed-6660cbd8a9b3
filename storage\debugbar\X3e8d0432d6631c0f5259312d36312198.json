{"__meta": {"id": "X3e8d0432d6631c0f5259312d36312198", "datetime": "2025-06-03 13:12:01", "utime": **********.706241, "method": "GET", "uri": "/sfm_v2_laravel/public/fetchRoutePlanApprNotifications", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.243456, "end": **********.706268, "duration": 0.4628121852874756, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.243456, "relative_start": 0, "end": **********.633458, "relative_end": **********.633458, "duration": 0.3900020122528076, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.633475, "relative_start": 0.39001917839050293, "end": **********.706271, "relative_end": 2.86102294921875e-06, "duration": 0.07279586791992188, "duration_str": "72.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36255896, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET fetchRoutePlanApprNotifications", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@fetchRoutePlanApprNotifications", "namespace": null, "prefix": "", "where": [], "as": "fetchRoutePlanApprNotifications", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=456\" onclick=\"\">app/Http/Controllers/DashboardController.php:456-492</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00173, "accumulated_duration_str": "1.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.683847, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.68775, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/fetchRoutePlanApprNotifications", "status_code": "<pre class=sf-dump id=sf-dump-851303056 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-851303056\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1073427242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1073427242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2080706973 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2080706973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6InMwZENGaFFDL1FCejVLenZJRFkyY0E9PSIsInZhbHVlIjoiYmswc055MmJnUGhDWnJrUFppek9yNngrYnhOd2RXd3U4QjVadDhtdVZIS2E1ejNhRTdDQ2hQK2JOK01oZXhCdUtIZnNveFZTekVESFVLZ2NzWWlmM3k4b3oxNGE5UU5hRzJwSVQyTTZzb1lYMVVsRFpvaWpaQU1CRitxWUpnYUoiLCJtYWMiOiJjZDQ0OTI5MmM2NmFiYmUwODdjY2YyMDNkNDZjNzM4ZjhkNjZmMzEzYzliZDhjM2NkNTJkNTU1ZmQ1MGQzODJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktaUG5CTWVFcWtXTDBqWEs1MEVPenc9PSIsInZhbHVlIjoiQ0lPWU5qVVFxNDFJS2RqZ2NiZjdKNGRwektaU0FzNU9mdEJjUEZQMS9QQzk2VXNxNFF3b0VjeWNSc1paTHBVWEErVGM2UVNLQ25HY2NnS29Ebm9DMWRPRmRyRlJkMnRnOThjbkVTcGNucmdNMTRWd2lNV01rRk5VYXMvT3dhMEMiLCJtYWMiOiIyNjY1OTlhYzdmNTUzNzYwNjU0YTcyNWU2YzdjNTFlNTBmOTVmZjcwOTQ2ZTgwOTcwNjIyNTQ2ZWVjMzNmYjJhIiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6IkRqU0ptQ2xUd2ZxLzltM3FzWnd6OUE9PSIsInZhbHVlIjoiWGVHU2NCTzRLTDdmVFJkMkFRVmJnMEZ2cUxsUjI4b1NBWGpYTGJuZkUyY1U4R2FKU2Qvd3J5WDRvektvdndvSUhhL2RlYk1reEs5TDVLTEV2MDBjU3UyeHlPV3QwQ0VVYnd6eFZGdFhRUmhDOXIrVjlKRC95QTZRRjlFaGV0R1QiLCJtYWMiOiIwNzc4Y2IzN2Y3YjY1YTFhZDRlYTA0N2YzZjE2NTUyZDM3NzQzMzQ3YWJjMDU3YzdhNjQ3MjlmNjQ2ZTcxNTAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-816625699 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816625699\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-426576379 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:42:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6InA4RW9LbkxwQUw4eDJzM2ZVd3ZSbUE9PSIsInZhbHVlIjoiM0w5N0krZjRqRHlpczFOM1pVayt2TUJFdmQ1Z2RRY1U4bVVqNXNnYXdnaEpzRXVxbzJ5WXJicVRySC9zNXpmbnJ2K1hOb1M5c0lmREFmY29yVWVjTTBxcTZNRmw5RmJkSE10TndDZTZNVmorNFg2bjVFZXRqYk9aalFwcVl4L3MiLCJtYWMiOiIwMTRjNThmMGEwZGNjZmYzNGQxYzI0NjVkZDYxOGIyNTZjMjdkYjA3MGU5YmM3YmYxYWVjMDJiYjE5NTQ4NzIzIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:42:01 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IndZc0NjOHU4U3F1T2JrR2N5Y3BjTmc9PSIsInZhbHVlIjoiTk9mazhxVFhoL2g5WHVmUGNya3U0WWlpZHpXWnVWYThlbWpkK2xwTXVTRW5ScjI3Qmg1ZlhIbHQ0elc5UXk4Y2U3WWdzWmx1aEk1UEFTNk5sOTloVDhRbU4rcUIxRy9wVDZYUkV1TDV4dGNpcGxIR0FKRWU3cExZY3g0NDJZelUiLCJtYWMiOiI3ODkzMDZjNjExZTcxMzllNmZlMzU0MTg5YWUxOGEwNjUyMTNkNTFmY2RmOGQyMTBlMGU1YmVhMWYwYWUwNGYwIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:42:01 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InA4RW9LbkxwQUw4eDJzM2ZVd3ZSbUE9PSIsInZhbHVlIjoiM0w5N0krZjRqRHlpczFOM1pVayt2TUJFdmQ1Z2RRY1U4bVVqNXNnYXdnaEpzRXVxbzJ5WXJicVRySC9zNXpmbnJ2K1hOb1M5c0lmREFmY29yVWVjTTBxcTZNRmw5RmJkSE10TndDZTZNVmorNFg2bjVFZXRqYk9aalFwcVl4L3MiLCJtYWMiOiIwMTRjNThmMGEwZGNjZmYzNGQxYzI0NjVkZDYxOGIyNTZjMjdkYjA3MGU5YmM3YmYxYWVjMDJiYjE5NTQ4NzIzIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:42:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IndZc0NjOHU4U3F1T2JrR2N5Y3BjTmc9PSIsInZhbHVlIjoiTk9mazhxVFhoL2g5WHVmUGNya3U0WWlpZHpXWnVWYThlbWpkK2xwTXVTRW5ScjI3Qmg1ZlhIbHQ0elc5UXk4Y2U3WWdzWmx1aEk1UEFTNk5sOTloVDhRbU4rcUIxRy9wVDZYUkV1TDV4dGNpcGxIR0FKRWU3cExZY3g0NDJZelUiLCJtYWMiOiI3ODkzMDZjNjExZTcxMzllNmZlMzU0MTg5YWUxOGEwNjUyMTNkNTFmY2RmOGQyMTBlMGU1YmVhMWYwYWUwNGYwIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:42:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426576379\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1999443887 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999443887\", {\"maxDepth\":0})</script>\n"}}