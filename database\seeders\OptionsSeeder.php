<?php

namespace Database\Seeders;

use App\Models\Options;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OptionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            ['key' => 'company_name', 'value' => 'NXC'],
            ['key' => 'logo', 'value' => 'nxclogo.png'],
            ['key' => 'ordering_type', 'value' => 'full'],
            ['key' => 'banner', 'value' => 'banner1.jpg,banner2.jpg'],
            ['key' => 'ss_prefix', 'value' => 'SS0001'],
            ['key' => 'distributor_prefix', 'value' => 'DIST0001'],
            ['key' => 'dealer_prefix', 'value' => 'DL0001'],
            ['key' => 'retailer_prefix', 'value' => 'RT0001'],
            ['key' => 'invoice_prefix', 'value' => 'IV'],
            ['key' => 'packaging_charge', 'value' => '0'],
            ['key' => 'accepted_accuracy', 'value' => '500'],
            ['key' => 'shop_images', 'value' => '0'], //0-all optional, 1-one compulsory,2-two compulsory,3-three compulsory
            ['key' => 'order_images', 'value' => '1'], // if true then considered and false then not
            ['key' => 'exp_min_amount_req_image', 'value' => '500'], //expense image required upload if value is >= 500
            ['key' => 'employee_prefix', 'value' => 'EMP0001'],
            ['key' => 'beat_prefix', 'value' => 'BM0001'],
            ['key' => 'add_retailer', 'value' => '0'],
            ['key' => 'customer_types', 'value' => json_encode([
                'ss' => [
                    'name' => 'Super Stockist',
                    'status' => 1
                ],
                'distributor' => [
                    'name' => 'Distributor',
                    'status' => 1
                ],
                'dealer' => [
                    'name' => 'Dealer',
                    'status' => 1
                ],
                'retailer' => [
                    'name' => 'Retailer',
                    'status' => 1
                ],
            ])],
            ['key' => 'show_payment', 'value' => '1'],
            ['key' => 'show_expense', 'value' => '1'],
            ['key' => 'show_leave', 'value' => '1'],
            ['key' => 'show_complain', 'value' => '1'],
            ['key' => 'show_report', 'value' => '1'],
            ['key' => 'add_dealer', 'value' => '1'],
            ['key' => 'add_distributor', 'value' => '1'],
            ['key' => 'add_beat', 'value' => '1'],
            ['key' => 'latest_app_version', 'value' => '1.0.0'],
            ['key' => 'tc_pc_chart', 'value' => '1'],
            ['key' => 'background_image', 'value' => 'bg4.jpg'],
            ['key' => 'language_button', 'value' => '0'],
            ['key' => 'add_route_plan', 'value' => '0'],
            ['key' => 'beat_request_auto_approve', 'value' => '0'],
            ['key' => 'route_plan_request_auto_approve', 'value' => '0'],
            ['key' => 'show_team_report', 'value' => '1'],
            ['key' => 'app_sales_calculation', 'value' => '1'],
            ['key' => 'favicon_icon', 'value' => 'sfm_favicon.jpeg'],
            ['key' => 'debugbar', 'value' => '0'],
            ['key' => 'traccar', 'value' => 'traccar json here'],
            ['key' => 'wa_appkey', 'value' => '0'],
            ['key' => 'wa_authkey', 'value' => '0'],
            ['key' => 'new_apk', 'value' => 'new apk link here'],
            ['key' => 'route_plan_max_fix_beat_allow', 'value' => '25'],
        ];
        foreach ($data as $item) {
            Options::updateOrCreate(
                ['key' => $item['key']],
                [
                    'value' => $item['value']
                ]
            );
        }
    }
}
