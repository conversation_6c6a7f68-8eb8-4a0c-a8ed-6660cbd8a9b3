<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithTitle;

class AttendanceSummaryExport implements FromArray, WithHeadings, WithEvents, WithCustomStartCell, WithTitle
{
    protected $data;
    protected $title;

    public function __construct($data, $title)
    {
        $this->data = $data;
        $this->title = $title;
    }

    public function array(): array
    {
        $rows = [];

        $totalCalls = 0;
        $totalWorkingDays = 0;
        $totalSecondarySales = 0;

        foreach ($this->data as $item) {
            $rows[] = [
                $item->empCode,
                $item->empName,
                $item->stateName,
                $item->totalCalls,
                $item->workingDays,
                ceil($item->secondarySales), // Round up secondary sales
                ceil($item->dailyCallAverage), // Round up daily call average
                ceil($item->dailySaleAverage), // Round up daily sale average
            ];

            $totalCalls += $item->totalCalls;
            $totalWorkingDays += $item->workingDays;
            $totalSecondarySales += $item->secondarySales; // Keep original for calculation
        }

        // Calculate the correct overall averages
        $overallDailyCallAvg = ($totalWorkingDays > 0) ? ceil($totalCalls / $totalWorkingDays) : 0;
        $overallDailySaleAvg = ($totalWorkingDays > 0) ? ceil($totalSecondarySales / $totalWorkingDays) : 0;

        // Add a total row with rounded values
        $rows[] = [
            '',
            'TOTAL',
            '',
            $totalCalls,
            $totalWorkingDays,
            ceil($totalSecondarySales), // Round up the total secondary sales
            $overallDailyCallAvg,
            $overallDailySaleAvg,
        ];

        return $rows;
    }

    public function headings(): array
    {
        return [
            'Employee Code',
            'Employee Name',
            'States',
            'Total Calls',
            'Working Days',
            'Secondary Sales',
            'Daily Call Average',
            'Daily Sale Average',
        ];
    }

    public function startCell(): string
    {
        return 'A3'; // Headings will start at row 3
    }

    public function title(): string
    {
        return 'Employee Summary';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Add title in row 1 and merge across all columns
                $event->sheet->setCellValue('A1', $this->title);
                $event->sheet->mergeCells('A1:H1');

                // Style the title
                $event->sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
                $event->sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                $event->sheet->getRowDimension(1)->setRowHeight(25);

                // Row 2 is left blank

                // Style the headings in row 3
                $event->sheet->getStyle('A3:H3')->getFont()->setBold(true);

                // Find the last row with data
                $lastRow = $event->sheet->getHighestRow();

                // Style the totals row
                $event->sheet->getStyle('A' . $lastRow . ':H' . $lastRow)->getFont()->setBold(true);
                $event->sheet->getStyle('B' . $lastRow)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
                $event->sheet->getStyle('A' . $lastRow . ':H' . $lastRow)->getBorders()->getTop()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

                // Auto fit column widths
                foreach (range('A', 'H') as $column) {
                    $event->sheet->getColumnDimension($column)->setAutoSize(true);
                }

                // Format number columns with specific formats
                $numRows = count($this->data) + 1; // +1 for the totals row

                // Format Total Calls and Working Days as integers (no decimals)
                $event->sheet->getStyle('D4:D' . (3 + $numRows))->getNumberFormat()->setFormatCode('#,##0');
                $event->sheet->getStyle('E4:E' . (3 + $numRows))->getNumberFormat()->setFormatCode('#,##0');

                // Format Secondary Sales with decimals
                $event->sheet->getStyle('F4:F' . (3 + $numRows))->getNumberFormat()->setFormatCode('#,##0');

                // Format averages with 2 decimal places
                $event->sheet->getStyle('G4:H' . (3 + $numRows))->getNumberFormat()->setFormatCode('#,##0');
            },
        ];
    }
}
