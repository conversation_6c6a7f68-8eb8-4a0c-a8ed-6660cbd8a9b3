<style type="text/css">
    .col-lg-6,
    .col-lg-12,
    .col-xl-6,
    .col-xl-12 {
        float: left;
    }

    .summary-total {
        color: #333333;
        font-weight: bold;
        border: 2px solid #e0e0e0;
        border-radius: 4px;
        padding: 2px;
    }
</style>
<!-- Page -->
<div class="page">
    <div class="page-content container-fluid " id="dashboard_infobox ">
        <?php if($message = Session::get('success')): ?>
            <div class="alert alert-success alert-block" id="success-alert">
                <button type="button" class="close" data-dismiss="alert">×</button>
                <strong><?php echo e($message); ?></strong>
            </div>
        <?php endif; ?>
        <?php echo $__env->make('admin.dashboard_box_content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title"><?php echo e(__('Daily Attendance Report')); ?> - <?php echo e($currentDate); ?> - <?php echo e($currentDay); ?>

                </h3>
            </div>
            <div class="panel-body p-0">
                <div id="dashboard_list_table"></div>
            </div>
        </div>
        <button onclick="window.location='<?php echo e(route('dashboard_charts')); ?>'"
            class="btn btn-primary btn-sm waves-effect waves-classic"
            style="font-size: 15px;"><?php echo e(__('Load Dashboard')); ?></button>
    </div>
</div>
</div>
<script type="text/javascript">
    mark_sidebar_active("dashboard");

    var dataGrid;
    $(document).ready(function() {
        var filterBuilder,
            filterButton;

        const dashboardData = <?php echo json_encode($dashboardList) ?>;

        const AutoInId = dashboardData.map((item, index) => {
            return {
                ...item,
                autoIncrementId: index + 1
            };
        });

        dataGrid = $("#dashboard_list_table").dxDataGrid({
            dataSource: {
                store: AutoInId,
            },
            // columnHidingEnabled: true,
            allowColumnResizing: true,
            columnAutoWidth: true,
            showRowLines: true,
            rowAlternationEnabled: true,
            showBorders: true,
            filterRow: {
                visible: true
            },
            columnChooser: {
                enabled: true,
                mode: "select"
            },
            columnFixing: {
                enabled: true
            },
            export: {
                enabled: true,
            },
            paging: {
                pageSize: 10
            },
            pager: {
                showPageSizeSelector: false,
                showNavigationButtons: true,
                allowedPageSizes: [5, 10, 20],
                showInfo: true
            },
            headerFilter: {
                visible: true
            },
            columns: [{
                    dataField: "autoIncrementId",
                    caption: "#",
                    dataType: "autoinc",
                    fixed: true,
                    allowFiltering: false,
                    width: 40
                },
                {
                    dataField: "emp_code",
                    caption: "<?php echo e(__('Employee Code')); ?>",
                },
                {
                    dataField: "emp_name",
                    caption: "<?php echo e(__('Employee Name')); ?>",
                },
                {
                    dataField: "emp_mobile",
                    caption: "<?php echo e(__('Mobile')); ?>",
                },
                {
                    dataField: "designation.dm_name",
                    caption: "<?php echo e(__('Designation')); ?>",
                },
                {
                    dataField: "attendance.atd_type",
                    caption: "<?php echo e(__('Action Type')); ?>",
                    calculateCellValue: function(data) {
                        if (data.attendance && data.attendance.atd_type !== null) {
                            switch (data.attendance.atd_type) {
                                case 1:
                                    return "Solo";
                                case 2:
                                    return "Join";
                                case 3:
                                    return "Other";
                                case 4:
                                    return "Absent";
                                default:
                                    return;
                            }
                        } else {
                            return "";
                        }
                    }
                },
                {
                    dataField: "attendance.atd_remarks",
                    caption: "<?php echo e(__('Remarks')); ?>",
                },
                {
                    dataField: "state_names",
                    caption: "<?php echo e(__('State')); ?>",
                },
                {
                    dataField: "attendance.atd_login_time",
                    caption: "<?php echo e(__('Login Time')); ?>",
                    calculateCellValue: function(data) {
                        // Extracting time part from the datetime string
                        if (data.attendance && data.attendance.atd_login_time) {
                            return data.attendance.atd_login_time.split(' ')[1];
                        } else {
                            return "Not Login"; // Or any default value you prefer
                        }
                    }
                },
                // {
                //     dataField: "first_call",
                //     caption: "FC Time",
                // },
                {
                    dataField: "total_call.first_call_time",
                    caption: "<?php echo e(__('FC Time')); ?>",
                    headerCellTemplate: function(header, info) {
                        header.append($('<div>').text('FC Time'));
                        header.attr('title', 'First Call Time');
                    },
                    calculateCellValue: function(data) {
                        // Extracting time part from the datetime string
                        if (data.total_call && data.total_call.first_call_time) {
                            return data.total_call.first_call_time.split(' ')[1];
                        } else {
                            return ""; // Or any default value you prefer
                        }
                    }
                },
                {
                    dataField: "total_call.last_call_time",
                    caption: "<?php echo e(__('LC Time')); ?>",
                    headerCellTemplate: function(header, info) {
                        header.append($('<div>').text('LC Time'));
                        header.attr('title', 'Last Call Time');
                    },
                    calculateCellValue: function(data) {
                        // Extracting time part from the datetime string
                        if (data.total_call && data.total_call.last_call_time) {
                            return data.total_call.last_call_time.split(' ')[1];
                        } else {
                            return ""; // Or any default value you prefer
                        }
                    }
                },
                {
                    dataField: "attendance.atd_logout_time",
                    caption: "<?php echo e(__('Logout Time')); ?>",
                    calculateCellValue: function(data) {
                        // Extracting time part from the datetime string
                        if (data.attendance && data.attendance.atd_logout_time) {
                            return data.attendance.atd_logout_time.split(' ')[1];
                        } else {
                            return ""; // Or any default value you prefer
                        }
                    }
                },
                {
                    dataField: "total_call_count",
                    caption: "<?php echo e(__('TC')); ?>",
                    dataType: "tc",
                    headerCellTemplate: function(header, info) {
                        header.append($('<div>').text('TC'));
                        header.attr('title', 'Total Call');
                    },
                    summaryType: "sum", // Add this line
                    summaryCellTemplate: function(cellElement, cellInfo) {
                        cellElement.text("Sum: " + cellInfo.value);
                    }
                },
                {
                    dataField: "total_product_call",
                    caption: "<?php echo e(__('PC')); ?>",
                    dataType: "pc",
                    headerCellTemplate: function(header, info) {
                        header.append($('<div>').text('PC'));
                        header.attr('title', 'Productive Call');
                    },
                    summaryType: "sum", // Add this line
                    summaryCellTemplate: function(cellElement, cellInfo) {
                        cellElement.text("Sum: " + cellInfo.value);
                    }
                },
                {
                    dataField: "total_call.total_grand_total",
                    caption: "<?php echo e(__('Total Sell')); ?>",
                    dataType: "totalsell",
                    summaryType: "sum", // Add this line
                    summaryCellTemplate: function(cellElement, cellInfo) {
                        cellElement.text("Sum: " + cellInfo.value);
                    }
                },
            ],
            summary: { // Add this block
                totalItems: [{
                        column: "total_call_count",
                        summaryType: "sum",
                        displayFormat: "{0}",
                        cssClass: "summary-total"
                    },
                    {
                        column: "total_product_call",
                        summaryType: "sum",
                        displayFormat: "{0}",
                        cssClass: "summary-total"
                    },
                    {
                        column: "total_call.total_grand_total",
                        summaryType: "sum",
                        valueFormat: {
                            type: "fixedPoint",
                            precision: 2
                        },
                        displayFormat: "{0}",
                        cssClass: "summary-total"
                    }
                ]
            }
        }).dxDataGrid('instance');
    });
</script>
<script>
    setTimeout(function() {
        document.getElementById('success-alert')?.remove();
        document.getElementById('error-alert')?.remove();
    }, 10000);
</script>
</body>
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\sfm_v2_laravel\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>