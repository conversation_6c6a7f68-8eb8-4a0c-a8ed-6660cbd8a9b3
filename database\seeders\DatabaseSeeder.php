<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\ExpenseType;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            BankMasterSeeder::class,
            // DesignationMasterSeeder::class,
            // ExpenseTypeSeeder::class,
            // NonProductiveReasonSeeder::class,
            // CustomerCategoryMasterSeeder::class,
            StateMasterSeeder::class,
            AllDistNameDataInsertSeeder::class,
            // HqMasterSeeder::class,
            // DistrictMasterSeeder::class,
            // TownMasterSeeder::class,
            // UomMasterSeeder::class,
            // MarketTypeMasterSeeder::class,
            // CustomerMasterSeeder::class,
            OptionsSeeder::class,
            GradeMasterSeeder::class,
            PaymentConditionSeeder::class,
            PaymentTypeSeeder::class,
            UserTableSeeder::class,
        ]);
    }
}
