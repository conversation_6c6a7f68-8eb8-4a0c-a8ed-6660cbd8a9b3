<?php

namespace App\Http\Controllers;


use App\Models\BankMaster;
use App\Models\BeatMaster;
use App\Models\ClaimComplainMaster;
use App\Models\ClaimComplainTypeMaster;
use App\Models\CmAnniBirthRelation;
use App\Models\CmBeatRelation;
use App\Models\CmGradeRelation;
use App\Models\CmImageRelation;
use App\Models\CustomerEmpRelation;
use App\Models\DistrictMaster;
use App\Models\EmpLocation;
use App\Models\EmployeeMaster;
use App\Models\Attendance;
use App\Models\BeatDatewiseMaster;
use App\Models\BrandMaster;
use App\Models\CallMaster;
use App\Models\CmCategoryRelation;
use App\Models\CustomerMaster;
use App\Models\EmpNotification;
use App\Models\ExpenseMaster;
use App\Models\ExpenseType;
use App\Models\GradeMaster;
use App\Models\LanguageMaster;
use App\Models\LeaveApplication;
use App\Models\MarketTypeMaster;
use App\Models\NonProductiveReason;
use App\Models\Options;
use App\Models\OptionsNotification;
use App\Models\OrderMaster;
use App\Models\PaymentCollection;
use App\Models\PaymentCondition;
use App\Models\PaymentType;
use App\Models\PriceGroup;
use App\Models\ProductMaster;
use App\Models\ProductType;
use App\Models\CustomerCategoryMaster;
use App\Models\TownMaster;
use App\Models\UomMaster;
use App\Models\waOrderMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Carbon\Carbon; // Add this line
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use PdfReport;
use App\Traits\SendFcmMessage;

class ApiController extends Controller
{
    use SendFcmMessage;

    public function login(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required',
                'emp_pin' => 'required',
                'device_info' => 'required',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }

        $employee = EmployeeMaster::where('emp_code', $request->emp_code)->with('team_members', 'reporting_emp')->first();

        // Check if employee exists
        if (!$employee) {
            // Check if the pin matches any employee's pin
            $pinMatch = EmployeeMaster::where('emp_pin', $request->emp_pin)->exists();
            if ($pinMatch) {
                return response()->json([
                    'error' => TRUE,
                    'message' => 'Please check your employee code.'
                ], 422);
            }
            return response()->json([
                'error' => TRUE,
                'message' => 'Employee does not exist.'
            ], 422);
        }

        // Check if employee status is 0
        if ($employee->emp_status == 0) {
            return response()->json([
                'error' => TRUE,
                'message' => 'Employee is disabled.'
            ], 422);
        }

        if (!$employee || !Str::of($request->emp_pin)->exactly($employee->emp_pin)) {
            return response()->json([
                'error' => TRUE,
                'message' => 'The provided credentials are incorrect.'
            ], 422);
        }

        // store device info
        $device_info = $request->input('device_info');
        $employee->device_info = $device_info;
        $employee->save();

        //check if employee already cheked in or not for today from attendance table
        $employee->checkIn = Attendance::where('emp_code', $employee->emp_code)->whereDate('atd_login_time', Carbon::today())->first();
        $response = [
            'token' => $employee->createToken($request->emp_code, ["*"], Carbon::today()->endOfDay())->plainTextToken,
            'isCheckedIn' => $employee->checkIn ? true : false,
            'checkInType' => $employee->checkIn ? $employee->checkIn->atd_type : null,
            'employee' => $employee,

        ];
        return $response;
    }

    public function checkin(Request $request)
    {
        try {
            $request->validate([
                'checkin_type' => 'required|numeric',
                'latitude' => 'required',
                'longitude' => 'required',
                'join_emp_code' => 'required_if:checkin_type,2',
                'remarks' => 'required_if:checkin_type,3,4'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }

        $employee = auth()->user();
        $attendance = new Attendance();
        $attendance->emp_code = $employee->emp_code;
        $attendance->atd_login_time = Carbon::now();
        $attendance->atd_type = $request->checkin_type;
        $attendance->atd_login_coordinates = DB::raw("POINT(" . $request->longitude . ", " . $request->latitude . ")");
        $attendance->join_emp_code = $request->join_emp_code;
        $attendance->atd_remarks = $request->remarks;
        //check if employee already cheked in or not for today from attendance table
        $employee->checkIn = Attendance::where('emp_code', $employee->emp_code)->whereDate('atd_login_time', Carbon::today())->exists();
        if ($employee->checkIn) {
            $response = [
                'message' => 'Already checked in',
                'isCheckedIn' => true
            ];
            return $response;
        }

        $attendance->save();
        $response = [
            'message' => 'Checkin successful',
            'isCheckedIn' => true
        ];
        return $response;
    }

    public function checkout(Request $request)
    {
        try {
            $request->validate([
                'latitude' => 'required',
                'longitude' => 'required',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }

        $employee = auth()->user();
        $attendance = Attendance::where('emp_code', $employee->emp_code)
            ->whereDate('atd_login_time', Carbon::today())
            ->first();

        if (!$attendance) {
            $response = [
                'message' => 'No checkin found',
                'isCheckedIn' => false
            ];
            return $response;
        }

        if ($attendance->atd_logout_time === null) {
            $attendance->atd_logout_time = Carbon::now();
            $attendance->atd_logout_coordinates = DB::raw("POINT(" . $request->longitude . ", " . $request->latitude . ")");
            $attendance->save();
        }

        $checkOutNoti = OptionsNotification::select('title', 'message', 'status')->where('type', 'checkout_notification')->first();
        $checkYestOutNoti = OptionsNotification::select('title', 'message', 'status')->where('type', 'checkout_yesterday_total_notification')->first();

        // status 1 then send notification else not
        if ($checkOutNoti->status == 1) {

            // Calculate today's total work time from the call_master table
            $totalWorkTimeToday = CallMaster::where('emp_code', $employee->emp_code)
                ->whereDate('created_at', Carbon::today())
                ->sum('grand_total');

            // Fetch yesterday's attendance record
            $yesterdayAttendance = Attendance::where('emp_code', $employee->emp_code)
                ->whereDate('atd_login_time', Carbon::yesterday())
                ->first();

            $totalWorkTimeYesterday = 0;
            $yesterdayStatus = '';

            if ($yesterdayAttendance) {
                // Calculate yesterday's total work time from the call_master table
                $totalWorkTimeYesterday = CallMaster::where('emp_code', $employee->emp_code)
                    ->whereDate('created_at', Carbon::yesterday())
                    ->sum('grand_total');
                $yesterdayStatus = $totalWorkTimeYesterday;

                $title = $checkOutNoti->title;
                $body_message = $checkOutNoti->message . $totalWorkTimeToday . "\n";
                $body_message .= $checkYestOutNoti->message . $yesterdayStatus;
            } else {
                $title = $checkOutNoti->title;
                $body_message = $checkOutNoti->message . $totalWorkTimeToday . "\n";
            }

            $file = null; // Add file if needed

            // Send notification to the user
            $deviceInfo = json_decode($employee->device_info, true);
            if (isset($deviceInfo['fcm_token'])) {
                $fcmToken = $deviceInfo['fcm_token'];

                $checkOutResponse = $this->sendNotification($fcmToken, $title, $body_message, $file);

                $notificationStatus = ($checkOutResponse->status() === 200) ? 1 : 2;
                $responseMessage = ($notificationStatus === 1) ? 'Checkout successful and notification sent successfully' : 'Checkout successful but notification not sent: ' . $checkOutResponse->getReasonPhrase();

                EmpNotification::create([
                    'en_title' => $title,
                    'en_message' => $body_message,
                    'emp_code' => $employee->emp_code,
                    'en_status' => $notificationStatus, // 1-sent,2-failed
                    'en_failed_reason' => ($notificationStatus === 2) ? $responseMessage : null
                ]);
                return response()->json([
                    'status' => $notificationStatus === 1 ? 'success' : 'error',
                    'message' => $responseMessage
                ], $checkOutResponse->status());
            } else {
                // EmpNotification::create([
                //     'en_title' => $title,
                //     'en_message' => $body_message,
                //     'emp_code' => $employee->emp_code,
                //     'en_status' => 2, // failed
                //     'en_failed_reason' => 'FCM token not found in device info for employee: ' . $employee->emp_code,
                // ]);
                Log::error('FCM token not found in device info for employee: ' . $employee->emp_code);
            }
        } else {
            return;
        }

        $response = [
            'message' => 'Checkout successful',
            'isCheckedIn' => false
        ];
        return $response;
    }

    public function getTerritory()
    {
        // Retrieve the authenticated employee
        $employee = auth()->user();

        // Retrieve the employee's geographical information
        $employee_geomat = EmployeeMaster::with([
            'empStateRelation.state',
            'empDistRelation.district.towns', // Load towns with the district
            'empHqRelation.hq',
        ])->find($employee->emp_code);

        // Prepare lists for the response
        $state_list = $employee_geomat->empStateRelation->pluck('state');
        $hq = $employee_geomat->empHqRelation->pluck('hq');

        // Prepare lists for the districts and their towns
        $districts_with_all_dist_0 = collect();
        $districts_with_all_dist_1 = collect();
        $final_districts = collect();

        foreach ($employee_geomat->empDistRelation as $empDistRelation) {
            $district = $empDistRelation->district;

            if ($district->all_dist == 0) {
                // Add this district directly if all_dist is 0
                $districts_with_all_dist_0->push($district);
            } else {
                // Retrieve all districts for the state if all_dist is 0
                $state_id = $district->state_id;
                $all_districts = DistrictMaster::with('towns')->where('state_id', $state_id)->where('all_dist', 0)->get();

                foreach ($all_districts as $dist) {
                    $districts_with_all_dist_1->push($dist);
                }
            }
        }

        // Combine the two lists ensuring no duplicates
        $final_districts = $districts_with_all_dist_0->merge($districts_with_all_dist_1)->unique('dm_id');

        // Structure the final response
        $emp_geo = [
            'state' => $state_list,
            'district_town' => $final_districts->values(),
            'hq' => $hq,
        ];

        return $emp_geo;
    }

    public function getProductBrand()
    {
        $brandData = BrandMaster::all();

        $product_brand = [
            'product_brand' => $brandData,
        ];
        return $product_brand;
    }

    public function getProductType()
    {
        $typeData = ProductType::with('brand')->get();

        $product_type = [
            'product_type' => $typeData,
        ];
        return $product_type;
    }

    public function getUom()
    {
        $uomData = UomMaster::all();

        return response()->json(['uom' => $uomData]);
    }

    public function getPriceGroup()
    {
        $employee = auth()->user();
        $priceGroupData = PriceGroup::with('product')->where('market_type', $employee->market_type)->get();

        return response()->json(['price_group' => $priceGroupData]);
    }

    public function getProduct()
    {
        $employee = auth()->user();
        $marketType = $employee->market_type;

        // Get products and join with price_group, brand_master, and product_type
        $productData = DB::table('product_master')
            ->join('price_group', 'product_master.product_code', '=', 'price_group.product_code')
            ->join('brand_master', 'product_master.product_brand', '=', 'brand_master.brand_id')
            ->join('product_type', 'product_master.pm_pt_id', '=', 'product_type.pt_id')
            ->where('product_master.product_status', 1) // Active
            ->where('price_group.market_type', $marketType)
            ->select(
                'product_master.product_code',
                'product_master.pm_pt_id',
                'product_master.product_brand',
                'product_master.product_name',
                'product_master.uom',
                'product_master.unit_size',
                'product_master.inner_case_size',
                'product_master.outer_case_size',
                'product_master.product_mrp',
                'product_master.product_gst',
                'product_master.product_hsn_sac_code',
                'product_master.product_image',
                'product_master.product_status',
                'product_master.barcode',
                'price_group.market_type',
                'price_group.product_ptss',
                'price_group.product_ptdistributor',
                'price_group.product_ptdealer',
                'price_group.product_ptretailer',
                'brand_master.brand_name as brand_name',
                'product_type.pt_name as product_type_name'
            )
            ->get()
            ->map(function ($product) {
                return [
                    'product_code' => $product->product_code,
                    'pm_pt_id' => $product->pm_pt_id,
                    'product_brand' => $product->product_brand,
                    'product_name' => $product->product_name,
                    'uom' => $product->uom,
                    'unit_size' => $product->unit_size,
                    'inner_case_size' => $product->inner_case_size,
                    'outer_case_size' => $product->outer_case_size,
                    'product_mrp' => $product->product_mrp,
                    'product_gst' => $product->product_gst,
                    'product_hsn_sac_code' => $product->product_hsn_sac_code,
                    'product_image' => $product->product_image,
                    'product_status' => $product->product_status,
                    'barcode' => $product->barcode,
                    'market_type' => $product->market_type,
                    'product_ptss' => $product->product_ptss,
                    'product_ptdistributor' => $product->product_ptdistributor,
                    'product_ptdealer' => $product->product_ptdealer,
                    'product_ptretailer' => $product->product_ptretailer,
                    'brand' => [
                        'brand_id' => $product->product_brand,
                        'brand_name' => $product->brand_name
                    ],
                    'producttype' => [
                        'pt_id' => $product->pm_pt_id,
                        'pt_name' => $product->product_type_name,
                        'brand_id' => $product->product_brand
                    ]
                ];
            });

        return response()->json(['product' => $productData]);
    }

    public function getMarketType()
    {
        $marketTypeData = MarketTypeMaster::all();

        return response()->json(['market_type' => $marketTypeData]);
    }

    public function getCustomerCategory()
    {
        $custCategoryData = CustomerCategoryMaster::all();

        $cust_category = [
            'category' => $custCategoryData,
        ];
        return $cust_category;
    }

    public function getGrade()
    {
        $gradeData = GradeMaster::select('gm_id', 'gm_name')->get();

        $grade = [
            'grade' => $gradeData,
        ];
        return $grade;
    }

    public function getBeatRoute()
    {
        $employee = auth()->user();

        $todayDate = now()->toDateString();
        $todayDay = now()->format('l');

        $beatRouteData = BeatDatewiseMaster::with('employee', 'beat')
            ->where('emp_code', $employee->emp_code)
            ->where(function ($query) use ($todayDate) {
                $query->where('bdm_date', $todayDate)
                    ->where('bdm_type', 3); // Filter for datewise data
            })
            ->orWhere(function ($query) use ($todayDay) {
                $query->where('bdm_day', $todayDay)
                    ->where('bdm_type', 2); // Filter for daywise data
            })
            ->orWhere(function ($query) use ($employee) {
                $query->where('emp_code', $employee->emp_code)
                    ->where('bdm_type', 1); // Filter for type 1 data
            })
            ->where('bdm_status', 1)
            ->get();

        return response()->json(['beat_route' => $beatRouteData]);
    }

    public function getCustomer()
    {
        $employee = auth()->user();

        // Get today's date and day
        $todayDate = now()->toDateString();
        $todayDay = now()->format('l');

        // Fetch beat route data for the authenticated employee
        $beatRouteData = BeatDatewiseMaster::with('employee', 'beat')
            ->where('bdm_status', 1)
            ->where('emp_code', $employee->emp_code)
            ->where(function ($query) use ($todayDate) {
                $query->where('bdm_date', $todayDate)
                    ->where('bdm_type', 3); // Filter for datewise data
            })
            ->orWhere(function ($query) use ($todayDay) {
                $query->where('bdm_day', $todayDay)
                    ->where('bdm_type', 2); // Filter for daywise data
            })
            ->orWhere(function ($query) use ($employee) {
                $query->where('emp_code', $employee->emp_code)
                    ->where('bdm_type', 1); // Filter for type 1 data
            })
            ->get()->pluck('beat_code');

        // Fetch retailer cm_code based on the beat codes
        $retailerCodes = CustomerMaster::where('cm_status', 1)->join('cm_beat_relation', 'customer_master.cm_code', '=', 'cm_beat_relation.cm_code')
            ->whereIn('cm_beat_relation.beat_code', $beatRouteData)
            ->pluck('customer_master.cm_code');

        // Fetch retailer data based on the retailer codes
        $retailerData = CustomerMaster::select(
            'customer_master.cm_code',
            'cm_name',
            'cm_mobile',
            'cm_mobile2',
            'cm_email',
            'cm_address',
            'cm_pincode',
            'cm_gst',
            'cm_pan',
            DB::raw('ST_X(cm_coordinates) as cm_long'),
            DB::raw('ST_Y(cm_coordinates) as cm_lat'),
            'cm_market_type',
            'cm_status',
            'cm_town_id',
            'cm_outstanding_amount',
            'cm_contact_person',
            'cm_area',
            'cm_relation_code',
            'cm_type',
            'customer_master.created_at',
            'cm_beat_relation.beat_code',
            'beat_master.beat_name',
            'beat_master.town_id',
            'cm_grade_relation.grade_id',
            'grade_master.gm_name',
            'cm_category_relation.category_id',
            'customer_category_master.ccm_name'
        )
            ->join('cm_beat_relation', 'customer_master.cm_code', '=', 'cm_beat_relation.cm_code')
            ->join('beat_master', 'cm_beat_relation.beat_code', '=', 'beat_master.beat_code')
            ->leftJoin('cm_grade_relation', 'customer_master.cm_code', '=', 'cm_grade_relation.cm_code')
            ->leftJoin('grade_master', 'cm_grade_relation.grade_id', '=', 'grade_master.gm_id')
            ->leftJoin('cm_category_relation', 'customer_master.cm_code', '=', 'cm_category_relation.cm_code')
            ->leftJoin('customer_category_master', 'cm_category_relation.category_id', '=', 'customer_category_master.ccm_id')
            ->whereIn('customer_master.cm_code', $retailerCodes)
            ->with(
                [
                    'anni_birth_relation',
                    'market_type_master',
                    'cm_image_relation',
                    // 'cm_category_relation.category',
                    // 'cm_grade_relation.grade'
                ]
            )
            ->get();

        // Fetch dealer, distributor, and SS data for the authenticated employee
        $customerData = CustomerMaster::select(
            'customer_master.cm_code',
            'cm_name',
            'cm_mobile',
            'cm_mobile2',
            'cm_email',
            'cm_address',
            'cm_pincode',
            'cm_gst',
            'cm_pan',
            DB::raw('ST_X(cm_coordinates) as cm_long'),
            DB::raw('ST_Y(cm_coordinates) as cm_lat'),
            'cm_market_type',
            'cm_status',
            'cm_town_id',
            'cm_outstanding_amount',
            'cm_contact_person',
            'cm_area',
            'cm_relation_code',
            'cm_type',
            'customer_master.created_at',
            'cm_beat_relation.beat_code',
            'beat_master.beat_name',
            'beat_master.town_id',
            'cm_grade_relation.grade_id',
            'grade_master.gm_name',
            'cm_category_relation.category_id',
            'customer_category_master.ccm_name'
        )
            ->join('customer_emp_relation', 'customer_master.cm_code', '=', 'customer_emp_relation.cm_code')
            ->leftJoin('cm_beat_relation', 'customer_master.cm_code', '=', 'cm_beat_relation.cm_code')
            ->leftJoin('beat_master', 'cm_beat_relation.beat_code', '=', 'beat_master.beat_code')
            ->leftJoin('cm_grade_relation', 'customer_master.cm_code', '=', 'cm_grade_relation.cm_code')
            ->leftJoin('grade_master', 'cm_grade_relation.grade_id', '=', 'grade_master.gm_id')
            ->leftJoin('cm_category_relation', 'customer_master.cm_code', '=', 'cm_category_relation.cm_code')
            ->leftJoin('customer_category_master', 'cm_category_relation.category_id', '=', 'customer_category_master.ccm_id')
            ->where('customer_master.cm_status', 1)
            ->where('customer_emp_relation.emp_code', $employee->emp_code)
            ->whereIn('cm_type', [2, 3, 4]) // Exclude cm_type 1 from this query
            ->with(
                [
                    'anni_birth_relation',
                    'market_type_master',
                    'cm_image_relation',
                    // 'cm_category_relation.category',
                    // 'cm_grade_relation.grade'
                ]
            )
            ->get();

        // Fetch customers with cm_type 1 separately
        $type1Customers = CustomerMaster::select(
            'customer_master.cm_code',
            'cm_name',
            'cm_mobile',
            'cm_mobile2',
            'cm_email',
            'cm_address',
            'cm_pincode',
            'cm_gst',
            'cm_pan',
            DB::raw('ST_X(cm_coordinates) as cm_long'),
            DB::raw('ST_Y(cm_coordinates) as cm_lat'),
            'cm_market_type',
            'cm_status',
            'cm_town_id',
            'cm_outstanding_amount',
            'cm_contact_person',
            'cm_area',
            'cm_relation_code',
            'cm_type',
            'customer_master.created_at',
            'cm_beat_relation.beat_code',
            'beat_master.beat_name',
            'beat_master.town_id',
            'cm_grade_relation.grade_id',
            'grade_master.gm_name',
            'cm_category_relation.category_id',
            'customer_category_master.ccm_name'
        )
            ->leftJoin('cm_beat_relation', 'customer_master.cm_code', '=', 'cm_beat_relation.cm_code')
            ->leftJoin('beat_master', 'cm_beat_relation.beat_code', '=', 'beat_master.beat_code')
            ->leftJoin('cm_grade_relation', 'customer_master.cm_code', '=', 'cm_grade_relation.cm_code')
            ->leftJoin('grade_master', 'cm_grade_relation.grade_id', '=', 'grade_master.gm_id')
            ->leftJoin('cm_category_relation', 'customer_master.cm_code', '=', 'cm_category_relation.cm_code')
            ->leftJoin('customer_category_master', 'cm_category_relation.category_id', '=', 'customer_category_master.ccm_id')
            ->where('customer_master.cm_status', 1)
            ->where('cm_type', 1) // Only include cm_type 1
            ->with(
                [
                    'anni_birth_relation',
                    'market_type_master',
                    'cm_image_relation',
                    // 'cm_category_relation.category',
                    // 'cm_grade_relation.grade'
                ]
            )
            ->get();

        // Merge retailer, customer, and type 1 customer data
        $mergedData = $retailerData->merge($customerData)->merge($type1Customers)->unique('cm_code');

        // Format the data to include the beat information as a single object
        $formattedData = $mergedData->map(function ($customer) {
            $customer->cm_beat_relation = [
                'cm_code' => $customer->cm_code,
                'beat_code' => $customer->beat_code,
                'beat' => [
                    'beat_code' => $customer->beat_code,
                    'town_id' => $customer->town_id,
                    'beat_name' => $customer->beat_name
                ]
            ];
            $customer->cm_grade_relation = [
                'cm_code' => $customer->cm_code,
                'grade_id' => $customer->grade_id,
                'grade' => [
                    'gm_id' => $customer->grade_id,
                    'gm_name' => $customer->gm_name
                ]
            ];
            $customer->cm_category_relation = [
                'cm_code' => $customer->cm_code,
                'category_id' => $customer->category_id,
                'category' => [
                    'ccm_id' => $customer->category_id,
                    'ccm_name' => $customer->ccm_name
                ]
            ];
            unset($customer->beat_code, $customer->beat_name, $customer->town_id, $customer->grade_id, $customer->gm_name, $customer->category_id, $customer->ccm_name);
            return $customer;
        });

        return response()->streamJson(['customer' => $formattedData]);
    }

    public function getBank()
    {
        $bankData = BankMaster::get();

        return response()->json(['bank' => $bankData]);
    }

    public function getClaimComplainType()
    {
        $claimComplainTypeData = ClaimComplainTypeMaster::get();

        return response()->json(['claim_complain_type' => $claimComplainTypeData]);
    }

    public function getClaimComplain()
    {
        $employee = auth()->user();
        $claimComplainData = ClaimComplainMaster::with([
            'employee:emp_code,emp_name,emp_mobile',
            'customer:cm_code,cm_name,cm_mobile,cm_mobile2,cm_email,cm_address,cm_pincode,cm_gst,cm_pan,cm_market_type,cm_status,cm_town_id,cm_outstanding_amount,cm_contact_person,cm_area,cm_relation_code,cm_type',
            'cc_type'
        ])
            ->where('emp_code', $employee->emp_code)
            ->get();

        return response()->json(['claim_complain' => $claimComplainData]);
    }

    public function getExpenseType()
    {
        $expenseTypeData = ExpenseType::all();

        return response()->json(['expense_type' => $expenseTypeData]);
    }

    public function getExpense(Request $request)
    {
        $employee = auth()->user();
        $query = ExpenseMaster::select('em_code', 'em_expense_type', 'em_code', 'em_expense_detail', 'em_amount', 'em_approved_amount', 'em_bill_picture', DB::raw('ST_X(em_coordinates) as em_long'), DB::raw('ST_Y(em_coordinates) as em_lat'), 'emp_code', 'em_expense_status', 'em_date', 'em_km')
            ->with('employee', 'expense_type')
            ->where('emp_code', $employee->emp_code);

        // Get the user's selection from the request
        $selectedDate = $request->selected_date ?? '1';

        // Apply dynamic filtering based on the user's selection
        switch ($selectedDate) {
            case '1':
                $query->whereDate('em_date', Carbon::today());
                break;
            case '2':
                $query->whereDate('em_date', Carbon::yesterday());
                break;
            case '7':
                $query->whereBetween('em_date', [Carbon::now()->subDays(6), Carbon::now()]);
                break;
            case '30':
                $query->where('em_date', '>=', Carbon::now()->subDays(29));
                break;
            case '60':
                // Get data for the last 60 days
                $query->where('em_date', '>=', Carbon::now()->subDays(59));
                break;
            default:
                $query->whereDate('em_date', Carbon::today());
                // If the selection is invalid, default to 'all'
                // $query->where('em_date', '>=', Carbon::now()->subDays(59));
                break;
        }

        $expenseData = $query->get();

        return response()->streamJson(['expense' => $expenseData]);
    }

    public function getNonProductionReason()
    {
        $nonProductiveReasonData = NonProductiveReason::all();

        return response()->json(['non_productive_reason' => $nonProductiveReasonData]);
    }

    public function getPaymentType()
    {
        $paymentTypeData = PaymentType::get();

        return response()->json(['payment_type' => $paymentTypeData]);
    }

    public function getPaymentCondition()
    {
        $paymentConditionData = PaymentCondition::get();

        return response()->json(['payment_condition' => $paymentConditionData]);
    }

    public function getPaymentCollection(Request $request)
    {
        $employee = auth()->user();
        $query = PaymentCollection::with(
            'customer:cm_code,cm_login_pin,cm_name,cm_mobile,cm_mobile2,cm_email,cm_address,cm_pincode,cm_gst,cm_pan,cm_market_type,cm_status,cm_town_id,cm_outstanding_amount,cm_contact_person,cm_area,cm_relation_code,created_emp_code,cm_type',
            'payment_type',
            'bank',
            'employee',
            'payment_condition'
        )->where('emp_code', $employee->emp_code);

        $selectedDate = $request->selected_date ?? '1';

        // Apply dynamic filtering based on the user's selection
        switch ($selectedDate) {
            case '1':
                $query->whereDate('created_at', Carbon::today());
                break;
            case '2':
                $query->whereDate('created_at', Carbon::yesterday());
                break;
            case '7':
                $query->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()]);
                break;
            case '30':
                $query->where('created_at', '>=', Carbon::now()->subDays(29));
                break;
            case '60':
                // Get data for the last 60 days
                $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
            default:
                $query->whereDate('created_at', Carbon::today());
                // If the selection is invalid, default to 'all'
                // $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
        }

        $paymentCollectionData = $query->get();

        return response()->json(['payment_collection' => $paymentCollectionData]);
    }

    public function getLeaveApplication()
    {
        $employee = auth()->user();
        $leaveApplicationData = LeaveApplication::with('employee', 'approved_by')->where('emp_code', $employee->emp_code)->get();

        return response()->json(['leave_application' => $leaveApplicationData]);
    }

    public function getCallMaster(Request $request)
    {
        $employee = auth()->user();
        $query = CallMaster::with([
            'employee',
            // 'customer_cli',
            'customer_cli' => function ($query) {
                $query->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', DB::raw('ST_X(cm_coordinates) as cm_long'), DB::raw('ST_Y(cm_coordinates) as cm_lat'), 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type');
            },
            'customer_party' => function ($query) {
                $query->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', DB::raw('ST_X(cm_coordinates) as cm_long'), DB::raw('ST_Y(cm_coordinates) as cm_lat'), 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type');
            },
            // 'customer_party',
            'orders'
        ])->select('call_code', 'emp_code', 'client_code', 'order_sign', DB::raw('ST_X(coordinates) as call_long'), DB::raw('ST_Y(coordinates) as call_lat'), 'accuracy', 'total_quantity', 'party_code', 'reason_id', 'grand_total', 'product_order_type', 'invoice_number', 'call_status', 'remarks', 'packaging_charge', 'transportation_charge', 'transportation_name', 'start_time', 'stop_time', 'cancel_description', 'is_telephonic', 'created_at')
            ->where('emp_code', $employee->emp_code);

        $selectedDate = $request->selected_date ?? '1';

        // Apply dynamic filtering based on the user's selection
        switch ($selectedDate) {
            case '1':
                $query->whereDate('created_at', Carbon::today());
                break;
            case '2':
                $query->whereDate('created_at', Carbon::yesterday());
                break;
            case '7':
                $query->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()]);
                break;
            case '30':
                $query->where('created_at', '>=', Carbon::now()->subDays(29));
                break;
            case '60':
                // Get data for the last 60 days
                $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
            default:
                $query->whereDate('created_at', Carbon::today());
                // If the selection is invalid, default to 'all'
                // $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
        }

        $callMasterData = $query->get();

        return response()->streamJson(['call_master' => $callMasterData]);
    }

    public function getOrderMaster(Request $request)
    {
        $employee = auth()->user();
        $query = CallMaster::with([
            'orders.product',
        ])->select('call_code', 'emp_code', 'client_code', 'order_sign', DB::raw('ST_X(coordinates) as call_long'), DB::raw('ST_Y(coordinates) as call_lat'), 'accuracy', 'total_quantity', 'party_code', 'reason_id', 'grand_total', 'product_order_type', 'invoice_number', 'call_status', 'remarks', 'packaging_charge', 'transportation_charge', 'transportation_name', 'start_time', 'stop_time', 'cancel_description', 'is_telephonic')
            ->where('emp_code', $employee->emp_code);

        $selectedDate = $request->selected_date ?? '1';

        // Apply dynamic filtering based on the user's selection
        switch ($selectedDate) {
            case '1':
                $query->whereDate('created_at', Carbon::today());
                break;
            case '2':
                $query->whereDate('created_at', Carbon::yesterday());
                break;
            case '7':
                $query->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()]);
                break;
            case '30':
                $query->where('created_at', '>=', Carbon::now()->subDays(29));
                break;
            case '60':
                // Get data for the last 60 days
                $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
            default:
                $query->whereDate('created_at', Carbon::today());
                // If the selection is invalid, default to 'all'
                // $query->where('created_at', '>=', Carbon::now()->subDays(59));
                break;
        }

        $orderMasterData = $query->get();
        // $callMasterData = OrderMaster::with(
        //     [
        //         'call' => function ($query) use ($employee) {
        //             $query->where('emp_code', $employee->emp_code);
        //         },
        //         'product'
        //     ]
        // )->get();
        // get all data added by add ordes
        //$callMasterData = CallMaster::where('emp_code', $employee->emp_code)
        //   ->with('orders.product')
        //  ->select('call_code', 'emp_code', 'client_code', 'order_sign', DB::raw('ST_X(coordinates) as call_long'), DB::raw('ST_Y(coordinates) as call_lat'), 'accuracy', 'total_quantity', 'party_code', 'reason_id', 'grand_total', 'product_order_type', 'invoice_number', 'call_status', 'remarks', 'packaging_charge', 'transportation_charge', 'transportation_name', 'start_time', 'stop_time', 'cancel_description', 'is_telephonic')
        // ->get();

        return response()->streamJson(['call_master' => $orderMasterData]);
    }

    public function getConfig()
    {
        $configData = Options::all();

        return response()->json(['config' => $configData]);
    }

    public function getInvoice(Request $request)
    {
        try {
            $request->validate([
                'call_code' => 'required|exists:call_master,call_code',
            ], [
                'call_code.required' => 'Call code is required',
                'call_code.exists' => 'Call code is invalid',
            ]);

            $callCode = $request->call_code;

            // Check if the call code exists
            $callExists = CallMaster::where('call_code', $callCode)->exists();

            if (!$callExists) {
                return response()->json([
                    'error' => true,
                    'message' => 'Call Code does not exist',
                ], 404);
            }

            return response()->json([
                'redirect_url' => route('view_order_invoice', ['call_code' => $callCode])
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // generate report 
    public function getReport(Request $request)
    {
        $reportController = new ReportController;
        $getReport = $reportController->fetchReport($request);
        return $getReport;
    }

    //get notification
    public function getNotification()
    {
        $employee = auth()->user();

        $notification = EmpNotification::where('emp_code', $employee->emp_code)
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get()
            ->map(function ($notification) {
                return [
                    'en_id' => $notification->en_id,
                    'en_type' => $notification->en_type,
                    'en_image' => $notification->en_image,
                    'en_title' => $notification->en_title,
                    'en_message' => $notification->en_message,
                    'emp_code' => $notification->emp_code,
                    'en_status' => $notification->en_status,
                    'en_failed_reason' => $notification->en_failed_reason,
                    'created_at' => Carbon::parse($notification->created_at)->format('Y-m-d H:i:s'),
                    'updated_at' => Carbon::parse($notification->updated_at)->format('Y-m-d H:i:s'),
                ];
            });

        return response()->json(['notification' => $notification]);
    }

    public function getLanguage()
    {
        $languages = DB::table('language_master AS lm')
            ->select('lm.lang_code', 'lm.lang_name', 'alsm.app_lang_string', 'al.translation')
            ->join('app_localization AS al', 'lm.lang_id', '=', 'al.lang_id')
            ->join('app_lang_string_master AS alsm', 'al.app_lang_id', '=', 'alsm.app_lang_id')
            ->orderBy('lm.lang_code')
            ->orderBy('alsm.app_lang_string')
            ->get();

        // language data
        $language_data = [];
        foreach ($languages as $language) {
            $langCode = $language->lang_code;
            if (!isset($language_data[$langCode])) {
                $language_data[$langCode] = [
                    'lang_code' => $language->lang_code,
                    'lang_name' => $language->lang_name,
                    'translations' => [],
                ];
            }
            $language_data[$langCode]['translations'][$language->app_lang_string] = $language->translation;
        }

        return response()->json(['language_data' => $language_data]);
        // return response()->json($response);
    }

    public function getDashboardData()
    {
        $employee = auth()->user();

        // Get the current month and previous month
        $currentMonth = Carbon::now()->format('M-y');
        $previousMonth = Carbon::now()->startOfMonth()->subMonth()->format('M-y');

        // Initialize the data arrays
        $tcPcData = [
            'previous_month' => [
                'month' => $previousMonth,
                'tc' => 0,
                'pc' => 0
            ],
            'current_month' => [
                'month' => $currentMonth,
                'tc' => 0,
                'pc' => 0
            ]
        ];

        // Get the start and end dates for the current month
        $startCurrentMonth = Carbon::now()->startOfMonth();
        $endCurrentMonth = Carbon::now()->endOfMonth();

        // Get total calls and productive calls for the current month
        $currentMonthCalls = CallMaster::where('emp_code', $employee->emp_code)
            ->whereBetween('created_at', [$startCurrentMonth, $endCurrentMonth])
            ->get();
        $tcPcData['current_month']['tc'] = $currentMonthCalls->count();
        $tcPcData['current_month']['pc'] = $currentMonthCalls->where('product_order_type', 1)->count();

        // Get the start and end dates for the previous month
        $startPreviousMonth = Carbon::now()->startOfMonth()->subMonth();
        $endPreviousMonth = Carbon::now()->endOfMonth()->subMonth();

        // Get total calls and productive calls for the previous month
        $previousMonthCalls = CallMaster::where('emp_code', $employee->emp_code)
            ->whereBetween('created_at', [$startPreviousMonth, $endPreviousMonth])
            ->get();
        $tcPcData['previous_month']['tc'] = $previousMonthCalls->count();
        $tcPcData['previous_month']['pc'] = $previousMonthCalls->where('product_order_type', 1)->count();

        $responseData = [
            'tc_pc' => $tcPcData
        ];

        return response()->json($responseData);
    }


    // Add Expense
    public function ExpenseAdd(Request $request)
    {
        try {
            $request->validate([
                'em_expense_type' => 'required',
                'em_code' => 'required|unique:expense_master,em_code',
                'em_expense_detail' => 'required',
                'em_amount' => 'required|numeric',
                'em_approved_amount' => 'required|numeric',
                'em_bill_picture' => 'nullable|string',
                'emp_code' => 'required|exists:employee_master,emp_code',
                'em_date' => 'required',
                'latitude' => 'required',
                'longitude' => 'required',
                'file_extension' => 'nullable',
            ], [
                'em_expense_type.required' => 'Expense Type is required',
                'em_code.required' => 'Expense Code is required',
                'em_code.unique' => 'Expense Code has already been taken.',
                'em_expense_detail.required' => 'Expense detail is required',
                'em_amount.required' => 'Amount is required',
                'em_amount.numeric' => 'Amount must be a numeric value',
                'em_approved_amount.required' => 'Approved amount is required',
                'em_approved_amount.numeric' => 'Approved amount must be a numeric value',
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
                'em_date.required' => 'Expense date is required',
                'latitude.required' => 'Latitude is required',
                'longitude.required' => 'Longitude is required',
            ]);

            if ($request->has('em_bill_picture') && !empty($request->input('em_bill_picture'))) {
                // Decode base64 image
                $imageData = base64_decode($request->em_bill_picture);

                // Determine file extension
                $extension = $request->file_extension ? strtolower($request->file_extension) : 'jpg';

                // Generate unique filename with the determined extension
                $filename = uniqid() . '.' . $extension;

                // Save the decoded image to the public folder
                $path = public_path('/uploads/expense_images/' . $filename);
                file_put_contents($path, $imageData);
            } else {
                $filename = null;
            }

            $expense = new ExpenseMaster();
            $expense->em_expense_type = $request->em_expense_type;
            $expense->em_code = $request->em_code;
            $expense->em_expense_detail = $request->em_expense_detail;
            $expense->em_amount = $request->em_amount;
            $expense->em_approved_amount = $request->em_approved_amount;
            $expense->em_bill_picture = $filename;
            // lat and long
            if (!empty($request->latitude) && !empty($request->longitude)) {
                $latitude = $request->latitude;
                $longitude = $request->longitude;
                $expense->em_coordinates = DB::raw("POINT($longitude,$latitude)");
            }
            // $expense->em_coordinates = $request->em_coordinates ?? null;
            $expense->emp_code = $request->emp_code;
            $expense->em_expense_status = $request->em_expense_status ?? 0; //pending
            $expense->em_date = $request->em_date;
            $expense->em_km = $request->em_km ?? 0;
            $expense->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Expense added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // Get Expense Status by id
    public function getExpneseStatus(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required|exists:employee_master,emp_code',
            ], [
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
            ]);
            $expenseMaster = ExpenseMaster::select('em_code', 'em_expense_type', 'em_code', 'em_expense_detail', 'em_amount', 'em_approved_amount', 'em_bill_picture', DB::raw('ST_X(em_coordinates) as em_long'), DB::raw('ST_Y(em_coordinates) as em_lat'), 'emp_code', 'em_expense_status', 'em_date', 'em_km')
                ->where('emp_code', $request->emp_code)
                ->get();

            return response()->json(['expense_status' => $expenseMaster]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // Add Leave
    public function LeaveAdd(Request $request)
    {
        try {
            $request->validate([
                'la_code' => 'required|unique:leave_application,la_code',
                'la_type_id' => 'required|integer',
                'la_from_date' => 'required',
                'la_total_day' => 'required|numeric',
                'emp_code' => 'required|exists:employee_master,emp_code',
            ], [
                'la_code.required' => 'Leave Code is required',
                'la_code.unique' => 'Leave Code has already been taken.',
                'la_type_id.required' => 'Leave Type is required',
                'la_type_id.integer' => 'Leave Type must be an integer',
                'la_from_date.required' => 'From date is required',
                'la_total_day.required' => 'Total day is required',
                'cm_pincode.numeric' => 'Total day must be a numeric value',
                'emp_code.required' => 'Employee id is required',
                'emp_code.exists' => 'Employee code is invalid',
            ]);

            $leave = new LeaveApplication();
            $leave->la_code = $request->la_code;
            $leave->la_type_id = $request->la_type_id;
            $leave->la_from_date = $request->la_from_date;
            $leave->la_to_date = $request->la_to_date ?? null;
            $leave->la_total_day = $request->la_total_day;
            $leave->la_approved_status = 1; //pending
            $leave->la_reason = $request->la_reason ?? null;
            $leave->emp_code = $request->emp_code;
            $leave->la_notification_status = 0;
            $leave->la_approved_by = $request->la_approved_by ?? null;
            $leave->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Leave added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // Get Leave Status by id
    public function getLeaveStatus(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required|exists:employee_master,emp_code',
            ], [
                'emp_code.required' => 'Employee Id is required',
                'emp_code.exists' => 'Employee code is invalid',

            ]);
            $LeaveStatus = LeaveApplication::where('emp_code', $request->emp_code)->get();

            return response()->json(['leave_status' => $LeaveStatus]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // Add Order
    public function OrderAdd(Request $request)
    {
        try {
            $validationRules = [
                'call_code' => 'required',
                'emp_code' => 'required|exists:employee_master,emp_code',
                'client_code' => 'required|exists:customer_master,cm_code',
                'accuracy' => 'required|numeric',
                'party_code' => 'required_if:product_order_type,1',
                'product_order_type' => 'required|integer',
                'latitude' => 'required',
                'longitude' => 'required',
                'start_time' => 'required',
                'stop_time' => 'required',
                'total_quantity' => 'required_if:product_order_type,1',
                'grand_total' => 'required_if:product_order_type,1',
                'is_telephonic' => 'required_if:product_order_type,0',
                'reason_id' => 'required_if:product_order_type,0',
            ];

            $validationMessages = [
                'call_code.required' => 'Call Code is required',
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee code is invalid',
                'client_code.required' => 'Client code is required',
                'client_code.exists' => 'Client code is invalid',
                'accuracy.required' => 'Accuracy is required',
                'accuracy.numeric' => 'Accuracy must be a numeric value',
                'party_code.required_if' => 'Party code is required when product order type is 1.',
                'product_order_type.required' => 'Product Order type is required',
                'product_order_type.integer' => 'Product Order type must be an integer',
                'latitude.required' => 'Latitude is required.',
                'longitude.required' => 'Longitude is required.',
                'start_time.required' => 'Start time is required',
                'stop_time.required' => 'Stop time is required',
                'total_quantity.required_if' => 'Total quantity is required when product order type is 1.',
                'grand_total.required_if' => 'Grand total is required when product order type is 1.',
                'is_telephonic.required_if' => 'Telephonic is required when product order type is 0.',
                'reason_id.required_if' => 'Reason is required when product order type is 0.',
            ];

            $request->validate($validationRules, $validationMessages);

            // Check if the call_code already exists
            $callData = CallMaster::where('call_code', $request->call_code)->first();

            if ($callData) {
                // If call_code exists, update the existing record
                $callData->emp_code = $request->emp_code;
                $callData->client_code = $request->client_code;
                $callData->order_sign = $request->order_sign ?? null;
                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $callData->coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $callData->accuracy = $request->accuracy;
                $callData->total_quantity = $request->total_quantity ?? null;
                $callData->party_code = $request->party_code;
                $callData->reason_id = $request->reason_id ?? null;
                $callData->grand_total = $request->grand_total ?? null;
                $callData->product_order_type = $request->product_order_type;
                //$callData->call_status = 0; // pending
                $callData->remarks = $request->remarks ?? null;
                $callData->packaging_charge = $request->packaging_charge ?? 0.00;
                $callData->transportation_charge = $request->transportation_charge ?? 0.00;
                $callData->transportation_name = $request->transportation_name ?? null;
                $callData->start_time = $request->start_time ?? null;
                $callData->stop_time = $request->stop_time ?? null;
                $callData->cancel_description = $request->cancel_description ?? null;
                $callData->is_telephonic = $request->is_telephonic ?? 0;
                $callData->save();
            } else {
                // Fetch the current invoice_prefix from the options table
                $invoicePrefix = Options::where('key', 'invoice_prefix')->first()->value;

                // Extract the numeric part from the invoice_prefix
                $numericPart = intval(preg_replace('/[^0-9]+/', '', $invoicePrefix), 10);

                // Find the maximum invoice number in the CallMaster table that starts with the non-numeric part of the invoice_prefix
                //$maxInvoiceNumber = CallMaster::where('invoice_number', 'like', preg_replace('/[0-9]+/', '', $invoicePrefix) . '%')->max('invoice_number');
                $invoices = CallMaster::where('invoice_number', 'like', $invoicePrefix . '%')
                    ->get(['invoice_number']);

                // Extract numeric parts and find the maximum
                $lastNumber = $invoices->reduce(function ($carry, $item) {
                    $numericPart = (int)preg_replace('/[^0-9]/', '', $item->invoice_number);
                    return $numericPart > $carry ? $numericPart : $carry;
                }, 0);
                // If a maximum invoice number was found, increment the numeric part
                if ($lastNumber) {
                    $numericPart = intval(preg_replace('/[^0-9]+/', '', $lastNumber), 10) + 1;
                } else {
                    $numericPart = 1;
                }

                // Combine the numeric part with the non-numeric part to form the new invoice_number
                $invoiceNumber = preg_replace('/[0-9]+/', '', $invoicePrefix) . str_pad($numericPart, strlen(preg_replace('/[^0-9]+/', '', $invoicePrefix)), '0', STR_PAD_LEFT);

                // Create a new record
                $callData = new CallMaster();
                $callData->call_code = $request->call_code;
                $callData->emp_code = $request->emp_code;
                $callData->client_code = $request->client_code;
                $callData->order_sign = $request->order_sign ?? null;
                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $callData->coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $callData->accuracy = $request->accuracy;
                $callData->total_quantity = $request->total_quantity ?? null;
                $callData->party_code = $request->party_code;
                $callData->reason_id = $request->reason_id ?? null;
                $callData->grand_total = $request->grand_total ?? null;
                $callData->product_order_type = $request->product_order_type;
                $callData->invoice_number = $invoiceNumber;
                $callData->call_status = ($request->product_order_type == 0) ? 1 : 0; // 0-pending,1-confirm
                $callData->remarks = $request->remarks ?? null;
                $callData->packaging_charge = $request->packaging_charge ?? 0.00;
                $callData->transportation_charge = $request->transportation_charge ?? 0.00;
                $callData->transportation_name = $request->transportation_name ?? null;
                $callData->start_time = $request->start_time ?? null;
                $callData->stop_time = $request->stop_time ?? null;
                $callData->cancel_description = $request->cancel_description ?? null;
                $callData->is_telephonic = $request->is_telephonic ?? 0;
                $callData->created_at = $request->created_at ?? now();
                $callData->updated_at = $request->created_at ?? now();
                $callData->save();
            }

            // If product_order_type is 1, store in OrderMaster table
            if ($request->product_order_type == 1) {
                $request->validate([
                    'products.*.order_code' => 'required',
                    'products.*.product_code' => 'required',
                    'products.*.quantity' => 'required|numeric',
                    'products.*.pcs' => 'required|numeric',
                    'products.*.box' => 'required|numeric',
                    'products.*.bunch' => 'required|numeric',
                    'products.*.mrp' => 'required|numeric',
                    'products.*.pts' => 'required|numeric',
                    'products.*.rate_basic' => 'required|numeric',
                    'products.*.total_basic_rate' => 'required|numeric',
                    'products.*.gst' => 'required|numeric',
                    'products.*.gst_amount' => 'required|numeric',
                    'products.*.grand_total' => 'required|numeric',
                ], [
                    'products.*.order_code.required' => 'Order Code is required',
                    'products.*.product_code.required' => 'Product code is required',
                    'products.*.quantity.required' => 'Quantity is required',
                    'products.*.quantity.numeric' => 'Quantity must be a numeric value',
                    'products.*.pcs.required' => 'Pcs is required',
                    'products.*.pcs.numeric' => 'Pcs must be a numeric value',
                    'products.*.box.required' => 'Box is required',
                    'products.*.box.numeric' => 'Box must be a numeric value',
                    'products.*.bunch.required' => 'Bunch is required',
                    'products.*.bunch.numeric' => 'Bunch must be a numeric value',
                    'products.*.mrp.required' => 'Mrp is required',
                    'products.*.mrp.numeric' => 'Mrp must be a numeric value',
                    'products.*.pts.required' => 'Pts is required',
                    'products.*.pts.numeric' => 'Pts must be a numeric value',
                    'products.*.rate_basic.required' => 'Rate Basic is required',
                    'products.*.rate_basic.numeric' => 'Rate Basic must be a numeric value',
                    'products.*.total_basic_rate.required' => 'Total Basic Rate is required',
                    'products.*.total_basic_rate.numeric' => 'Total Basic Rate must be a numeric value',
                    'products.*.gst.required' => 'Gst is required',
                    'products.*.gst.numeric' => 'Gst must be a numeric value',
                    'products.*.gst_amount.required' => 'Gst Amount is required',
                    'products.*.gst_amount.numeric' => 'Gst Amount must be a numeric value',
                    'products.*.grand_total.required' => 'Grand Total is required',
                    'products.*.grand_total.numeric' => 'Grand Total must be a numeric value',
                ]);

                foreach ($request->products as $product) {
                    $orderData = OrderMaster::where('order_code', $product['order_code'])
                        ->where('call_code', $callData->call_code)
                        ->first();

                    if ($orderData) {
                        // Update existing order data
                        $orderData->product_code = $product['product_code'];
                        $orderData->quantity = $product['quantity'];
                        $orderData->pcs = $product['pcs'];
                        $orderData->box = $product['box'];
                        $orderData->bunch = $product['bunch'];
                        $orderData->mrp = $product['mrp'] ?? 0.00;
                        $orderData->pts = $product['pts'] ?? 0.00;
                        $orderData->rate_basic = $product['rate_basic'] ?? 0.00;
                        $orderData->total_basic_rate = $product['total_basic_rate'] ?? 0.00;
                        $orderData->gst = $product['gst'] ?? 0.00;
                        $orderData->gst_amount = $product['gst_amount'] ?? 0.00;
                        $orderData->grand_total = $product['grand_total'] ?? 0.00;
                        $orderData->save();
                    } else {
                        // Create new order data
                        $orderData = new OrderMaster();
                        $orderData->order_code = $product['order_code'];
                        $orderData->call_code = $callData->call_code;
                        $orderData->product_code = $product['product_code'];
                        $orderData->quantity = $product['quantity'];
                        $orderData->pcs = $product['pcs'];
                        $orderData->box = $product['box'];
                        $orderData->bunch = $product['bunch'];
                        $orderData->mrp = $product['mrp'] ?? 0.00;
                        $orderData->pts = $product['pts'] ?? 0.00;
                        $orderData->rate_basic = $product['rate_basic'] ?? 0.00;
                        $orderData->total_basic_rate = $product['total_basic_rate'] ?? 0.00;
                        $orderData->gst = $product['gst'] ?? 0.00;
                        $orderData->gst_amount = $product['gst_amount'] ?? 0.00;
                        $orderData->grand_total = $product['grand_total'] ?? 0.00;
                        $orderData->save();
                    }
                }
            }

            $waBuyerProd = waOrderMessage::where('category', 'buyer')->where('type', 'productive')->first();
            $waSellerProd = waOrderMessage::where('category', 'seller')->where('type', 'productive')->first();
            $waBuyerNonProd = waOrderMessage::where('category', 'buyer')->where('type', 'non_productive')->first();

            if ($request->product_order_type == 1) {
                // send message to productive order
                if ($waBuyerProd && $waBuyerProd->status == 1) {
                    $evtData = explode(',', $waBuyerProd->event);
                    if (in_array('receive', $evtData)) {
                        waOrderMessages($callData->call_code, $callData->client_code, $callData->party_code, $callData->invoice_number, $callData->grand_total, $callData->total_quantity, $waBuyerProd->template_id, $waBuyerProd->language, $type = 'buyer', 1);
                    }
                }

                if ($waSellerProd && $waSellerProd->status == 1) {
                    $evtData = explode(',', $waSellerProd->event);
                    if (in_array('receive', $evtData)) {
                        waOrderMessages($callData->call_code, $callData->client_code, $callData->party_code, $callData->invoice_number, $callData->grand_total, $callData->total_quantity, $waSellerProd->template_id, $waSellerProd->language, $type = 'seller', 1);
                    }
                }
            } else {
                // send message to non productive order
                if ($waBuyerNonProd && $waBuyerNonProd->status == 1) {
                    $evtData = explode(',', $waBuyerNonProd->event);
                    if (in_array('receive', $evtData)) {
                        waOrderMessages($callData->call_code, $callData->client_code, $callData->party_code, $callData->invoice_number, $callData->grand_total, $callData->total_quantity, $waBuyerNonProd->template_id, $waBuyerNonProd->language, $type = 'buyer', 0);
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Order added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => true,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Claim Complain Add
    public function ClaimComplainAdd(Request $request)
    {
        try {
            $request->validate([
                'ccm_code' => 'required|unique:claim_complain_master,ccm_code',
                'ccm_type_id' => 'required|integer',
                'ccm_note' => 'required',
                'cm_code' => 'required|exists:customer_master,cm_code',
                'emp_code' => 'required|exists:employee_master,emp_code',
                'ccm_cc_type' => 'required|exists:claim_complain_type_master,cct_id|integer',
                'ccm_image' => 'nullable|string',
            ], [
                'ccm_code.required' => 'Claim Complain Code is required',
                'ccm_code.unique' => 'Claim Complain Code has already been taken.',
                'ccm_type_id.required' => 'Claim Complain Type is required',
                'ccm_type_id.integer' => 'Claim Complain Type must be an integer',
                'ccm_note.required' => 'Claim Complain Note is required',
                'cm_code.required' => 'Customer code is required',
                'cm_code.exists' => 'Customer code is invalid',
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
                'ccm_cc_type.required' => 'Claim Complain Type is required',
                'ccm_cc_type.exists' => 'Claim Complain Type is invalid',
                'ccm_cc_type.integer' => 'Claim Complain must be an integer',
            ]);

            if ($request->has('ccm_image')) {
                // Decode base64 image
                $imageData = base64_decode($request->ccm_image);

                // Generate unique filename
                $filename = uniqid() . '.jpg';

                // Save the decoded image to the public folder
                $path = public_path('/uploads/claim_complain_images/' . $filename);
                file_put_contents($path, $imageData);
            } else {
                $filename = null;
            }

            $claimCompData = new ClaimComplainMaster();
            $claimCompData->ccm_code = $request->ccm_code;
            $claimCompData->ccm_type_id = $request->ccm_type_id;
            $claimCompData->ccm_note = $request->ccm_note;
            $claimCompData->ccm_image = $filename;
            $claimCompData->cm_code = $request->cm_code;
            $claimCompData->emp_code = $request->emp_code;
            $claimCompData->ccm_cc_type = $request->ccm_cc_type;
            $claimCompData->ccm_status = 0; //pending
            $claimCompData->ccm_remarks = $request->ccm_remarks ?? null;
            $claimCompData->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Claim Complain added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Get Claim Complain Status by id
    public function getClaimComplainStatus(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required|exists:employee_master,emp_code',
            ], [
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
            ]);
            $ClaimComplainStatus = ClaimComplainMaster::where('emp_code', $request->emp_code)->get();

            return response()->json(['claim_complain_status' => $ClaimComplainStatus]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }

    // Customer Add
    public function CustomerAdd(Request $request)
    {
        $employee = auth()->user();
        try {
            $request->validate([
                'cm_code' => 'required|unique:customer_master,cm_code',
                'cm_name' => 'required',
                'cm_mobile' => 'required|numeric',
                'cm_mobile2' => 'nullable|numeric',
                'cm_address' => 'required',
                'cm_pincode' => 'required|numeric',
                'latitude' => 'required',
                'longitude' => 'required',
                // 'cm_gst' => 'required',
                'cm_town_id' => 'required|exists:town_master,town_id|integer',
                'cm_outstanding_amount' => 'nullable|numeric',
                'cm_contact_person' => 'required',
                'cm_area' => 'required',
                'cm_relation_code' => 'required|exists:customer_master,cm_code',
                'cm_type' => 'required|integer',
                'beat_code' => [
                    'sometimes',
                    'required_if:cm_type,5',
                    'exists:beat_master,beat_code',
                ],
                'category_id' => [
                    'sometimes',
                    'required_if:cm_type,5',
                    'exists:customer_category_master,ccm_id',
                    'integer',
                ],
                'grade_id' => [
                    'sometimes',
                    'required_if:cm_type,5',
                    'exists:grade_master,gm_id',
                    'integer',
                ],
            ], [
                'cm_code.required' => 'Code is required',
                'cm_code.unique' => 'Code has already been taken.',
                'cm_name.required' => 'Name is required',
                'cm_mobile.required' => 'Mobile Number is required',
                'cm_mobile.numeric' => 'Mobile Number must be a numeric value',
                'cm_mobile2.numeric' => 'Mobile Number 2 must be a numeric value',
                'cm_address.required' => 'Address is required',
                'cm_pincode.required' => 'Pincode is required',
                'cm_pincode.numeric' => 'Pincode must be a numeric value',
                'latitude.required' => 'Latitude is required',
                'longitude.required' => 'Longitude is required',
                // 'cm_gst.required' => 'Gst is required',
                'cm_town_id.required' => 'Town is required',
                'cm_town_id.exists' => 'Town is invalid',
                'cm_town_id.integer' => 'Town must be an integer',
                'cm_outstanding_amount.numeric' => 'Outstanding Amount must be a numeric value',
                'cm_contact_person.required' => 'Contact Person Name is required',
                'cm_area.required' => 'Area is required',
                'cm_relation_code.required' => 'Customer relation code is required',
                'cm_relation_code.exists' => 'Customer relation code is invalid',
                'cm_type.required' => 'Customer Type is required',
                'cm_type.integer' => 'Customer Type must be an integer',
                'beat_code.required_if' => 'Beat code is required if Customer Type is Retailer',
                'beat_code.exists' => 'Beat code is invalid',
                'category_id.required_if' => 'Category is required if Customer Type is Retailer',
                'category_id.exists' => 'Category is invalid',
                'category_id.integer' => 'Category must be an integer',
                'grade_id.required_if' => 'Grade is required if Customer Type is Retailer',
                'grade_id.exists' => 'Grade is invalid',
                'grade_id.integer' => 'Grade must be an integer',
            ]);

            $customerData = new CustomerMaster();
            $customerData->cm_code = $request->cm_code;
            if ($request->cm_type == 5) {
                $customerData->cm_login_pin = null;
            } else {
                $customerData->cm_login_pin = substr($request->cm_code, -4) ?? null;
            }
            $customerData->cm_name = $request->cm_name;
            $customerData->cm_mobile = $request->cm_mobile;
            $customerData->cm_mobile2 = $request->cm_mobile2 ?? null;
            $customerData->cm_email = $request->cm_email ?? null;
            $customerData->cm_address = $request->cm_address;
            $customerData->cm_pincode = $request->cm_pincode;
            $customerData->cm_gst = $request->cm_gst ?? null;
            $customerData->cm_pan = $request->cm_pan ?? null;

            // lat and long
            if (!empty($request->latitude) && !empty($request->longitude)) {
                $latitude = $request->latitude;
                $longitude = $request->longitude;
                $customerData->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
            }

            $customerData->cm_status = 1; // 1-active, 0-inactive
            $customerData->cm_town_id = $request->cm_town_id;
            $customerData->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
            $customerData->cm_contact_person = $request->cm_contact_person;
            $customerData->cm_area = $request->cm_area;
            $customerData->cm_relation_code = $request->cm_relation_code;
            $customerData->created_emp_code = $employee->emp_code;
            // $customerData->cm_market_type = $request->cm_market_type;
            $relationCustomer = CustomerMaster::find($request->cm_relation_code);
            if ($relationCustomer) {
                $customerData->cm_market_type = $relationCustomer->cm_market_type;
            }
            $customerData->cm_type = $request->cm_type; //1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
            $customerData->save();

            if (!empty($request->beat_code)) {
                $cmBeatRel = new CmBeatRelation();
                $cmBeatRel->cm_code = $customerData->cm_code;
                $cmBeatRel->beat_code = $request->beat_code;
                $cmBeatRel->save();
            }

            if (!empty($request->category_id)) {
                $cmCategoryRel = new CmCategoryRelation();
                $cmCategoryRel->cm_code = $customerData->cm_code;
                $cmCategoryRel->category_id = $request->category_id;
                $cmCategoryRel->save();
            }

            if (!empty($request->grade_id)) {
                $cmGradeRel = new CmGradeRelation();
                $cmGradeRel->cm_code = $customerData->cm_code;
                $cmGradeRel->grade_id = $request->grade_id;
                $cmGradeRel->save();
            }

            if (!empty($request->birth_date) || !empty($request->anni_date)) {
                $anniBirthRel = new CmAnniBirthRelation();
                $anniBirthRel->cm_code = $customerData->cm_code;
                $anniBirthRel->birth_date = $request->birth_date;
                $anniBirthRel->anni_date = $request->anni_date;
                $anniBirthRel->save();
            }


            if ($employee->emp_code) {
                $cst_emp_rel = new CustomerEmpRelation();
                $cst_emp_rel->emp_code = $employee->emp_code;
                $cst_emp_rel->cm_code = $customerData->cm_code;
                $cst_emp_rel->save();
            }

            // Retrieve and concatenate the images' filenames
            $imageFilenames = [];
            if ($request->has('shop_images') && !empty($request->shop_images)) {
                $images = array_filter($request->shop_images, function ($image) {
                    return !is_null($image) && !empty($image);
                });

                if (count($images) > 0) {
                    foreach ($images as $image) {
                        $filename = uniqid() . '.jpg';
                        $imageData = base64_decode($image);
                        $path = public_path('/uploads/retailer_images/' . $filename);
                        file_put_contents($path, $imageData);

                        // Store the filename in the array
                        $imageFilenames[] = $filename;
                    }

                    // Concatenate the filenames with commas
                    $concatenatedFilenames = implode(',', $imageFilenames);

                    // Save the concatenated filenames to the database
                    $ccmImage = new CmImageRelation();
                    $ccmImage->cm_code = $customerData->cm_code;
                    $ccmImage->image_name = $concatenatedFilenames;
                    $ccmImage->save();
                }
            }


            return response()->json([
                'success' => TRUE,
                'message' => 'Customer added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Customer Update
    public function CustomerUpdate(Request $request)
    {
        try {
            $request->validate([
                'cm_code' => 'required|exists:customer_master,cm_code',
                'cm_name' => 'required',
                'cm_mobile' => 'required|numeric',
                'cm_mobile2' => 'nullable|numeric',
                'cm_address' => 'required',
                'cm_pincode' => 'required|numeric',
                'latitude' => 'required',
                'longitude' => 'required',
                // 'cm_gst' => 'required',
                'cm_town_id' => 'required|exists:town_master,town_id|integer',
                'cm_contact_person' => 'required',
                'cm_area' => 'required',
                'cm_relation_code' => 'required|exists:customer_master,cm_code',
                'beat_code' => 'required|exists:beat_master,beat_code',
                'category_id' => 'required|exists:customer_category_master,ccm_id|integer',
                'grade_id' => 'required|exists:grade_master,gm_id|integer',
            ], [
                'cm_code.required' => 'Customer code is required',
                'cm_code.exists' => 'Customer code is invalid',
                'cm_name.required' => 'Name is required',
                'cm_mobile.required' => 'Mobile Number is required',
                'cm_mobile.numeric' => 'Mobile Number must be a numeric value',
                'cm_mobile2.numeric' => 'Mobile Number 2 must be a numeric value',
                'cm_address.required' => 'Address is required',
                'cm_pincode.required' => 'Pincode is required',
                'cm_pincode.numeric' => 'Pincode must be a numeric value',
                // 'cm_gst.required' => 'Gst is required',
                'cm_town_id' => 'Town is required',
                'cm_town_id.exists' => 'Town is invalid',
                'cm_town_id.integer' => 'Town must be an integer',
                'cm_contact_person' => 'Contact Person Name is required',
                'cm_area' => 'Area is required',
                'cm_relation_code' => 'Customer relation code is required',
                'cm_relation_code.exists' => 'Customer relation code is invalid',
                'beat_code' => 'Beat code is required',
                'beat_code.exists' => 'Beat code is invalid',
                'category_id' => 'Category is required',
                'category_id.exists' => 'Category is invalid',
                'category_id.integer' => 'Category must be an integer',
                'grade_id' => 'Grade is required',
                'grade_id.exists' => 'Grade is invalid',
                'grade_id.integer' => 'Grade must be an integer',
            ]);

            $customerData = CustomerMaster::find($request->cm_code);
            $customerData->cm_name = $request->cm_name;
            $customerData->cm_mobile = $request->cm_mobile;
            $customerData->cm_mobile2 = $request->cm_mobile2 ?? null;
            $customerData->cm_email = $request->cm_email ?? null;
            $customerData->cm_address = $request->cm_address;
            $customerData->cm_pincode = $request->cm_pincode;
            $customerData->cm_gst = $request->cm_gst ?? null;
            $customerData->cm_pan = $request->cm_pan ?? null;

            // lat and long
            if (!empty($request->latitude) && !empty($request->longitude)) {
                $latitude = $request->latitude;
                $longitude = $request->longitude;
                $customerData->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
            }

            $customerData->cm_status = 1; // 1-active, 0-inactive
            $customerData->cm_town_id = $request->cm_town_id;
            $customerData->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
            $customerData->cm_contact_person = $request->cm_contact_person;
            $customerData->cm_area = $request->cm_area;
            $customerData->cm_relation_code = $request->cm_relation_code; // customer master table foreign key
            $relationCustomer = CustomerMaster::find($request->cm_relation_code);
            if ($relationCustomer) {
                $customerData->cm_market_type = $relationCustomer->cm_market_type;
            }
            $customerData->save();

            // Beat Relation
            if (!empty($request->beat_code)) {
                $cmBeatRel = CmBeatRelation::where('cm_code', $customerData->cm_code)->first();
                if ($cmBeatRel) {
                    $cmBeatRel->beat_code = $request->beat_code;
                    $cmBeatRel->save();
                } else {
                    $cmBeatRel = new CmBeatRelation();
                    $cmBeatRel->cm_code = $customerData->cm_code;
                    $cmBeatRel->beat_code = $request->beat_code;
                    $cmBeatRel->save();
                }
            }

            // Category Relation
            if (!empty($request->category_id)) {
                $cmCategoryRel = CmCategoryRelation::where('cm_code', $customerData->cm_code)->first();
                if ($cmCategoryRel) {
                    $cmCategoryRel->category_id = $request->category_id;
                    $cmCategoryRel->save();
                } else {
                    $cmCategoryRel = new CmCategoryRelation();
                    $cmCategoryRel->cm_code = $customerData->cm_code;
                    $cmCategoryRel->category_id = $request->category_id;
                    $cmCategoryRel->save();
                }
            }

            // Grade Relation
            if (!empty($request->grade_id)) {
                $cmGradeRel = CmGradeRelation::where('cm_code', $customerData->cm_code)->first();
                if ($cmGradeRel) {
                    $cmGradeRel->grade_id = $request->grade_id;
                    $cmGradeRel->save();
                } else {
                    $cmGradeRel = new CmGradeRelation();
                    $cmGradeRel->cm_code = $customerData->cm_code;
                    $cmGradeRel->grade_id = $request->grade_id;
                    $cmGradeRel->save();
                }
            }

            // Anniversary and Birthdate Relation
            if (!empty($request->birth_date) || !empty($request->anni_date)) {
                $aniBirthRel = CmAnniBirthRelation::where('cm_code', $customerData->cm_code)->first();
                if ($aniBirthRel) {
                    $aniBirthRel->birth_date = $request->birth_date ?? null;
                    $aniBirthRel->anni_date = $request->anni_date ?? null;
                    $aniBirthRel->save();
                } else {
                    $anniBirthRel = new CmAnniBirthRelation();
                    $anniBirthRel->cm_code = $customerData->cm_code;
                    $anniBirthRel->birth_date = $request->birth_date ?? null;
                    $anniBirthRel->anni_date = $request->anni_date ?? null;
                    $anniBirthRel->save();
                }
            }

            // Handle Image Update
            if ($request->has('shop_images') && !empty($request->shop_images)) {
                // Delete old images
                $existingImages = CmImageRelation::where('cm_code', $customerData->cm_code)->first();
                if ($existingImages) {
                    $oldImages = explode(',', $existingImages->image_name);
                    foreach ($oldImages as $oldImage) {
                        $oldImagePath = public_path('/uploads/retailer_images/' . $oldImage);
                        if (file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                    $existingImages->delete();
                }

                // Save new images
                $imageFilenames = [];
                $images = $request->shop_images;
                foreach ($images as $image) {
                    $filename = uniqid() . '.jpg';
                    $imageData = base64_decode($image);
                    $path = public_path('/uploads/retailer_images/' . $filename);
                    file_put_contents($path, $imageData);

                    // Store the filename in the array
                    $imageFilenames[] = $filename;
                }

                // Concatenate the filenames with commas
                $concatenatedFilenames = implode(',', $imageFilenames);

                // Save the concatenated filenames to the database
                $ccmImage = new CmImageRelation();
                $ccmImage->cm_code = $customerData->cm_code;
                $ccmImage->image_name = $concatenatedFilenames;
                $ccmImage->save();
            }

            return response()->json([
                'success' => TRUE,
                'message' => 'Customer Updated successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getTeamMember()
    {
        $employee = auth()->user();

        $temMember = EmployeeMaster::where('reporting_emp_code', $employee->emp_code)->get();
        return response()->json(['team_members' => $temMember]);
    }

    // Employee Forgot Pin
    public function EmployeeForgotPin(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required|exists:employee_master,emp_code',
                'emp_pin' => 'required|numeric',
            ], [
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
                'emp_pin.required' => 'Pin is required',
                'emp_pin.numeric' => 'Pin must be a numeric value',
            ]);

            $EmpPin = EmployeeMaster::find($request->emp_code);
            $EmpPin->emp_pin = $request->emp_pin;
            $EmpPin->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Employee Pin Updated successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Payment Add
    public function PaymentAdd(Request $request)
    {
        try {
            $request->validate([
                'pc_code' => 'required|unique:payment_collection,pc_code',
                'cm_code' => 'required|exists:customer_master,cm_code',
                'pc_type' => 'required|exists:payment_type,pt_id|integer',
                'pc_amount' => 'required|numeric',
                'pc_note' => 'required',
                'emp_code' => 'required|exists:employee_master,emp_code',
                'pc_account_no' => 'nullable|numeric',
                'pc_condition_id' => 'required|exists:payment_condition,pc_id|integer',
            ], [
                'pc_code.required' => 'Code is required',
                'pc_code.unique' => 'Code has already been taken.',
                'cm_code.required' => 'Customer code is required',
                'cm_code.exists' => 'Customer code is invalid',
                'pc_type.required' => 'Type is required',
                'pc_type.exists' => 'Type is invalid',
                'pc_type.integer' => 'Type must be an integer',
                'pc_amount.required' => 'Amount is required',
                'pc_amount.numeric' => 'Amount must be a numeric value',
                'pc_note.required' => 'Note is required',
                'emp_code.required' => 'Employee code is required',
                'emp_code.exists' => 'Employee Code is invalid',
                'pc_account_no.numeric' => 'Account Number must be a numeric value',
                'pc_condition_id.required' => 'Condition id is required',
                'pc_condition_id.exists' => 'Condition id is invalid',
                'pc_condition_id.integer' => 'Condition id must be an integer',
            ]);

            if ($request->has('pc_cheque_photo')) {
                // Decode base64 image
                $imageData = base64_decode($request->pc_cheque_photo);

                // Generate unique filename
                $filename = uniqid() . '.jpg';

                // Save the decoded image to the public folder
                $path = public_path('/uploads/payment_collection_images/' . $filename);
                file_put_contents($path, $imageData);
            } else {
                $filename = null;
            }

            $paymentColl = new PaymentCollection();
            $paymentColl->pc_code = $request->pc_code;
            $paymentColl->cm_code = $request->cm_code;
            $paymentColl->pc_type = $request->pc_type;
            $paymentColl->pc_cheque_date = $request->pc_cheque_date ?? null;
            $paymentColl->pc_amount = $request->pc_amount;
            $paymentColl->pc_bank_id = $request->pc_bank_id ?? null;
            $paymentColl->pc_cheque_no = $request->pc_cheque_no ?? null;
            $paymentColl->pc_note = $request->pc_note;
            $paymentColl->emp_code = $request->emp_code;
            $paymentColl->pc_account_no = $request->pc_account_no ?? null;
            $paymentColl->pc_cheque_photo = $filename;
            $paymentColl->pc_condition_id = $request->pc_condition_id;
            $paymentColl->pc_utr = $request->pc_utr ?? null;
            $paymentColl->pc_status = 0;
            $paymentColl->created_at = $request->created_at ?? now();
            $paymentColl->updated_at = $request->updated_at ?? now();
            $paymentColl->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Payment Collection added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Location Sync Add
    public function LocationSyncAdd(Request $request)
    {
        try {
            $request->validate([
                'emp_code' => 'required',
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'accuracy' => 'required|numeric',
                'sync_time' => 'required',
                'other_info' => 'required',
            ], [
                'emp_code.required' => 'Employee code is required',
                'latitude.required' => 'Latitude is required',
                'latitude.numeric' => 'Latitude must be a numeric value',
                'longitude.required' => 'Longitude is required',
                'longitude.numeric' => 'Longitude must be a numeric value',
                'accuracy.required' => 'Accuracy is required',
                'accuracy.numeric' => 'Accuracy must be a numeric value',
                'sync_time.required' => 'Sync Time is required',
                'other_info.required' => 'Other info is required',
            ]);

            $empLocation = new EmpLocation();
            $empLocation->emp_code = $request->emp_code;

            //store lat long
            $latitude = $request->latitude;
            $longitude = $request->longitude;
            $empLocation->coordinates = DB::raw("POINT($longitude,$latitude)");

            $empLocation->accuracy = $request->accuracy;
            $empLocation->sync_time = $request->sync_time;
            $empLocation->other_info = $request->other_info;
            $empLocation->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Location Sync added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function FcmTokenUpdate(Request $request)
    {
        try {
            $request->validate([
                'fcm_token' => 'required',
            ], [
                'fcm_token.required' => 'Fcm Token is required',
            ]);

            $employee = auth()->user();
            $deviceInfo = json_decode($employee->device_info, true);
            $deviceInfo['fcm_token'] = $request->fcm_token;
            $employee->device_info = json_encode($deviceInfo);
            $employee->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'FCM token updated successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function BeatAdd(Request $request)
    {
        $employee = auth()->user();
        try {
            $request->validate([
                'town_id' => 'required|exists:town_master,town_id|integer',
                'beat_name' => 'required',
            ], [
                'town_id.required' => 'Town is required',
                'town_id.exists' => 'Town not found',
                'town_id.integer' => 'Town must be an integer',
                'beat_name.required' => 'Name is required',
            ]);

            // Fetch the prefix from the options table
            $beatPrefix = Options::where('key', 'beat_prefix')->first()->value;

            // extract the numeric part from the prefix
            $numericPart = intval(preg_replace('/[^0-9]+/', '', $beatPrefix), 10);

            //Find the maximum code in the EmployeeMaster table that starts with the non-numeric part of the prefix
            $maxCmCode = BeatMaster::where('beat_code', 'like', preg_replace('/[0-9]+/', '', $beatPrefix) . '%')->max('beat_code');

            // If a maximum code was found, increment the numeric part
            if ($maxCmCode) {
                $numericPart = intval(preg_replace('/[^0-9]+/', '', $maxCmCode), 10) + 1;
            }

            // Generate the next code
            $beat_code = preg_replace('/[0-9]+/', '', $beatPrefix) . str_pad($numericPart, strlen(preg_replace('/[^0-9]+/', '', $beatPrefix)), '0', STR_PAD_LEFT);
            // dd($beat_code);
            $autoApprove = Options::where('key', 'beat_request_auto_approve')->first()->value ?? 0;

            $beatData = new BeatMaster();
            $beatData->beat_code = $beat_code;
            $beatData->town_id = $request->town_id;
            $beatData->beat_name = $request->beat_name;
            if ($autoApprove == '1') {
                $beatData->beat_status = 1;
            } else {
                $beatData->beat_status = 0;
            }

            $beatData->created_emp_code = $employee->emp_code;

            $beatData->save();

            $beatRoute = new BeatDatewiseMaster();
            $beatRoute->beat_code = $beat_code;
            $beatRoute->emp_code = $employee->emp_code;
            $beatRoute->bdm_type = 1; //fix

            $beatRoute->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Beat added successfully'
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function RoutePlanList()
    {
        $employee = auth()->user();

        $todayDate = now()->toDateString();
        $todayDay = now()->format('l');

        $beatRouteData = BeatDatewiseMaster::with('employee', 'beat')
            ->where('emp_code', $employee->emp_code)
            ->where(function ($query) use ($todayDate) {
                $query->where('bdm_date', '>=', $todayDate)
                    ->where('bdm_type', 3); // 3-datewise
            })
            ->orWhere(function ($query) use ($todayDay) {
                $query->where('bdm_type', 2); // 2-daywise
            })
            ->orWhere(function ($query) use ($employee) {
                $query->where('emp_code', $employee->emp_code)
                    ->where('bdm_type', 1); // 1- fix
            })
            ->whereIn('bdm_status', [0, 1, 2])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json(['beat_route' => $beatRouteData]);
    }

    // for add route plan already assigned beat not send to response only remaining beat send to array
    public function notAssociateBeatList()
    {
        $employee = auth()->user();

        $allBeats = BeatMaster::select('beat_code', 'beat_name')
            ->where('beat_status', 1)
            ->orderBy('beat_name', 'asc')
            ->get();

        $userBeats = BeatDatewiseMaster::where('emp_code', $employee->emp_code)
            ->where('bdm_type', 1)
            ->whereIn('bdm_status', [0, 1])
            ->pluck('beat_code')
            ->toArray();

        $beat_list = $allBeats->whereNotIn('beat_code', $userBeats)->values();
        return response()->json(['beat_list' => $beat_list->toArray()]);
    }

    public function RoutePlanAdd(Request $request)
    {
        $employee = auth()->user();
        try {
            $request->validate([
                'beat_code' => 'required|exists:beat_master,beat_code',
                'bdm_type' => 'required',
                'bdm_day' => 'required_if:bdm_type,2',
                'bdm_date' => 'required_if:bdm_type,3'
            ], [
                'beat_code.required' => 'Beat Code is required.',
                'beat_code.exists' => 'Beat not found',
                'bdm_type.required' => 'Type is required.',
                'bdm_day.required_if' => 'Day is required when type is set to Day.',
                'bdm_date.required_if' => 'Date is required when type is set to Date.'
            ]);

            $autoApprove = Options::where('key', 'route_plan_request_auto_approve')->first()->value ?? 0;

            $beat = new BeatDatewiseMaster();
            $beat->beat_code = $request->beat_code;
            $beat->emp_code = $employee->emp_code;
            $beat->bdm_type = $request->bdm_type;
            $beat->bdm_date = $request->bdm_date ?? null;
            $beat->bdm_day = $request->bdm_day ?? null;

            if ($autoApprove == '1') {
                $beat->bdm_status = 1; //approve
            } else {
                $beat->bdm_status = 0; //pending
            }

            $beat->save();

            return response()->json([
                'success' => TRUE,
                'message' => 'Route Plan added successfully',
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function GetTeamReportLIst()
    {
        $reportTypes = [
            ['title' => 'Attendance Report', 'value' => 'attendance_employee'],
            [
                'title' => 'Order Report',
                'value' => 'order_report',
                'filters' =>  [
                    [
                        'title' => 'Customer Report Type',
                        'value' => 'cust_report_type',
                        'customer_report_type' => [
                            ['title' => 'SS', 'value' => 'ss'],
                            ['title' => 'Distributor', 'value' => 'distributor'],
                            ['title' => 'Dealer', 'value' => 'dealer'],
                            ['title' => 'Retailer', 'value' => 'retailer']
                        ],
                    ]
                ]
            ],
            ['title' => 'Expense Report', 'value' => 'expense_report'],
        ];
        return response()->json(['report_types' => $reportTypes]);
    }

    public function TeamReports(Request $request)
    {
        try {
            $reportController = new ReportController;
            $getReport = $reportController->fetchReport($request);
            return $getReport;
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getNewApk(Request $request)
    {
        try {
            $request->validate([
                'url' => 'required',
            ], [
                'url.required' => 'Url is required'
            ]);

            $url = $request->url;

            return response()->json([
                'redirect_url' => $url
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => TRUE,
                'message' => $e->errors()
            ], 422);
        }
    }
}
