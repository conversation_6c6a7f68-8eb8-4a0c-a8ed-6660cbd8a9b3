<?php

namespace App\Http\Controllers;

use App\Models\BeatMaster;
use App\Models\CallLog;
use App\Models\CallMaster;
use App\Models\ClaimComplainMaster;
use App\Models\CmAnniBirthRelation;
use App\Models\CmBeatRelation;
use App\Models\CmCategoryRelation;
use App\Models\CmGradeRelation;
use App\Models\CmImageRelation;
use App\Models\CustomerCategoryMaster;
use App\Models\CustomerEmpRelation;
use App\Models\CustomerMaster;
use App\Models\DistrictMaster;
use App\Models\EmployeeMaster;
use App\Models\GradeMaster;
use App\Models\HqMaster;
use App\Models\MarketTypeMaster;
use App\Models\Options;
use App\Models\OrderMaster;
use App\Models\PaymentCollection;
use App\Models\StateMaster;
use App\Models\TownMaster;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class CustomersController extends Controller
{
    private function getCustomerData($type)
    {
        return CustomerMaster::with('cust_town_relation', 'market_type_master')
            ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', DB::raw('ST_X(cm_coordinates) as longitude'), DB::raw('ST_Y(cm_coordinates) as latitude'), 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
            ->where('cm_type', $type)
            ->get();
    }

    //list ss,distributor,dealer,retailer dynamically
    public function customer_list(Request $request, $type)
    {
        //stateList ,marketTypeList function to customHelper.php file
        $state_list = stateList();

        $districtList = DistrictMaster::all();
        $hqList = HqMaster::all();
        $townList = TownMaster::all();
        $marketType = marketTypeList();
        $gradeType = GradeMaster::all();
        $customerCategory = CustomerCategoryMaster::orderBy('ccm_name', 'asc')->get();

        //employeeList function to customHelper.php file
        $emp_list = employeeList();

        $beat_list = BeatMaster::orderBy('beat_name', 'asc')->get();

        $ss_list = $distributor_list = $dealer_list = $retailer_list = null;

        // getConfigDetails helper function from app/Helper/customHelper.php
        $configDetails = getConfigDetails();

        if (isset($configDetails['customerTypes'][$type]) && $configDetails['customerTypes'][$type]['status'] == 1) {
            switch ($type) {
                case 'ss':
                    $ss_list = $this->getCustomerData(2);
                    break;
                case 'distributor':
                    $distributor_list = $this->getCustomerData(3);
                    $ss_list = CustomerMaster::where('cm_type', 2)
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                case 'dealer':
                    $dealer_list = $this->getCustomerData(4);
                    $ss_list = CustomerMaster::where('cm_type', 2)
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                case 'retailer':
                    $retailer_list = $this->getCustomerData(5);
                    $ss_list = CustomerMaster::whereIn('cm_type', [3, 4]) //  3 - dist,4-dealer
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                default:
                    return response()->json(['error', 'Invalid customer type'], 404);
            }

            if ($request->debug === 'true') {
                $deleteCustomer = 0;
                $deleteOrder = 0;
                if ($request->deleteCustomer === 'true') {
                    $deleteCustomer = 1;
                }
                if ($request->deleteOrder === 'true') {
                    $deleteOrder = 1;
                }
                return view('layouts.stylesheets')
                    . view('layouts.header')
                    . view('layouts.sidebar')
                    . view('layouts.scripts')
                    . view('customers.' . $type . '_list_debug', compact($type . '_list', 'deleteCustomer', 'deleteOrder', 'state_list', 'districtList', 'emp_list', 'hqList', 'marketType', 'townList', 'ss_list', 'distributor_list', 'dealer_list', 'retailer_list', 'beat_list'))
                    . view('layouts.footer');
            } else {
                return view('layouts.stylesheets')
                    . view('layouts.header')
                    . view('layouts.sidebar')
                    . view('layouts.scripts')
                    . view('customers.' . $type . '_list', compact($type . '_list', 'state_list', 'districtList', 'emp_list', 'hqList', 'marketType', 'townList', 'ss_list', 'distributor_list', 'dealer_list', 'retailer_list', 'beat_list'))
                    . view('modal.' . $type . '_edit_detail_modal', compact('state_list', 'marketType', 'gradeType', 'customerCategory', 'emp_list', 'ss_list', 'beat_list'))
                    . view('layouts.footer');
            }
        } else {
            return view('errors.permission_denied');
        }
    }

    public function getNextAvailableCode($entityType)
    {
        // Fetch the prefix from the options table
        $customerPrefix = Options::where('key', $entityType . '_prefix')->first()->value;

        // extract the numeric part from the prefix
        $numericPart = intval(preg_replace('/[^0-9]+/', '', $customerPrefix), 10);

        //Find the maximum code in the CustomerMaster table that starts with the non-numeric part of the prefix
        $maxCmCode = CustomerMaster::where('cm_code', 'like', preg_replace('/[0-9]+/', '', $customerPrefix) . '%')->max('cm_code');

        // If a maximum code was found, increment the numeric part
        if ($maxCmCode) {
            $numericPart = intval(preg_replace('/[^0-9]+/', '', $maxCmCode), 10) + 1;
        }

        // Generate the next code
        $nextCode = preg_replace('/[0-9]+/', '', $customerPrefix) . str_pad($numericPart, strlen(preg_replace('/[^0-9]+/', '', $customerPrefix)), '0', STR_PAD_LEFT);

        return $nextCode;
    }

    //add ss, distributor, dealer, retailer dynamically
    public function add_customer($type)
    {
        //stateList ,marketTypeList function to customHelper.php file
        $state_list = stateList();
        $marketType = marketTypeList();
        $gradeType = GradeMaster::all();
        $customerCategory = CustomerCategoryMaster::orderBy('ccm_name', 'asc')->get();
        $emp_list = EmployeeMaster::where('emp_status', '1')->orderBy('emp_name', 'asc')->get();
        $beat_list = BeatMaster::orderBy('beat_name', 'asc')->where('beat_status', 1)->get();
        $nextCode = null;
        $ss_list = null;

        // getConfigDetails helper function from app/Helper/customHelper.php
        $configDetails = getConfigDetails();

        if (isset($configDetails['customerTypes'][$type]) && $configDetails['customerTypes'][$type]['status'] == 1) {
            switch ($type) {
                case 'ss':
                    $nextCode = $this->getNextAvailableCode('ss');
                    break;
                case 'distributor':
                    $nextCode = $this->getNextAvailableCode('distributor');
                    $ss_list = CustomerMaster::where('cm_type', 2)
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                case 'dealer':
                    $nextCode = $this->getNextAvailableCode('dealer');
                    $ss_list = CustomerMaster::where('cm_type', 2)
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                case 'retailer':
                    $ss_list = CustomerMaster::whereIn('cm_type', [3, 4]) // 2 - ss, 3 - dist, 4 - dealer
                        ->where('cm_status', 1)
                        ->orderBy('cm_name', 'asc')
                        ->get();
                    break;
                default:
                    return response()->json(['error', 'Invalid customer type'], 404);
            }

            return view('layouts.stylesheets')
                . view('layouts.header')
                . view('layouts.sidebar')
                . view('layouts.scripts')
                . view('customers.add_' . $type, compact('emp_list', 'state_list', 'marketType', 'gradeType', 'customerCategory', 'ss_list', 'beat_list', 'nextCode'))
                . view('modal.add_customer_state_modal')
                . view('modal.add_customer_district_modal', compact('state_list'))
                . view('modal.add_customer_town_modal', compact('state_list'))
                . view('modal.add_location_marker_modal')
                . view('layouts.footer');
        } else {
            return view('errors.permission_denied');
        }
    }

    //save ss, distributor, dealer, retailer dynamically
    public function save_customer(Request $request, $type)
    {
        $cm_code = $request->cm_code;
        $cm_login_pin = substr($cm_code, -4) ?? null;
        $cm_name = $request->cm_name;
        $cm_mobile = $request->cm_mobile;
        $cm_email = $request->cm_email ?? null;
        $cm_address = $request->cm_address;
        $cm_pincode = $request->cm_pincode;
        $cm_gst = $request->cm_gst ?? null;
        $cm_pan = $request->cm_pan ?? null;

        if ($type == 'ss') {
            try {

                $validator = $this->saveCustomerValidator($request, $type, 2);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $ss = new CustomerMaster();
                $ss->cm_code = $cm_code;
                $ss->cm_login_pin = $cm_login_pin;
                $ss->cm_name = $cm_name;
                $ss->cm_mobile = $cm_mobile;
                $ss->cm_email = $cm_email;
                $ss->cm_address = $cm_address;
                $ss->cm_pincode = $cm_pincode;
                $ss->cm_gst = $cm_gst;
                $ss->cm_pan = $cm_pan;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $ss->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $ss->cm_market_type = $request->cm_market_type;
                $ss->cm_status = 1; // 1-active, 0-inactive
                $ss->cm_town_id = $request->cm_town_id;
                $ss->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $ss->cm_contact_person = $request->cm_contact_person;
                $ss->cm_area = $request->cm_area ?? null;
                $ss->cm_relation_code = null;
                $ss->created_emp_code = $request->created_emp_code ?? null;
                $ss->cm_type = 2; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $ss->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $anniBirthRel = new CmAnniBirthRelation();
                    $anniBirthRel->cm_code = $ss->cm_code;
                    $anniBirthRel->birth_date = $request->birth_date ?? null;
                    $anniBirthRel->anni_date = $request->anni_date ?? null;
                    $anniBirthRel->save();
                }

                $assign_to = $request->assign_to;
                if ($assign_to) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $ss->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                session()->flash('success', 'SS added successfully');
                return response()->json(['redirect' => route('customer_list', ['type' => 'ss'])]);
            } catch (Exception $e) {
                return response()->json(['message' => 'Error while adding SS: ' . $e->getMessage()], 500);
            }
        }
        if ($type == 'distributor') {
            try {

                $validator = $this->saveCustomerValidator($request, $type, 3);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $dist = new CustomerMaster();
                $dist->cm_code = $cm_code;
                $dist->cm_login_pin = $cm_login_pin;
                $dist->cm_name = $cm_name;
                $dist->cm_mobile = $cm_mobile;
                $dist->cm_email = $cm_email;
                $dist->cm_address = $cm_address;
                $dist->cm_pincode = $cm_pincode;
                $dist->cm_gst = $cm_gst;
                $dist->cm_pan = $cm_pan;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $dist->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $dist->cm_market_type = $request->cm_market_type;
                $dist->cm_status = 1; // 1-active, 0-inactive
                $dist->cm_town_id = $request->cm_town_id;
                $dist->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $dist->cm_contact_person = $request->cm_contact_person;
                $dist->cm_area = $request->cm_area ?? null;
                $dist->cm_relation_code = $request->parent_ss ?? null;
                $dist->created_emp_code = $request->created_emp_code ?? null;
                $dist->cm_type = 3; // 1-company, 2-ss, 3-dist, 4-dealer, 5
                $dist->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $anniBirthRel = new CmAnniBirthRelation();
                    $anniBirthRel->cm_code = $dist->cm_code;
                    $anniBirthRel->birth_date = $request->birth_date ?? null;
                    $anniBirthRel->anni_date = $request->anni_date ?? null;
                    $anniBirthRel->save();
                }

                $assign_to = $request->assign_to;
                if ($assign_to) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $dist->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                session()->flash('success', 'Distributor added successfully');
                return response()->json(['redirect' => route('customer_list', ['type' => 'distributor'])]);
            } catch (Exception $e) {
                return redirect()->back()->with('error', 'Error while adding Distributor:' . $e->getMessage());
            }
        }
        if ($type == 'dealer') {
            try {

                $validator = $this->saveCustomerValidator($request, $type, 4);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $ss = new CustomerMaster();
                $ss->cm_code = $cm_code;
                $ss->cm_login_pin = $cm_login_pin;
                $ss->cm_name = $cm_name;
                $ss->cm_mobile = $cm_mobile;
                $ss->cm_email = $cm_email;
                $ss->cm_address = $cm_address;
                $ss->cm_pincode = $cm_pincode;
                $ss->cm_gst = $cm_gst;
                $ss->cm_pan = $cm_pan;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $ss->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $ss->cm_market_type = $request->cm_market_type;
                $ss->cm_status = 1; // 1-active, 0-inactive
                $ss->cm_town_id = $request->cm_town_id;
                $ss->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $ss->cm_contact_person = $request->cm_contact_person;
                $ss->cm_area = $request->cm_area ?? null;
                $ss->cm_relation_code = $request->parent_ss ?? null;
                $ss->created_emp_code = $request->created_emp_code ?? null;
                $ss->cm_type = 4; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $ss->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $anniBirthRel = new CmAnniBirthRelation();
                    $anniBirthRel->cm_code = $ss->cm_code;
                    $anniBirthRel->birth_date = $request->birth_date ?? null;
                    $anniBirthRel->anni_date = $request->anni_date ?? null;
                    $anniBirthRel->save();
                }

                $assign_to = $request->assign_to;
                if ($assign_to) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $ss->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                session()->flash('success', 'Dealer added successfully');
                return response()->json(['redirect' => route('customer_list', ['type' => 'dealer'])]);
            } catch (Exception $e) {
                return response()->json(['message' => 'Error while adding Dealer: ' . $e->getMessage()], 500);
            }
        }
        if ($type == 'retailer') {
            try {
                $validator = Validator::make($request->all(), [
                    'cm_mobile' => [
                        'required',
                        'numeric',
                        'min:10',
                        function ($attribute, $value, $fail) use ($request) {
                            $exists = CustomerMaster::where('cm_mobile', $value)
                                ->where('cm_type', 5)
                                ->exists();
                            if ($exists) {
                                $fail('The mobile number has already been taken in retailer.');
                            }
                        },
                    ],
                    'cm_mobile2' => 'nullable|numeric|min:10',
                    'state_name' => 'required',
                    'district_name' => 'required',
                    'cm_town_id' => 'required',
                    'beat_assign' => 'required',
                    'grade_type' => 'required',
                    'cm_name' => 'required',
                    'cm_contact_person' => 'required',
                    'cm_market_type' => 'required',
                    'cm_pincode' => 'required',
                    'cust_category' => 'required',
                    'cm_area' => 'required',
                    'cm_address' => 'required',
                ], [
                    'cm_mobile.required' => 'Mobile Number is required',
                    'cm_mobile.numeric' => 'Mobile Number must be numeric',
                    'cm_mobile.min' => 'Mobile Number must be at least :min characters',
                    'cm_mobile2.numeric' => 'Second mobile number must be numeric',
                    'cm_mobile2.min' => 'Second mobile number must be at least :min characters',
                    'state_name.required' => 'State is required',
                    'district_name.required' => 'District is required',
                    'cm_town_id.required' => 'Town is required',
                    'beat_assign.required' => 'Beat is required',
                    'grade_type.required' => 'Grade type is required',
                    'cm_name.required' => 'Name is required',
                    'cm_contact_person.required' => 'Contact Person Name is required',
                    'cm_market_type.required' => 'Market Type is required',
                    'cm_pincode.required' => 'Pincode is required',
                    'cm_pincode.numeric' => 'Pincode must be numeric',
                    'cm_pincode.min' => 'Pincode must be at least :min characters',
                    'cust_category.required' => 'Category is required',
                    'cm_area.required' => 'Area is required',
                    'cm_address.required' => 'Address is required',
                ]);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $retailer = new CustomerMaster();
                $retailer->cm_code = Str::uuid();
                // $retailer->cm_login_pin = $cm_login_pin;
                $retailer->cm_name = $cm_name;
                $retailer->cm_mobile = $cm_mobile;
                $retailer->cm_mobile2 = $request->cm_mobile2 ?? null;
                $retailer->cm_email = $cm_email;
                $retailer->cm_address = $cm_address;
                $retailer->cm_pincode = $cm_pincode;
                $retailer->cm_gst = $cm_gst;
                $retailer->cm_pan = $cm_pan;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $retailer->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $retailer->cm_market_type = $request->cm_market_type;
                $retailer->cm_status = 1; // 1-active, 0-inactive
                $retailer->cm_town_id = $request->cm_town_id;
                $retailer->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $retailer->cm_contact_person = $request->cm_contact_person;
                $retailer->cm_area = $request->cm_area ?? null;
                $retailer->cm_relation_code = $request->parent_ss ?? null;
                $retailer->created_emp_code = $request->created_emp_code ?? null;
                $retailer->cm_type = 5; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $retailer->save();

                // save beat relation
                if (!empty($request->beat_assign)) {
                    $cmBeatRel = new CmBeatRelation();
                    $cmBeatRel->cm_code = $retailer->cm_code;
                    $cmBeatRel->beat_code = $request->beat_assign;
                    $cmBeatRel->save();
                }

                // Save grade relation
                if (!empty($request->grade_type)) {
                    $cmGradeRel = new CmGradeRelation();
                    $cmGradeRel->cm_code = $retailer->cm_code;
                    $cmGradeRel->grade_id = $request->grade_type;
                    $cmGradeRel->save();
                }

                // Save category relation
                if (!empty($request->cust_category)) {
                    $cmCategoryRel = new CmCategoryRelation();
                    $cmCategoryRel->cm_code = $retailer->cm_code;
                    $cmCategoryRel->category_id = $request->cust_category;
                    $cmCategoryRel->save();
                }

                // Save anniversary and birthdate relation
                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $anniBirthRel = new CmAnniBirthRelation();
                    $anniBirthRel->cm_code = $retailer->cm_code;
                    $anniBirthRel->birth_date = $request->birth_date ?? null;
                    $anniBirthRel->anni_date = $request->anni_date ?? null;
                    $anniBirthRel->save();
                }

                // Save employee relations
                $assign_to = $request->assign_to;
                if ($assign_to) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $retailer->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                // Save retailer images
                if ($request->hasFile('retailer_images')) {
                    $images = $request->file('retailer_images');
                    $imageNames = [];
                    foreach ($images as $image) {
                        $name = uniqid() . '.' . $image->getClientOriginalExtension();
                        $destinationPath = public_path('/uploads/retailer_images');
                        $image->move($destinationPath, $name);
                        $imageNames[] = $name;
                    }
                    $cst_img_rel = new CmImageRelation();
                    $cst_img_rel->cm_code = $retailer->cm_code;
                    $cst_img_rel->image_name = implode(',', $imageNames);
                    $cst_img_rel->save();
                }

                session()->flash('success', 'Retailer added successfully');
                return response()->json(['redirect' => route('customer_list', ['type' => 'retailer'])]);
            } catch (Exception $e) {
                return response()->json(['error' => 'Error while adding Retailer:' . $e->getMessage()]);
            }
        }
    }

    private function saveCustomerValidator($request, $type, $csttype)
    {
        return Validator::make($request->all(), [
            'cm_code' => 'required|unique:customer_master,cm_code',
            'cm_mobile' => [
                'required',
                'numeric',
                'min:10',
                function ($attribute, $value, $fail) use ($request, $csttype, $type) {
                    $exists = CustomerMaster::where('cm_mobile', $value)
                        ->where('cm_type', $csttype)
                        ->exists();
                    if ($exists) {
                        $fail('The mobile number has already been taken in ' . $type . '.');
                    }
                },
            ],
            'state_name' => 'required',
            'district_name' => 'required',
            'cm_town_id' => 'required',
            'cm_name' => 'required',
            'cm_contact_person' => 'required',
            'cm_market_type' => 'required',
            'cm_pincode' => 'required|numeric|min:6',
            'cm_area' => 'required',
            'cm_address' => 'required',
        ], [
            'cm_code.required' => 'Code is required',
            'cm_code.unique' => 'Code has already been taken.',
            'cm_mobile.required' => 'Mobile Number is required',
            'cm_mobile.numeric' => 'Mobile Number must be numeric',
            'cm_mobile.min' => 'Mobile No must be at least :min characters',
            'state_name.required' => 'State is required',
            'district_name.required' => 'District is required',
            'cm_town_id.required' => 'Town is required',
            'cm_name.required' => 'Name is required',
            'cm_contact_person.required' => 'Contact Person Name is required',
            'cm_market_type.required' => 'Market Type is required',
            'cm_pincode.required' => 'Pincode is required',
            'cm_pincode.numeric' => 'Pincode must be numeric',
            'cm_pincode.min' => 'Pincode must be at least :min characters',
            'cm_area.required' => 'Area is required',
            'cm_address.required' => 'Address is required',
        ]);
    }

    //edit ss, distributor, dealer, retailer dynamically
    public function edit_customer($type, $cm_code)
    {
        $cstType = CustomerMaster::with(['anni_birth_relation', 'market_type_master', 'cm_image_relation', 'cm_beat_relation', 'cm_category_relation', 'cm_grade_relation'])
            ->select(
                'cm_code',
                'cm_code',
                'cm_login_pin',
                'cm_name',
                'cm_mobile',
                'cm_mobile2',
                'cm_email',
                'cm_address',
                'cm_pincode',
                'cm_gst',
                'cm_pan',
                DB::raw('ST_Y(cm_coordinates) as latitude'),
                DB::raw('ST_X(cm_coordinates) as longitude'),
                'cm_market_type',
                'cm_status',
                'cm_town_id',
                'cm_outstanding_amount',
                'cm_contact_person',
                'cm_area',
                'cm_relation_code',
                'created_emp_code',
                'cm_type'
            )->find($cm_code);
        if ($type == 'ss') {
            try {
                $ss = $cstType;

                $anni_date = null;
                $birth_date = null;
                if ($ss->anni_birth_relation) {
                    $anni_date = $ss->anni_birth_relation->anni_date;
                    $birth_date = $ss->anni_birth_relation->birth_date;
                }

                $ss->anni_date = $anni_date;
                $ss->birth_date = $birth_date;

                $town = TownMaster::find($ss->cm_town_id);
                $district = DistrictMaster::find($town->district_id);
                $state = StateMaster::find($district->state_id);

                $ss->state_id = $state->state_id;
                $ss->district_id = $district->dm_id;
                $ss->town_id = $town->town_id;

                $marketType = $ss->market_type_master->mtm_id;
                $ss->marketType = $marketType;

                $emp_ids = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $ss->cm_code)
                    ->pluck('employee_master.emp_code');
                $ss->emp_ids = $emp_ids;

                $cst_emp_assign_name = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $ss->cm_code)
                    ->select('employee_master.emp_code', 'employee_master.emp_name', 'employee_master.emp_code')
                    ->get();

                $ss->cst_emp_assign_name = $cst_emp_assign_name->map(function ($employee) {
                    return $employee->emp_name . ' (' . $employee->emp_code . ')';
                });

                $ssPayColl = PaymentCollection::with('payment_type', 'bank', 'customer', 'employee')
                    ->whereHas('customer', function ($query) use ($cm_code) {
                        $query->where('cm_code', $cm_code)
                            ->where('cm_type', 2);
                    })
                    ->get();
                $ss->payColSsData = $ssPayColl;

                return response()->json([
                    'status' => 'success',
                    'data' => $ss,
                ]);
            } catch (Exception $e) {
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ], 404);
            }
        }
        if ($type == 'distributor') {
            try {
                $dist = $cstType;

                $anni_date = null;
                $birth_date = null;
                if ($dist->anni_birth_relation) {
                    $anni_date = $dist->anni_birth_relation->anni_date;
                    $birth_date = $dist->anni_birth_relation->birth_date;
                }

                $dist->anni_date = $anni_date;
                $dist->birth_date = $birth_date;

                $town = TownMaster::find($dist->cm_town_id);
                $district = DistrictMaster::find($town->district_id);
                $state = StateMaster::find($district->state_id);

                $dist->state_id = $state->state_id;
                $dist->district_id = $district->dm_id;
                $dist->town_id = $town->town_id;

                $marketType = $dist->market_type_master->mtm_id;
                $dist->marketType = $marketType;

                $emp_ids = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $dist->cm_code)
                    ->pluck('employee_master.emp_code');
                $dist->emp_ids = $emp_ids;

                $cst_emp_assign_name = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $dist->cm_code)
                    ->select('employee_master.emp_code', 'employee_master.emp_name', 'employee_master.emp_code')
                    ->get();

                $dist->cst_emp_assign_name = $cst_emp_assign_name->map(function ($employee) {
                    return $employee->emp_name . ' (' . $employee->emp_code . ')';
                });

                return response()->json([
                    'status' => 'success',
                    'data' => $dist,
                ]);
            } catch (Exception $e) {
                //log error
                Log::error($e->getMessage());
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ], 404);
            }
        }
        if ($type == 'dealer') {
            try {
                $dealer = $cstType;

                $anni_date = null;
                $birth_date = null;
                if ($dealer->anni_birth_relation) {
                    $anni_date = $dealer->anni_birth_relation->anni_date;
                    $birth_date = $dealer->anni_birth_relation->birth_date;
                }

                $dealer->anni_date = $anni_date;
                $dealer->birth_date = $birth_date;

                $town = TownMaster::find($dealer->cm_town_id);
                $district = DistrictMaster::find($town->district_id);
                $state = StateMaster::find($district->state_id);

                $dealer->state_id = $state->state_id;
                $dealer->district_id = $district->dm_id;
                $dealer->town_id = $town->town_id;

                $marketType = $dealer->market_type_master->mtm_id;
                $dealer->marketType = $marketType;

                $emp_ids = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $dealer->cm_code)
                    ->pluck('employee_master.emp_code');
                $dealer->emp_ids = $emp_ids;

                $cst_emp_assign_name = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $dealer->cm_code)
                    ->select('employee_master.emp_code', 'employee_master.emp_name', 'employee_master.emp_code')
                    ->get();

                $dealer->cst_emp_assign_name = $cst_emp_assign_name->map(function ($employee) {
                    return $employee->emp_name . ' (' . $employee->emp_code . ')';
                });

                return response()->json([
                    'status' => 'success',
                    'data' => $dealer,
                ]);
            } catch (Exception $e) {
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ], 404);
            }
        }
        if ($type == 'retailer') {
            try {
                $retailer = $cstType;

                $anni_date = null;
                $birth_date = null;
                if ($retailer->anni_birth_relation) {
                    $anni_date = $retailer->anni_birth_relation->anni_date;
                    $birth_date = $retailer->anni_birth_relation->birth_date;
                }

                $retailer->anni_date = $anni_date;
                $retailer->birth_date = $birth_date;

                $town = TownMaster::find($retailer->cm_town_id);
                $district = DistrictMaster::find($town->district_id);
                $state = StateMaster::find($district->state_id);

                $retailer->state_id = $state->state_id;
                $retailer->district_id = $district->dm_id;
                $retailer->town_id = $town->town_id;

                $marketType = $retailer->market_type_master->mtm_id;
                $retailer->marketType = $marketType;

                $beatRel = DB::table('cm_beat_relation')
                    ->select('beat_code')
                    ->where('cm_code', $cm_code)
                    ->first();

                // Add beatRel to retailer data
                $retailer->beatRel = $beatRel ? $beatRel->beat_code : null;

                // $gradeRel = $retailer->cm_grade_ralation->grade_id;
                // $retailer->gradeRel = $gradeRel;
                $gradeRel = DB::table('cm_grade_relation')
                    ->select('grade_id')
                    ->where('cm_code', $cm_code)
                    ->first();

                // Add gradeRel to retailer data
                $retailer->gradeRel = $gradeRel ? $gradeRel->grade_id : null;

                // Fetch category relation manually (using optional() to handle null)
                $categoryRel = DB::table('cm_category_relation')
                    ->select('category_id')
                    ->where('cm_code', $cm_code)
                    ->first();

                // Add categoryRel to retailer data
                $retailer->categoryRel = $categoryRel ? $categoryRel->category_id : null;

                $emp_ids = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $retailer->cm_code)
                    ->pluck('employee_master.emp_code');
                $retailer->emp_ids = $emp_ids;

                $cst_emp_assign_name = EmployeeMaster::join('customer_emp_relation', 'employee_master.emp_code', '=', 'customer_emp_relation.emp_code')
                    ->where('customer_emp_relation.cm_code', $retailer->cm_code)
                    ->select('employee_master.emp_code', 'employee_master.emp_name', 'employee_master.emp_code')
                    ->get();

                $retailer->cst_emp_assign_name = $cst_emp_assign_name->map(function ($employee) {
                    return $employee->emp_name . ' (' . $employee->emp_code . ')';
                });

                // get retailer images
                $imageNames = [];
                foreach ($retailer->cm_image_relation as $relation) {
                    $imageNames[] = $relation->image_name;
                }
                $retailer->retailer_image = $imageNames;

                return response()->json([
                    'status' => 'success',
                    'data' => $retailer,
                ]);
            } catch (Exception $e) {
                //log error
                Log::error($e->getMessage());
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ], 404);
            }
        }
    }

    //update ss, distributor, dealer, retailer dynamically
    public function update_customer(Request $request, $type)
    {
        if ($type == 'ss') {
            try {
                $validator = $this->updateCustomerValidator($request, $type, 2);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $ss = CustomerMaster::find($request->cm_code);
                //$ss->cm_code = $request->cm_code;
                $ss->cm_name = $request->cm_name;
                $ss->cm_mobile = $request->cm_mobile;
                $ss->cm_email = $request->cm_email ?? null;
                $ss->cm_address = $request->cm_address;
                $ss->cm_pincode = $request->cm_pincode;
                $ss->cm_gst = $request->cm_gst ?? null;
                $ss->cm_pan = $request->cm_pan ?? null;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $ss->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $ss->cm_market_type = $request->cm_market_type;
                $ss->cm_status = $request->cm_status;
                $ss->cm_town_id = $request->cm_town_id;
                $ss->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $ss->cm_contact_person = $request->cm_contact_person;
                $ss->cm_area = $request->cm_area ?? null;
                $ss->cm_relation_code = null;
                $ss->created_emp_code = $request->created_emp_code ?? null;
                $ss->cm_type = 2; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $ss->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $aniBirthRel = CmAnniBirthRelation::where('cm_code', $ss->cm_code)->first();
                    if ($aniBirthRel) {
                        $aniBirthRel->birth_date = $request->birth_date ?? null;
                        $aniBirthRel->anni_date = $request->anni_date ?? null;
                        $aniBirthRel->save();
                    } else {
                        $anniBirthRel = new CmAnniBirthRelation();
                        $anniBirthRel->cm_code = $ss->cm_code;
                        $anniBirthRel->birth_date = $request->birth_date ?? null;
                        $anniBirthRel->anni_date = $request->anni_date ?? null;
                        $anniBirthRel->save();
                    }
                }

                // Always delete existing CustomerEmpRelation records
                CustomerEmpRelation::where('cm_code', $ss->cm_code)->delete();

                // Insert new CustomerEmpRelation records if assign_to is provided
                $assign_to = $request->assign_to;
                if (!empty($assign_to)) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $ss->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                return response()->json(['status' => 'success', 'message' => 'SS Updated Successfully']);
            } catch (Exception $e) {
                Log::error('Error updating ss: ' . $e->getMessage());
                return response()->json(['message' => 'An error occurred while updating the ss: ' . $e->getMessage()], 500);
            }
        }
        if ($type == 'distributor') {
            try {
                $validator = $this->updateCustomerValidator($request, $type, 3);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $dist = CustomerMaster::find($request->cm_code);
                $dist->cm_name = $request->cm_name;
                $dist->cm_mobile = $request->cm_mobile;
                $dist->cm_email = $request->cm_email ?? null;
                $dist->cm_address = $request->cm_address;
                $dist->cm_pincode = $request->cm_pincode;
                $dist->cm_gst = $request->cm_gst ?? null;
                $dist->cm_pan = $request->cm_pan ?? null;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $dist->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $dist->cm_market_type = $request->cm_market_type;
                $dist->cm_status = $request->cm_status;
                $dist->cm_town_id = $request->cm_town_id;
                $dist->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $dist->cm_contact_person = $request->cm_contact_person;
                $dist->cm_area = $request->cm_area ?? null;
                $dist->cm_relation_code = $request->parent_ss ?? null;
                $dist->created_emp_code = $request->created_emp_code ?? null;
                $dist->cm_type = 3; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $dist->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $aniBirthRel = CmAnniBirthRelation::where('cm_code', $dist->cm_code)->first();
                    if ($aniBirthRel) {
                        $aniBirthRel->birth_date = $request->birth_date ?? null;
                        $aniBirthRel->anni_date = $request->anni_date ?? null;
                        $aniBirthRel->save();
                    } else {
                        $anniBirthRel = new CmAnniBirthRelation();
                        $anniBirthRel->cm_code = $dist->cm_code;
                        $anniBirthRel->birth_date = $request->birth_date ?? null;
                        $anniBirthRel->anni_date = $request->anni_date ?? null;
                        $anniBirthRel->save();
                    }
                }

                // Always delete existing CustomerEmpRelation records
                CustomerEmpRelation::where('cm_code', $dist->cm_code)->delete();

                // Insert new CustomerEmpRelation records if assign_to is provided
                $assign_to = $request->assign_to;
                if (!empty($assign_to)) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $dist->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                return response()->json(['status' => 'success', 'message' => 'Distributor Updated Successfully']);
            } catch (Exception $e) {
                Log::error('Error updating distributor: ' . $e->getMessage());
                return response()->json(['message' => 'An error occurred while updating the distributor: ' . $e->getMessage()], 500);
            }
        }
        if ($type == 'dealer') {
            try {
                $validator = $this->updateCustomerValidator($request, $type, 4);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }

                $dealer = CustomerMaster::find($request->cm_code);
                //$dealer->cm_code = $request->cm_code;
                $dealer->cm_name = $request->cm_name;
                $dealer->cm_mobile = $request->cm_mobile;
                $dealer->cm_email = $request->cm_email ?? null;
                $dealer->cm_address = $request->cm_address;
                $dealer->cm_pincode = $request->cm_pincode;
                $dealer->cm_gst = $request->cm_gst ?? null;
                $dealer->cm_pan = $request->cm_pan ?? null;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $dealer->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }
                $dealer->cm_market_type = $request->cm_market_type;
                $dealer->cm_status = $request->cm_status;
                $dealer->cm_town_id = $request->cm_town_id;
                $dealer->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $dealer->cm_contact_person = $request->cm_contact_person;
                $dealer->cm_area = $request->cm_area ?? null;
                $dealer->cm_relation_code = $request->parent_ss ?? null;
                $dealer->created_emp_code = $request->created_emp_code ?? null;
                $dealer->cm_type = 4; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $dealer->save();

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $aniBirthRel = CmAnniBirthRelation::where('cm_code', $dealer->cm_code)->first();
                    if ($aniBirthRel) {
                        $aniBirthRel->birth_date = $request->birth_date ?? null;
                        $aniBirthRel->anni_date = $request->anni_date ?? null;
                        $aniBirthRel->save();
                    } else {
                        $anniBirthRel = new CmAnniBirthRelation();
                        $anniBirthRel->cm_code = $dealer->cm_code;
                        $anniBirthRel->birth_date = $request->birth_date ?? null;
                        $anniBirthRel->anni_date = $request->anni_date ?? null;
                        $anniBirthRel->save();
                    }
                }

                // Always delete existing CustomerEmpRelation records
                CustomerEmpRelation::where('cm_code', $dealer->cm_code)->delete();

                // Insert new CustomerEmpRelation records if assign_to is provided
                $assign_to = $request->assign_to;
                if (!empty($assign_to)) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $dealer->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                return response()->json(['status' => 'success', 'message' => 'Dealer Updated Successfully']);
            } catch (Exception $e) {
                Log::error('Error updating dealer: ' . $e->getMessage());
                return response()->json(['message' => 'An error occurred while updating the dealer: ' . $e->getMessage()], 500);
            }
        }
        if ($type == 'retailer') {
            try {
                $validator = Validator::make($request->all(), [
                    // 'cm_code' => 'required',
                    'cm_code' => 'required',
                    'cm_mobile' => [
                        'required',
                        'numeric',
                        'min:10',
                        function ($attribute, $value, $fail) use ($request) {
                            $exists = CustomerMaster::where('cm_mobile', $value)
                                ->where('cm_type', 5)
                                ->where('cm_code', '!=', $request->cm_code)
                                ->exists();
                            if ($exists) {
                                $fail('The mobile number has already been taken in retailer.');
                            }
                        },
                    ],
                    'state_name' => 'required',
                    'district_name' => 'required',
                    'cm_town_id' => 'required',
                    'beat_assign' => 'required',
                    'grade_type' => 'required',
                    'cm_name' => 'required',
                    'cm_contact_person' => 'required',
                    'cm_market_type' => 'required',
                    'cm_pincode' => 'required|min:6',
                    'cust_category' => 'required',
                    'cm_area' => 'required',
                    'cm_address' => 'required',
                ], [
                    //'cm_code.required' => 'Id not found',
                    'cm_code.required' => 'Code is required',
                    'cm_mobile.required' => 'Mobile Number is required',
                    'cm_mobile.min' => 'Mobile Number must be at least :min characters',
                    // 'cm_mobile.unique' => 'Mobile Number has already been taken.',
                    'state_name.required' => 'State is required',
                    'district_name.required' => 'District is required',
                    'cm_town_id.required' => 'Town is required',
                    'beat_assign.required' => 'Beat is required',
                    'grade_type.required' => 'Grade type is required',
                    'cm_name.required' => 'Name is required',
                    'cm_contact_person.required' => 'Contact Person Name is required',
                    'cm_market_type.required' => 'Market Type is required',
                    'cm_pincode.required' => 'Pincode is required',
                    'cm_pincode.min' => 'Pincode must be at least :min characters',
                    'cust_category.required' => 'Category is required',
                    'cm_area.required' => 'Area is required',
                    'cm_address.required' => 'Address is required',
                ]);

                if ($validator->fails()) {
                    return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
                }
                $retailer = CustomerMaster::find($request->cm_code);
                //$retailer->cm_code = $request->cm_code;
                $retailer->cm_name = $request->cm_name;
                $retailer->cm_mobile = $request->cm_mobile;
                $retailer->cm_mobile2 = $request->cm_mobile2 ?? null;
                $retailer->cm_email = $request->cm_email ?? null;
                $retailer->cm_address = $request->cm_address;
                $retailer->cm_pincode = $request->cm_pincode;
                $retailer->cm_gst = $request->cm_gst ?? null;
                $retailer->cm_pan = $request->cm_pan ?? null;

                if (!empty($request->latitude) && !empty($request->longitude)) {
                    $latitude = $request->latitude;
                    $longitude = $request->longitude;
                    $retailer->cm_coordinates = DB::raw("POINT($longitude,$latitude)");
                }

                $retailer->cm_market_type = $request->cm_market_type;
                $retailer->cm_status = $request->cm_status;
                $retailer->cm_town_id = $request->cm_town_id;
                $retailer->cm_outstanding_amount = $request->cm_outstanding_amount ?? 0;
                $retailer->cm_contact_person = $request->cm_contact_person;
                $retailer->cm_area = $request->cm_area ?? null;
                $retailer->cm_relation_code = $request->parent_ss ?? null;
                $retailer->created_emp_code = $request->created_emp_code ?? null;
                $retailer->cm_type = 5; // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                $retailer->save();

                // Update beat relation
                CmBeatRelation::updateOrCreate(
                    ['cm_code' => $retailer->cm_code],
                    ['beat_code' => $request->beat_assign]
                );

                // Update grade relation
                CmGradeRelation::updateOrCreate(
                    ['cm_code' => $retailer->cm_code],
                    ['grade_id' => $request->grade_type]
                );

                // Update category relation
                CmCategoryRelation::updateOrCreate(
                    ['cm_code' => $retailer->cm_code],
                    ['category_id' => $request->cust_category]
                );

                if (!empty($request->birth_date) || !empty($request->anni_date)) {
                    $aniBirthRel = CmAnniBirthRelation::where('cm_code', $retailer->cm_code)->first();
                    if ($aniBirthRel) {
                        $aniBirthRel->birth_date = $request->birth_date ?? null;
                        $aniBirthRel->anni_date = $request->anni_date ?? null;
                        $aniBirthRel->save();
                    } else {
                        $anniBirthRel = new CmAnniBirthRelation();
                        $anniBirthRel->cm_code = $retailer->cm_code;
                        $anniBirthRel->birth_date = $request->birth_date ?? null;
                        $anniBirthRel->anni_date = $request->anni_date ?? null;
                        $anniBirthRel->save();
                    }
                }

                // Update employee relations
                CustomerEmpRelation::where('cm_code', $retailer->cm_code)->delete();
                $assign_to = $request->assign_to;
                if ($assign_to) {
                    foreach ($assign_to as $emp_code) {
                        $cst_emp_rel = new CustomerEmpRelation();
                        $cst_emp_rel->emp_code = $emp_code;
                        $cst_emp_rel->cm_code = $retailer->cm_code;
                        $cst_emp_rel->save();
                    }
                }

                // Update retailer images
                if ($request->hasFile('retailer_images')) {
                    $images = $request->file('retailer_images');
                    $imageNames = [];
                    foreach ($images as $image) {
                        $name = uniqid() . '.' . $image->getClientOriginalExtension();
                        $destinationPath = public_path('/uploads/retailer_images');
                        $image->move($destinationPath, $name);
                        $imageNames[] = $name;
                    }
                    CmImageRelation::updateOrCreate(
                        ['cm_code' => $retailer->cm_code],
                        ['image_name' => implode(',', $imageNames)]
                    );
                }

                return response()->json(['status' => 'success', 'message' => 'Retailer Updated Successfully']);
            } catch (Exception $e) {
                Log::error('Error updating retailer: ' . $e->getMessage());
                return response()->json(['message' => 'An error occurred while updating the retailer: ' . $e->getMessage()], 500);
            }
        }
    }

    private function updateCustomerValidator($request, $type, $csttype)
    {
        return Validator::make($request->all(), [
            'cm_code' => 'required',
            'cm_mobile' => [
                'required',
                'numeric',
                'min:10',
                function ($attribute, $value, $fail) use ($request, $type, $csttype) {
                    $exists = CustomerMaster::where('cm_mobile', $value)
                        ->where('cm_type', $csttype)
                        ->where('cm_code', '!=', $request->cm_code)
                        ->exists();
                    if ($exists) {
                        $fail('The mobile number has already been taken in ' . $type . '.');
                    }
                },
            ],
            'state_name' => 'required',
            'district_name' => 'required',
            'cm_town_id' => 'required',
            'cm_name' => 'required',
            'cm_contact_person' => 'required',
            'cm_market_type' => 'required',
            'cm_pincode' => 'required|min:6',
            'cm_area' => 'required',
            'cm_address' => 'required',
        ], [
            //'cm_code.required' => 'Id not found',
            'cm_code.required' => 'Code is required',
            'cm_mobile.required' => 'Mobile Number is required',
            'cm_mobile.min' => 'Mobile Number must be at least :min characters',
            // 'cm_mobile.unique' => 'Mobile Number has already been taken.',
            'state_name.required' => 'State is required',
            'district_name.required' => 'District is required',
            'cm_town_id.required' => 'Town is required',
            'cm_name.required' => 'Name is required',
            'cm_contact_person.required' => 'Contact Person Name is required',
            'cm_market_type.required' => 'Market Type is required',
            'cm_pincode.required' => 'Pincode is required',
            'cm_pincode.min' => 'Pincode must be at least :min characters',
            'cm_area.required' => 'Area is required',
            'cm_address.required' => 'Address is required',
        ]);
    }

    public function update_customer_pin(Request $request)
    {
        try {
            $request->validate([
                'epin_cm_id' => 'required',
                'cm_login_pin' => 'required',
            ]);

            $customer = CustomerMaster::find($request->epin_cm_id);
            $customer->cm_login_pin = $request->cm_login_pin;
            $customer->save();

            return response()->json(['message' => 'Pin Updated Successfully', 'status' => 'success']);
        } catch (Exception $e) {
            Log::error('Error updating edit ss pin: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while updating the ss edit pin.']);
        }
    }

    public function updateTableRefresh($type)
    {
        try {
            switch ($type) {
                case 'ss':
                    $data = $this->getCustomerData(2);
                    break;
                case 'dist':
                    $data = $this->getCustomerData(3);
                    break;
                case 'dealer':
                    $data = $this->getCustomerData(4);
                    break;
                case 'retailer':
                    $data = $this->getCustomerData(5);
                    break;
                default:
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Invalid type specified',
                    ], 400);
            }

            return response()->json([
                'status' => 'success',
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getFilterSsList(Request $request)
    {
        try {
            $ss = $request->searchSs;
            $state = $request->searchState;
            $district = $request->searchDistrict;
            $town = $request->searchTown;
            $marketType = $request->searchMarType;
            $status = $request->searchstatus;
            $employee = $request->searchEmployee;

            $query = CustomerMaster::with('cust_town_relation.district.state', 'market_type_master')
                ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
                ->where('cm_type', 2);

            if ($ss) {
                $query = $query->where('cm_code', $ss);
            }

            if ($marketType) {
                $query = $query->where('cm_market_type', $marketType);
            }

            if ($state) {
                $query->whereHas('cust_town_relation.district', function ($q) use ($state) {
                    $q->where('state_id', $state);
                });
            }

            if ($district) {
                $query->whereHas('cust_town_relation', function ($q) use ($district) {
                    $q->where('district_id', $district);
                });
            }

            if ($town) {
                $query = $query->where('cm_town_id', $town);
            }

            if ($status) {
                if ($status == 'active') {
                    $query = $query->where('cm_status', 1);
                } elseif ($status == 'deactive') {
                    $query = $query->where('cm_status', 0);
                } else {
                    return;
                }
            }

            if ($employee) {
                $query->whereHas('cust_emp_relation', function ($q) use ($employee) {
                    $q->where('emp_code', $employee);
                });
            }

            $ssFilterList = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $ssFilterList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getFilterDistributorList(Request $request)
    {
        try {
            $distributor = $request->searchDistributor;
            $ss = $request->searchSs;
            $state = $request->searchState;
            $district = $request->searchDistrict;
            $town = $request->searchTown;
            $marketType = $request->searchMarType;
            $status = $request->searchstatus;
            $employee = $request->searchEmployee;

            $query = CustomerMaster::with('cust_town_relation.district.state', 'market_type_master')
                ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
                ->where('cm_type', 3);

            if ($distributor) {
                $query = $query->where('cm_code', $distributor);
            }

            if ($ss) {
                $query = $query->where('cm_relation_code', $ss);
            }

            if ($marketType) {
                $query = $query->where('cm_market_type', $marketType);
            }

            if ($state) {
                $query->whereHas('cust_town_relation.district', function ($q) use ($state) {
                    $q->where('state_id', $state);
                });
            }

            if ($district) {
                $query->whereHas('cust_town_relation', function ($q) use ($district) {
                    $q->where('district_id', $district);
                });
            }

            if ($town) {
                $query = $query->where('cm_town_id', $town);
            }

            if ($status) {
                if ($status == 'active') {
                    $query = $query->where('cm_status', 1);
                } elseif ($status == 'deactive') {
                    $query = $query->where('cm_status', 0);
                } else {
                    return;
                }
            }

            if ($employee) {
                $query->whereHas('cust_emp_relation', function ($q) use ($employee) {
                    $q->where('emp_code', $employee);
                });
            }

            $distFilterList = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $distFilterList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getFilterDealerList(Request $request)
    {
        try {
            $dealer = $request->searchDealer;
            $ss = $request->searchSs;
            $state = $request->searchState;
            $district = $request->searchDistrict;
            $town = $request->searchTown;
            $marketType = $request->searchMarType;
            $status = $request->searchstatus;
            $employee = $request->searchEmployee;

            $query = CustomerMaster::with('cust_town_relation.district.state', 'market_type_master')
                ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
                ->where('cm_type', 4);

            if ($dealer) {
                $query = $query->where('cm_code', $dealer);
            }

            if ($ss) {
                $query = $query->where('cm_relation_code', $ss);
            }

            if ($marketType) {
                $query = $query->where('cm_market_type', $marketType);
            }

            if ($state) {
                $query->whereHas('cust_town_relation.district', function ($q) use ($state) {
                    $q->where('state_id', $state);
                });
            }

            if ($district) {
                $query->whereHas('cust_town_relation', function ($q) use ($district) {
                    $q->where('district_id', $district);
                });
            }

            if ($town) {
                $query = $query->where('cm_town_id', $town);
            }

            if ($status) {
                if ($status == 'active') {
                    $query = $query->where('cm_status', 1);
                } elseif ($status == 'deactive') {
                    $query = $query->where('cm_status', 0);
                } else {
                    return;
                }
            }

            if ($employee) {
                $query->whereHas('cust_emp_relation', function ($q) use ($employee) {
                    $q->where('emp_code', $employee);
                });
            }

            $dealerFilterList = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $dealerFilterList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function applyFilters($query, $request)
    {
        $retailer = $request->searchRetailer;
        $ss = $request->searchSs;
        $beat = $request->searchBeat;
        $state = $request->searchState;
        $district = $request->searchDistrict;
        $town = $request->searchTown;
        $marketType = $request->searchMarType;
        $status = $request->searchstatus;

        if ($retailer) {
            $query = $query->where('cm_code', $retailer);
        }

        if ($ss) {
            $query = $query->where('cm_relation_code', $ss);
        }

        if ($marketType) {
            $query = $query->where('cm_market_type', $marketType);
        }

        if ($beat) {
            $query->whereHas('cm_beat_relation', function ($q) use ($beat) {
                $q->where('beat_code', $beat);
            });
        }

        if ($state) {
            $query->whereHas('cust_town_relation.district', function ($q) use ($state) {
                $q->where('state_id', $state);
            });
        }

        if ($district) {
            $query->whereHas('cust_town_relation', function ($q) use ($district) {
                $q->where('district_id', $district);
            });
        }

        if ($town) {
            $query = $query->where('cm_town_id', $town);
        }

        if ($status) {
            if ($status == 'active') {
                $query = $query->where('cm_status', 1);
            } elseif ($status == 'deactive') {
                $query = $query->where('cm_status', 0);
            } else {
                return;
            }
        }

        return $query;
    }

    public function getFilterRetailerList(Request $request)
    {
        try {
            $query = CustomerMaster::with('cust_town_relation.district.state', 'market_type_master', 'cm_beat_relation.beat')
                ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
                ->where('cm_type', 5);

            $query = $this->applyFilters($query, $request);

            $retailerFilterList = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $retailerFilterList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function getCustomerAjaxData(Request $request, int $cmType)
    {
        $query = CustomerMaster::with('cust_town_relation', 'market_type_master')
            ->select('cm_code', 'cm_login_pin', 'cm_name', 'cm_mobile', 'cm_email', 'cm_address', 'cm_pincode', 'cm_gst', 'cm_pan', DB::raw('ST_X(cm_coordinates) as longitude'), DB::raw('ST_Y(cm_coordinates) as latitude'), 'cm_market_type', 'cm_status', 'cm_town_id', 'cm_outstanding_amount', 'cm_contact_person', 'cm_area', 'cm_relation_code', 'cm_type')
            ->where('cm_type', $cmType);

        // Handle search
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('cm_name', 'LIKE', "%$search%")
                    ->orWhere('cm_code', 'LIKE', "%$search%")
                    ->orWhere('cm_mobile', 'LIKE', "%$search%")
                    ->orWhere('cm_email', 'LIKE', "%$search%")
                    ->orWhere('cm_address', 'LIKE', "%$search%")
                    ->orWhereHas('cust_town_relation', function ($q) use ($search) {
                        $q->where('town_name', 'LIKE', "%$search%");
                    })->orWhereHas('market_type_master', function ($q) use ($search) {
                        $q->where('mtm_type', 'LIKE', "%$search%");
                    });
            });
        }

        $query = $this->applyFilters($query, $request);

        // Handle sorting
        if ($request->has('sort')) {
            $sorts = json_decode($request->input('sort'), true);
            foreach ($sorts as $sort) {
                $query->orderBy($sort['selector'], $sort['desc'] ? 'desc' : 'asc');
            }
        } else {
            $query->orderBy('cm_code', 'desc');
        }

        // Check if this is an export request
        if ($request->input('export')) {
            $stateData = $query->get();
            return response()->json([
                'data' => $stateData,
                'total' => $stateData->count(),
                'summary' => null // Add summary data if needed
            ]);
        }

        $pageSize = $request->input('pageSize', 10);
        $page = $request->input('page', 1);

        $customerData = $query->paginate($pageSize, ['*'], 'page', $page);

        return response()->json([
            'data' => $customerData->items(),
            'total' => $customerData->total(),
            'summary' => null // Add summary data if needed
        ]);
    }

    public function getDealerAjaxData(Request $request)
    {
        return $this->getCustomerAjaxData($request, 4); // 4 is the type of dealer
    }


    public function getRetailerAjaxData(Request $request)
    {
        return $this->getCustomerAjaxData($request, 5); // 5 is the type of retailer
    }

    public function searchRetailerByName(Request $request)
    {
        try {
            $term = $request->input('term');

            // Query to search retailers by name
            $retailers = CustomerMaster::where('cm_type', 5)
                ->where('cm_name', 'like', '%' . $term . '%')
                ->orWhere('cm_code', 'like', '%' . $term . '%')->get();

            return response()->json($retailers);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteRetailerData($cm_code)
    {
        // dd($cm_code);
        try {
            $callData = CallMaster::where('client_code', $cm_code)->pluck('call_code')->toArray();
            // dd($callData);

            foreach ($callData as $call_code) {
                // Step 2.1: Retrieve all order_codes related to the current call_code
                $orderData = OrderMaster::where('call_code', $call_code)->pluck('order_code')->toArray();

                $callLog = CallLog::where('call_code', $call_code)->pluck('call_code')->toArray();

                if (!empty($callLog)) {
                    CallLog::whereIn('call_code', $callLog)->delete();
                }

                // Step 2.2: Delete the orders related to this call_code (if there are any)
                if (!empty($orderData)) {
                    OrderMaster::whereIn('order_code', $orderData)->delete();
                }

                // Step 2.3: Delete the call record associated with the current call_code
                CallMaster::where('call_code', $call_code)->delete();
                ClaimComplainMaster::where('cm_code', $cm_code)->delete();
                PaymentCollection::where('cm_code', $cm_code)->delete();
            }
            // $orderData = OrderMaster::where('call_code',$callData)->pluck('order_code')->toArray();
            // dd($orderData);


            CmBeatRelation::where('cm_code', $cm_code)->delete();
            CmCategoryRelation::where('cm_code', $cm_code)->delete();
            CmGradeRelation::where('cm_code', $cm_code)->delete();
            //CmImageRelation::where('cm_code', $cm_code)->delete();
            CmAnniBirthRelation::where('cm_code', $cm_code)->delete();

            //delete image
            $cmImageRelation = CmImageRelation::where('cm_code', $cm_code)->first();

            if ($cmImageRelation) {
                // Step 2: Delete the image files from the server if they exist
                $imageNames = explode(',', $cmImageRelation->image_name);  // Assuming image names are stored as a comma-separated list

                foreach ($imageNames as $imageName) {
                    $imagePath = public_path('/uploads/retailer_images/' . $imageName);

                    // Check if file exists and delete
                    if (File::exists($imagePath)) {
                        File::delete($imagePath);
                    }
                }

                // Step 3: Delete the record from CmImageRelation table
                $cmImageRelation->delete();
            }
            // if (!empty($orderData)) {
            // dd($orderData);
            // OrderMaster::whereIn('order_code', $orderData)->delete();
            // }

            // if (!empty($callData)) {
            // CallMaster::whereIn('call_code', $callData)->delete();
            // }

            $customer = CustomerMaster::where('cm_code', $cm_code)->first();
            if ($customer) {
                $customer->delete();
            }
            // return response()->json(['message' => 'Retailer deleted successfully']);
            return response()->json(['status' => 'success', 'message' => 'Retailer deleted successfully']);
        } catch (QueryException $e) {
            // Check if the exception is a foreign key violation
            if ($e->getCode() == '23000') {
                return response()->json(['status' => 'error', 'message' => 'Cannot delete the retailer due to foreign key constraints.']);
            }
            // Other exceptions can be handled here
            return response()->json(['status' => 'error', 'message' => 'An error occurred.']);
        }
    }

    public function deleteCustomerOrder($cm_code)
    {
        try {
            $callData = CallMaster::where('client_code', $cm_code)->pluck('call_code')->toArray();

            if (empty($callData)) {
                return response()->json(['status' => 'error', 'message' => 'No orders found for the customer.']);
            }

            foreach ($callData as $call_code) {

                $orderData = OrderMaster::where('call_code', $call_code)->pluck('order_code')->toArray();

                $callLog = CallLog::where('call_code', $call_code)->pluck('call_code')->toArray();

                if (!empty($callLog)) {
                    CallLog::whereIn('call_code', $callLog)->delete();
                }

                if (!empty($orderData)) {
                    OrderMaster::whereIn('order_code', $orderData)->delete();
                }

                CallMaster::where('call_code', $call_code)->delete();
            }

            return response()->json(['status' => 'success', 'message' => 'Customer order deleted successfully']);
        } catch (QueryException $e) {
            if ($e->getCode() == '23000') {
                return response()->json(['status' => 'error', 'message' => 'Cannot delete the customer order due to foreign key constraints.']);
            }
            // Other exceptions can be handled here
            Log::error('Error deleting customer order: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'An error occurred.']);
        }
    }
}
