<?php

namespace App\Http\Controllers;

use App\Models\BrandMaster;
use App\Models\MarketTypeMaster;
use App\Models\ProductMaster;
use App\Models\ProductType;
use App\Models\UomMaster;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProductMasterController extends Controller
{
    private function getProductMasterData()
    {
        return ProductMaster::with('brand', 'producttype')
            //->orderBy('product_code', 'desc')
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $product_list = $this->getProductMasterData();
        $brandName = BrandMaster::orderBy('brand_name', 'asc')->get();
        $productType = ProductType::orderBy('pt_name', 'asc')->get();
        $uomName = UomMaster::orderBy('uom_name', 'asc')->get();

        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('products.product.index', compact('product_list', 'brandName', 'productType'))
            . view('modal.product_edit_detail_modal', compact('brandName', 'productType', 'uomName'))
            . view('layouts.footer');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $brandName = BrandMaster::orderBy('brand_name', 'asc')->get();
        $uomName = UomMaster::orderBy('uom_name', 'asc')->get();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('products.product.create', compact('brandName', 'uomName'))
            . view('layouts.footer');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_type' => 'required',
                'product_brand' => 'required',
                // 'product_name' => 'required|unique:product_master,product_name',
                'product_name' => 'required',
                'uom' => 'required',
                'unit_size' => 'required',
                'inner_case_size' => 'required',
                'outer_case_size' => 'required',
                'product_mrp' => 'required',
                'product_gst' => 'required',
                'barcode' => 'nullable|unique:product_master,barcode',
            ], [
                'product_type.required' => 'Product type is required',
                'product_brand.required' => 'Product brand is required',
                'product_name.required' => 'Product name is required',
                'product_name.unique' => 'Product name has already been taken',
                'uom.required' => 'UOM is required',
                'unit_size.required' => 'Unit size is required',
                'unit_size.numeric' => 'Unit size must be a number',
                'inner_case_size.required' => 'Inner case size is required',
                'inner_case_size.numeric' => 'Inner case size must be a number',
                'outer_case_size.required' => 'Outer case size is required',
                'outer_case_size.numeric' => 'Outer case size must be a number',
                'product_mrp.required' => 'Product Mrp is required',
                'product_mrp.numeric' => 'Product Mrp must be a number',
                'product_gst.required' => 'Product GST is required',
                'product_gst.numeric' => 'Product GST must be a number',
                'barcode.unique' => 'Barcode has already been taken',
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 422);
            }

            $product = new ProductMaster();
            $product->product_code = Str::uuid();
            $product->pm_pt_id = $request->product_type;
            $product->product_brand = $request->product_brand;
            $product->product_name = $request->product_name;
            $product->uom = $request->uom;
            $product->unit_size = $request->unit_size;
            $product->inner_case_size = $request->inner_case_size;
            $product->outer_case_size = $request->outer_case_size;
            $product->product_mrp = $request->product_mrp;
            $product->product_gst = $request->product_gst;
            $product->product_hsn_sac_code = $request->product_hsn_sac_code ?? null;
            $product->product_status = 1; //active
            $product->barcode = $request->barcode ?? null;
            $product->weight = $request->weight ?? 0.00;

            //for procuct multiple image upload
            if ($request->hasFile('product_images')) {
                $images = $request->file('product_images');
                $imageNames = [];
                foreach ($images as $image) {
                    $name = uniqid() . '.' . $image->getClientOriginalExtension();
                    $destinationPath = public_path('/uploads/product_images');
                    $image->move($destinationPath, $name);
                    $imageNames[] = $name;
                }
                $product->product_image = implode(',', $imageNames);
            } else {
                $product->product_image = null;
            }

            $product->save();

            session()->flash('success', 'Product Added Successfully');
            return response()->json(['redirect' => route('product.index')]);
        } catch (Exception $e) {
            // Log::error('Error saving product: ' . $e->getMessage());
            return response()->json(['message' => 'Error while adding designation' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $product_code)
    {
        try {
            $product = ProductMaster::find($product_code);
            if (!$product) {
                throw new Exception('Product not found');
            }
            return response()->json([
                'status' => 'success',
                'data' => $product,
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching product: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $product_code)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_code' => 'required',
                'product_type' => 'required',
                'product_brand' => 'required',
                'product_name' => 'required',
                // 'product_name' => 'required|unique:product_master,product_name,' . $product_code . ',product_code',
                'uom' => 'required',
                'unit_size' => 'required|numeric',
                'inner_case_size' => 'required|numeric',
                'outer_case_size' => 'required|numeric',
                'product_mrp' => 'required|numeric',
                'product_gst' => 'required|numeric',
                'barcode' => 'nullable|unique:product_master,barcode,' . $product_code . ',product_code',
            ], [
                'product_code.required' => 'Product id is required',
                'product_type.required' => 'Product type is required',
                'product_brand.required' => 'Product brand is required',
                'product_name.required' => 'Product name is required',
                'product_name.unique' => 'Product name has already been taken',
                'uom.required' => 'UOM is required',
                'unit_size.required' => 'Unit size is required',
                'unit_size.numeric' => 'Unit size must be a number',
                'inner_case_size.required' => 'Inner case size is required',
                'inner_case_size.numeric' => 'Inner case size must be a number',
                'outer_case_size.required' => 'Outer case size is required',
                'outer_case_size.numeric' => 'Outer case size must be a number',
                'product_mrp.required' => 'Product Mrp is required',
                'product_mrp.numeric' => 'Product Mrp must be a number',
                'product_gst.required' => 'Product GST is required',
                'product_gst.numeric' => 'Product GST must be a number',
                'barcode.unique' => 'Barcode has already been taken',
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 'error', 'message' => 'Product Validation failed', 'errors' => $validator->errors()], 422);
            }

            $product = ProductMaster::findOrFail($product_code);
            if (!$product) {
                throw new Exception('Product not found');
            }
            $product->pm_pt_id = $request->product_type;
            $product->product_brand = $request->product_brand;
            $product->product_name = $request->product_name;
            $product->uom = $request->uom;
            $product->unit_size = $request->unit_size;
            $product->inner_case_size = $request->inner_case_size;
            $product->outer_case_size = $request->outer_case_size;
            $product->product_mrp = $request->product_mrp;
            $product->product_gst = $request->product_gst;
            $product->product_hsn_sac_code = $request->product_hsn_sac_code ?? null;
            $product->product_status = $request->product_status;
            $product->barcode = $request->barcode ?? null;
            $product->weight = $request->weight ?? 0.00;

            //for procuct multiple image upload or if already have image or select new image then update and old image will be deleted
            if ($request->hasFile('product_images')) {
                // Delete old images from the folder
                if ($product->product_image) {
                    $oldImages = explode(',', $product->product_image);
                    foreach ($oldImages as $oldImage) {
                        $oldImagePath = public_path('/uploads/product_images/' . $oldImage);
                        if (file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                }

                // Upload new images
                $images = $request->file('product_images');
                $imageNames = [];
                foreach ($images as $image) {
                    $name = uniqid() . '.' . $image->getClientOriginalExtension();
                    $destinationPath = public_path('/uploads/product_images');
                    $image->move($destinationPath, $name);
                    $imageNames[] = $name;
                }
                $product->product_image = implode(',', $imageNames);
            } else {
                $product->product_image = $product->product_image;
            }

            $product->save();

            return response()->json(['status' => 'success', 'message' => 'Product Updated Successfully']);
        } catch (Exception $e) {
            Log::error('Error updating product: ' . $e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the product' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function getFilterProductList(Request $request)
    {
        try {
            $product = $request->product;
            $brand = $request->brand;
            $type = $request->type;
            $status = $request->status;

            $query = ProductMaster::with('brand', 'producttype');

            if ($brand) {
                $query = $query->where('product_brand', $brand);
            }

            if ($product) {
                $query = $query->where('product_code', $product);
            }

            if ($type) {
                $query = $query->where('pm_pt_id', $type);
            }

            if ($status) {
                if ($status == 'active') {
                    $query = $query->where('product_status', 1);
                } elseif ($status == 'deactive') {
                    $query = $query->where('product_status', 0);
                } else {
                    return;
                }
            }

            $productList = $query->get();

            return response()->json([
                'status' => 'success',
                'data' => $productList,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function productListUpdateTableRefresh()
    {
        try {
            $product_list = $this->getProductMasterData();

            return response()->json([
                'status' => 'success',
                'data' => $product_list,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getProductAjaxData(Request $request)
    {
        $query = ProductMaster::with('brand', 'producttype')->orderBy('created_at', 'asc');

        // Handle search
        if ($request->has('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('barcode', 'LIKE', "%$searchTerm%")
                    ->orWhere('product_name', 'LIKE', "%$searchTerm%")
                    ->orWhereHas('brand', function ($q) use ($searchTerm) {
                        $q->where('brand_name', 'LIKE', "%$searchTerm%");
                    })
                    ->orWhereHas('producttype', function ($q) use ($searchTerm) {
                        $q->where('pt_name', 'LIKE', "%$searchTerm%");
                    });
            });
        }

        $pageSize = $request->input('pageSize', 10);
        $productData = $query->paginate($pageSize);

        return response()->json($productData);
    }

    public function getTypeByBrandId(Request $request)
    {
        $brand_id = $request->input('brand_id');
        $types = ProductType::where('brand_id', $brand_id)->pluck('pt_name', 'pt_id');
        return response()->json([
            'data' => $types,
        ]);
    }
}
