<div class="modal fade" id="product_edit_detail_modal" role="dialog" aria-labelledby="EmployeeviewDetailsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="container">
                    <div class="row align-items-center" style="margin:0px 0px -6px -15px;">
                        <ul class="nav nav-tabs nav-tabs-line" role="tablist">
                            <li class="nav-item" role="presentation"><a class="nav-link active" data-toggle="tab"
                                    aria-controls="" role="tab">Edit Product</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="tab-content pt-5">
                    <div class="tab-pane active" id="employeeDetail" role="tabpanel">
                        <div class="container">
                            <form id="edit_product_form" class="add_form">
                                @csrf
                                <div class="col-md-12">
                                    <div class="col-sm-12 col-md-6">
                                        <div class="form-group row">
                                            <input type="text" name="product_code" id="product_code" readonly hidden>
                                            <label class="col-sm-4 col-md-4 col-form-label">Product Brand <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <select class="select2_product_brand form-control" name="product_brand"
                                                    id="product_brand">
                                                    <option></option>
                                                    @foreach ($brandName as $brand)
                                                        <option value="{{ $brand->brand_id }}">
                                                            {{ $brand->brand_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product Name <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="product_name"
                                                    id="product_name" placeholder="Enter Product Name">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Unit Size <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="unit_size"
                                                    id="unit_size" placeholder="Enter Unit Size">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Inner Case Size <span
                                                    class="required">*</span>(Bunch)</label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="inner_case_size"
                                                    id="inner_case_size" placeholder="Enter Inner Case Size" value="0">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product MRP <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="product_mrp"
                                                    id="product_mrp" placeholder="Enter Mrp">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product Barcode </label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="barcode"
                                                    id="barcode" placeholder="Enter Barcode">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">HSN/SAC Code </label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="product_hsn_sac_code"
                                                    id="product_hsn_sac_code" minlength="4" maxlength="8" placeholder="Enter Hsn/Sac">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product Type <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <select class="select2_product_type form-control " name="product_type"
                                                    id="pm_pt_id">
                                                    <option></option>
                                                    @foreach ($productType as $proType)
                                                        <option value="{{ $proType->pt_id }}">
                                                            {{ $proType->pt_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Uom <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <select class="select2_product_uom form-control" name="uom"
                                                    id="uom">
                                                    <option></option>
                                                    @foreach ($uomName as $uom)
                                                        <option value="{{ $uom->uom_id }}">
                                                            {{ $uom->uom_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Outer Case Size <span
                                                    class="required">*</span>(Box)</label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="outer_case_size"
                                                    id="outer_case_size" placeholder="Enter Outer Case Size" value="0">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product GST <span
                                                    class="required">*</span></label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="product_gst"
                                                    id="product_gst" placeholder="Enter Gst" value="0">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Weight (Kg)') }} </label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <input type="text" class="form-control " name="weight" id="weight"
                                                    placeholder="{{ __('Product Weight in Kg') }}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Status</label>
                                            <div style="padding:0;" class="col-sm-8 col-md-8">
                                                <select class="form-control" name="product_status"
                                                    id="product_status">
                                                    <option value="1">Active</option>
                                                    <option value="0">Inactive</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-md-4 col-form-label">Product Image </label>
                                            <div style="position:sticky;padding:0;" class="col-sm-8 col-md-8">
                                                <div class="input-group input-group-file product_image_btn"
                                                    data-plugin="inputGroupFile">
                                                    {{-- <input type="text" id="product_image" name="product_image"
                                                        class="form-control" placeholder="Product Image"> --}}
                                                    <span class="input-group-append">
                                                        <span class="btn btn-primary btn-file">
                                                            <i class="icon md-upload" aria-hidden="true"></i>
                                                            <input type="file" id="product_images"
                                                                class="form-control" name="product_images[]"
                                                                accept="image/*" multiple>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group row" style="margin-left: 27px;">
                                            {{-- <label class="col-sm-4 col-md-4 col-form-label">Preview Image</label> --}}
                                            <div class="col-sm-12 col-md-12" id="image_preview">

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="clear-both modal-footer">
                                    <button type="submit" class="btn btn-primary waves-effect btn-sm waves-classic"
                                        onclick="updateProduct()">Submit</button>
                                    <button data-dismiss="modal"
                                        class="btn btn-default waves-effect btn-sm waves-classic">Close</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // product images preview
    $(document).ready(function() {
        $('#product_images').on('change', function() {
            var files = $(this).get(0).files;
            $('#image_preview').empty(); // Clear the preview area

            for (var i = 0; i < files.length; i++) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#image_preview').append('<img src="' + e.target.result +
                        '" width="100" height="100" style="margin: 10px;">');
                }

                reader.readAsDataURL(files[i]);
            }
        });
    });

    $(document).ready(function() {
        $('#edit_product_form').formValidation({
            framework: "bootstrap4",
            button: {
                disabled: 'disabled'
            },
            icon: null,
            fields: {
                product_brand: {
                    validators: {
                        notEmpty: {
                            message: 'Product Brand is required'
                        }
                    }
                },
                product_name: {
                    validators: {
                        notEmpty: {
                            message: 'Product Name is required'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9\s]+$/, // Allow letters, numbers, and spaces
                            message: 'Only letters, numbers, and spaces allowed'
                        }
                    }
                },
                unit_size: {
                    validators: {
                        notEmpty: {
                            message: 'Unit Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                inner_case_size: {
                    validators: {
                        notEmpty: {
                            message: 'Inner Case Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_mrp: {
                    validators: {
                        notEmpty: {
                            message: 'Product MRP is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_type: {
                    validators: {
                        notEmpty: {
                            message: 'Product Type is required'
                        }
                    }
                },
                uom: {
                    validators: {
                        notEmpty: {
                            message: 'Uom is required'
                        }
                    }
                },
                outer_case_size: {
                    validators: {
                        notEmpty: {
                            message: 'Outer Case Size is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
                product_gst: {
                    validators: {
                        notEmpty: {
                            message: 'Product GST is required'
                        },
                        regexp: {
                            regexp: /^\d+(\.\d+)?$/,
                            message: 'Only number allowed'
                        }
                    }
                },
            },
            err: {
                clazz: 'invalid-feedback'
            },
            control: {
                // The CSS class for valid control
                valid: 'is-valid',
                // The CSS class for invalid control
                invalid: 'is-invalid'
            },
            row: {
                invalid: 'has-danger'
            }
        });
        $('#edit_product_form').on('keypress', function(e) {
            if (e.which == 13) {
                e.preventDefault();
                updateProduct();
            }
        });
    });
</script>
<script>
    $(".select2_product_brand").select2({
        // maximumSelectionLength: 1,
        placeholder: "Select Product Brand",
        allowClear: true
    });
    $(".select2_product_type").select2({
        // maximumSelectionLength: 1,
        placeholder: "Select Product Type",
        allowClear: true
    });
    $(".select2_product_uom").select2({
        // maximumSelectionLength: 1,
        placeholder: "Select Uom",
        allowClear: true
    });
</script>
