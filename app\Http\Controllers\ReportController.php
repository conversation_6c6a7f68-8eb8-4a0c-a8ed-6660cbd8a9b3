<?php

namespace App\Http\Controllers;

use App\Exports\AttendanceSummaryExport;
use App\Exports\BookingVsFulfillmentExport;
use App\Exports\DBRReportExport;
use App\Exports\DSRReportExport;
use App\Exports\EmployeeMapExport;
use App\Exports\NewPartyReport;
use App\Exports\NonVisitPartyReportExport;
use App\Exports\ProductWiseSalesReportExport;
use App\Exports\TargetVsAchievementReportExport;
use App\Models\BeatDatewiseMaster;
use App\Models\BeatMaster;
use App\Models\CmBeatRelation;
use App\Models\CustomerEmpRelation;
use App\Models\CustomerMaster;
use App\Models\OrderMaster;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Contracts\View\View;
use App\Models\Attendance;
use App\Models\BrandMaster;
use App\Models\CallMaster;
use App\Models\EmployeeMaster;
use App\Models\MarketTypeMaster;
use App\Models\ProductMaster;
use App\Models\StateMaster;
use App\Models\TownMaster;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use ExcelReport;
use PdfReport;

class ReportController extends Controller
{
    public function reports()
    {
        $emp_list = EmployeeMaster::orderBy('emp_name', 'asc')->get();
        $brandData = BrandMaster::all();
        $stateData = StateMaster::orderBy('state_name', 'asc')->get();
        $townData = TownMaster::all();
        $marketTypeData = MarketTypeMaster::all();
        return view('layouts.stylesheets')
            . view('layouts.header')
            . view('layouts.sidebar')
            . view('layouts.scripts')
            . view('reports.reports', compact('emp_list', 'brandData', 'stateData', 'townData', 'marketTypeData'))
            . view('layouts.footer');
    }

    public function fetchReport(Request $request)
    {
        // dd($request->all());

        if ($request->date) {
            $dates = explode(',', $request->date);
            $start_date = $dates[0];
            $end_date = isset($dates[1]) ? $dates[1] : null;
        } else {
            $month = $request->month;
            $year = $request->year;

            // Set start date to the first day of the selected month
            $start_date = $year . '-' . $month . '-01';

            // Set end date to the last day of the selected month
            $end_date = date('Y-m-t', strtotime($start_date));
        }
        $report = $request->report;

        $employee = $request->search_employee;
        $reportType = $request->r_type;
        $state = $request->search_state;
        $town = $request->search_town;
        $marketType = $request->market_type;
        $custReportType = $request->cust_report_type;
        $productCategory = $request->product_category;
        $employeeStatus = $request->emp_status;
        $primSecond = $request->prima_secon;
        $downloadType = $request->d_type; //pdf,excel,json

        $marketTypeName = 'All';
        $stateName = 'All State';
        $townName = 'All Town';
        $data = [];
        $title = '';
        $query = '';
        $columns = '';
        if ($report == 'dbr') {
            $query = DB::table("call_master")
                ->select(
                    "call_master.call_code",
                    "call_master.created_at as date",
                    "call_master.grand_total as total",
                    "employee_master.emp_code as empCode",
                    "employee_master.emp_name as empName",
                    "employee_master.emp_status as empStatus",
                    "designation_master.dm_name as designation",
                    "hq_master.hq_name as hq",
                    "customer_category_master.ccm_name as shopCategory",
                    "buyer.cm_name as retailerName",
                    "buyer.cm_area as area",
                    "seller.cm_name as distributor",
                    "beat_master.beat_name as beatName",
                    "town_master.town_name as townName",
                    "market_type_master.mtm_type as marketType",
                    DB::raw('COALESCE(nonproductive_reason.npr_reason, "N/A") as npReason'),
                    DB::raw('COUNT(call_master.call_code) OVER() as total_calls')
                )
                ->leftJoin('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->leftJoin('designation_master', 'employee_master.designation_id', '=', 'designation_master.dm_id')
                ->leftJoin('emp_hq_relation', 'employee_master.emp_code', '=', 'emp_hq_relation.emp_code')
                ->leftJoin('hq_master', 'emp_hq_relation.hq_id', '=', 'hq_master.hq_id')
                ->leftJoin("nonproductive_reason", "nonproductive_reason.npr_id", "=", "call_master.reason_id")
                ->leftJoin("customer_master as buyer", "call_master.client_code", "=", "buyer.cm_code")
                ->leftJoin("customer_master as seller", "call_master.party_code", "=", "seller.cm_code")
                ->leftJoin('cm_beat_relation', "buyer.cm_code", "=", "cm_beat_relation.cm_code")
                ->leftJoin('beat_master', "cm_beat_relation.beat_code", "=", "beat_master.beat_code")
                ->leftJoin("town_master", "town_master.town_id", "=", "buyer.cm_town_id")
                ->leftJoin('market_type_master', 'buyer.cm_market_type', '=', 'market_type_master.mtm_id')
                ->leftJoin("cm_category_relation", "buyer.cm_code", "=", "cm_category_relation.cm_code")
                ->leftJoin("customer_category_master", "cm_category_relation.category_id", "=", "customer_category_master.ccm_id")
                ->leftJoin('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                ->leftJoin('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                ->leftJoin('brand_master', 'product_master.product_brand', '=', 'brand_master.brand_id')
                //->where("buyer.cm_type", 5)
                ->whereBetween('call_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->orderBy('call_master.created_at', 'asc');

            // Apply filters
            if ($employee != 'all') {
                $query->where("call_master.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            if ($primSecond == 'primary') {
                $query->whereIn("buyer.cm_type", [1, 2, 3]);
            } elseif ($primSecond == 'secondary') {
                $query->whereIn("buyer.cm_type", [4, 5]);
            } else {
                $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);
            }

            $brands = DB::table('brand_master')->pluck('brand_name')->toArray();
            $products = DB::table('product_master')->pluck('product_name')->toArray();

            // Build dynamic columns based on report type
            if ($reportType == 'brand_wise') {
                $rpName = 'Brand Wise';
                $baseSelect = 'call_master.call_code,call_master.created_at as date,call_master.grand_total as total,employee_master.emp_code,employee_master.emp_name,employee_master.emp_status,designation_master.dm_name,buyer.cm_name,buyer.cm_area,seller.cm_name,hq_master.hq_name,beat_master.beat_name,town_master.town_name,market_type_master.mtm_type,COALESCE(nonproductive_reason.npr_reason, "N/A") as npReason';
                $query->selectRaw($baseSelect);

                foreach ($brands as $brand) {
                    $query->addSelect(DB::raw("(SUM(CASE WHEN brand_master.brand_name = '$brand' THEN order_master.quantity ELSE 0 END)) AS `$brand`"));
                }

                $groupByColumns = [
                    'call_master.call_code',
                    'call_master.created_at',
                    'call_master.grand_total',
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'employee_master.emp_status',
                    'designation_master.dm_name',
                    'buyer.cm_name',
                    'buyer.cm_area',
                    'seller.cm_name',
                    'hq_master.hq_name',
                    'beat_master.beat_name',
                    'town_master.town_name',
                    'market_type_master.mtm_type',
                    'nonproductive_reason.npr_reason',
                    'customer_category_master.ccm_name'
                ];
                $query->groupBy($groupByColumns);
            } elseif ($reportType == 'product_wise') {
                $rpName = 'Product Wise';
                $baseSelect = 'call_master.call_code,call_master.created_at as date,call_master.grand_total as total,employee_master.emp_code,employee_master.emp_name,employee_master.emp_status,designation_master.dm_name,buyer.cm_name,buyer.cm_area,seller.cm_name,hq_master.hq_name,beat_master.beat_name,town_master.town_name,market_type_master.mtm_type,COALESCE(nonproductive_reason.npr_reason, "N/A") as npReason';
                $query->selectRaw($baseSelect);

                foreach ($products as $product) {
                    $query->addSelect(DB::raw("(SUM(CASE WHEN product_master.product_name = '$product' THEN order_master.quantity ELSE 0 END)) AS `$product`"));
                }

                $groupByColumns = [
                    'call_master.call_code',
                    'call_master.created_at',
                    'call_master.grand_total',
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'employee_master.emp_status',
                    'designation_master.dm_name',
                    'buyer.cm_name',
                    'buyer.cm_area',
                    'seller.cm_name',
                    'hq_master.hq_name',
                    'beat_master.beat_name',
                    'town_master.town_name',
                    'market_type_master.mtm_type',
                    'nonproductive_reason.npr_reason',
                    'customer_category_master.ccm_name'
                ];
                $query->groupBy($groupByColumns);
            }

            $salesType = ($primSecond == 'primary') ? 'Primary' : (($primSecond == 'secondary') ? 'Secondary' : 'Primary & Secondary');

            // Generate report title
            $title = 'DBR Report - ' . $rpName . ' - ' . $employee . ' - ' . $salesType . ' (' .
                date('d-m-Y', strtotime($start_date)) . ' to ' .
                date('d-m-Y', strtotime($end_date)) . ')';

            // Define columns for export
            $columns = [
                'RETAILER NAME' => 'retailerName',
                'DISTRIBUTOR NAME' => 'distributor',
                'RETAILER AREA' => 'area',

                'RETAILER TYPE' => 'shopCategory',
                'BEAT' => 'beatName',
                'TOWN/CITY' => 'townName',
                'NP REASON' => 'npReason',

            ];

            if ($reportType == 'brand_wise') {
                foreach ($brands as $brand) {
                    $columns[$brand] = $brand;
                }
            } elseif ($reportType == 'product_wise') {
                foreach ($products as $product) {
                    $columns[$product] = $product;
                }
            }
            $columns['Total'] = 'total';
            $columns['DATE'] = function ($data) {
                return date('d-m-Y', strtotime($data->date));
            };
            return Excel::download(
                new DBRReportExport(
                    $query,
                    $columns,
                    $title,
                    $reportType,
                    $request->start_date,
                    $request->end_date
                ),
                $title . '.xlsx'
            );
            // return Excel::download(
            //     new DBRReportExport($query, $columns, $title, $reportType),
            //     'DBR_Report_' . date('Y-m-d_His') . '.xlsx'
            // );
        } else if ($report == 'dsr') {
            if ($downloadType == 'excel') {
                if ($reportType == 'brand_wise') {
                    // First, get the call counts per employee
                    $queryAll = DB::table('call_master')
                        ->select(
                            'call_master.emp_code',
                            'employee_master.emp_name',
                            'employee_master.emp_mobile',
                            'employee_master.emp_status',
                            'designation_master.dm_name',
                            'market_type_master.mtm_type',
                            DB::raw('COUNT(DISTINCT call_master.call_code) as total_calls'),
                            DB::raw('COUNT(DISTINCT CASE WHEN EXISTS (
                            SELECT 1 FROM order_master 
                            WHERE order_master.call_code = call_master.call_code 
                            AND call_master.product_order_type = 1
                        ) THEN call_master.call_code END) as productive_calls'),
                            DB::raw('GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ", ") as stateName'),
                            DB::raw('COALESCE(SUM(call_master.grand_total), 0) as grand_total')
                        )
                        ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                        ->leftJoin('designation_master', 'employee_master.designation_id', '=', 'designation_master.dm_id')
                        ->leftJoin('market_type_master', 'market_type_master.mtm_id', '=', 'employee_master.market_type')
                        ->leftJoin('emp_state_relation', 'employee_master.emp_code', '=', 'emp_state_relation.emp_code')
                        ->leftJoin('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->groupBy(
                            'call_master.emp_code',
                            'employee_master.emp_name',
                            'employee_master.emp_mobile',
                            'designation_master.dm_name',
                            'market_type_master.mtm_type',
                            'employee_master.emp_status'
                        );

                    if ($employee) {
                        $queryAll->where("call_master.emp_code", $employee);
                    }

                    $callCounts = $queryAll->get();

                    // Then, get the brand-wise sales
                    $brandSales = DB::table('call_master')
                        ->select(
                            'call_master.emp_code',
                            'brand_master.brand_name',
                            DB::raw('SUM(order_master.quantity) as total_quantity_sold')
                        )
                        ->join('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                        ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                        ->join('brand_master', 'product_master.product_brand', '=', 'brand_master.brand_id')
                        ->where('call_master.product_order_type', 1)
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->groupBy('call_master.emp_code', 'brand_master.brand_name')
                        ->get();

                    // Get all unique brands
                    $allBrands = DB::table('brand_master')
                        ->select('brand_name')
                        ->whereIn('brand_id', function ($query) use ($start_date, $end_date) {
                            $query->select('product_master.product_brand')
                                ->distinct()
                                ->from('product_master')
                                ->join('order_master', 'product_master.product_code', '=', 'order_master.product_code')
                                ->join('call_master', 'order_master.call_code', '=', 'call_master.call_code')
                                ->where('call_master.product_order_type', 1);
                        })
                        ->pluck('brand_name')
                        ->toArray();

                    // Prepare employee data
                    $employeeData = [];

                    // First, initialize with call counts
                    foreach ($callCounts as $count) {
                        $employeeData[$count->emp_code] = [
                            'emp_code' => $count->emp_code,
                            'emp_name' => $count->emp_name,
                            'emp_status' => $count->emp_status == 1 ? 'Active' : 'Inactive',
                            'stateName' => $count->stateName,
                            'dm_name' => $count->dm_name,
                            'mtm_type' => $count->mtm_type,
                            'emp_mobile' => $count->emp_mobile,
                            'total_calls' => strval($count->total_calls),
                            'productive_calls' => strval($count->productive_calls),
                            'brand_sales' => array_fill_keys($allBrands, "0"),
                            'grand_total' => $count->grand_total,
                        ];
                    }

                    // Then, add brand sales
                    foreach ($brandSales as $sale) {
                        if (isset($employeeData[$sale->emp_code]) && $sale->brand_name) {
                            $employeeData[$sale->emp_code]['brand_sales'][$sale->brand_name] = $sale->total_quantity_sold;
                        }
                    }

                    // Prepare final matrix
                    $finalMatrix = [];
                    // First row is headers
                    $finalMatrix[] = array_merge(
                        ['EMPLOYEE CODE', 'EMPLOYEE NAME',  'STATUS', 'STATE',  'DESIGNATION', 'MARKET TYPE', 'MOBILE', 'TC', 'PC'],
                        $allBrands,
                        ['GRAND TOTAL']
                    );

                    // Add data rows
                    foreach ($employeeData as $empData) {
                        $row = [
                            $empData['emp_code'],
                            $empData['emp_name'],
                            $empData['emp_status'],
                            $empData['stateName'],
                            $empData['dm_name'],
                            $empData['mtm_type'],
                            $empData['emp_mobile'],
                            $empData['total_calls'],
                            $empData['productive_calls']
                        ];

                        // Add brand sales
                        foreach ($allBrands as $brand) {
                            $row[] = $empData['brand_sales'][$brand];
                        }

                        // grand total last column
                        $row[] = $empData['grand_total'];

                        $finalMatrix[] = $row;
                    }
                } else if ($reportType == 'product_wise') {
                    // First, get the call counts per employee
                    $queryAll = DB::table('call_master')
                        ->select(
                            'call_master.emp_code',
                            'employee_master.emp_name',
                            'employee_master.emp_mobile',
                            'employee_master.emp_status',
                            'designation_master.dm_name',
                            'market_type_master.mtm_type',
                            DB::raw('COUNT(DISTINCT call_master.call_code) as total_calls'),
                            DB::raw('COUNT(DISTINCT CASE WHEN EXISTS (
                            SELECT 1 FROM order_master 
                            WHERE order_master.call_code = call_master.call_code 
                            AND call_master.product_order_type = 1
                        ) THEN call_master.call_code END) as productive_calls'),
                            DB::raw('GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ", ") as stateName'),
                            DB::raw('COALESCE(SUM(call_master.grand_total), 0) as grand_total')
                        )
                        ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                        ->leftJoin('designation_master', 'employee_master.designation_id', '=', 'designation_master.dm_id')
                        ->leftJoin('market_type_master', 'market_type_master.mtm_id', '=', 'employee_master.market_type')
                        ->leftJoin('emp_state_relation', 'employee_master.emp_code', '=', 'emp_state_relation.emp_code')
                        ->leftJoin('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->groupBy(
                            'call_master.emp_code',
                            'employee_master.emp_name',
                            'employee_master.emp_mobile',
                            'designation_master.dm_name',
                            'market_type_master.mtm_type',
                            'employee_master.emp_status'
                        );

                    if ($employee) {
                        $queryAll->where("call_master.emp_code", $employee);
                    }

                    $callCounts = $queryAll->get();

                    // Then, get the product-wise sales
                    $productSales = DB::table('call_master')
                        ->select(
                            'call_master.emp_code',
                            'product_master.product_name',
                            DB::raw('SUM(order_master.quantity) as total_quantity_sold')
                        )
                        ->join('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                        ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                        ->where('call_master.product_order_type', 1)
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->groupBy(
                            'call_master.emp_code',
                            'product_master.product_name'
                        )
                        ->get();

                    // Get all unique products
                    $allProducts = DB::table('product_master')
                        ->select('product_name')
                        ->whereIn('product_code', function ($query) use ($start_date, $end_date) {
                            $query->select('product_master.product_code')
                                ->distinct()
                                ->from('product_master')
                                ->join('order_master', 'product_master.product_code', '=', 'order_master.product_code')
                                ->join('call_master', 'order_master.call_code', '=', 'call_master.call_code')
                                ->where('call_master.product_order_type', 1);
                        })
                        ->pluck('product_name')
                        ->toArray();
                    // dd(array_fill_keys($allProducts, "0"));
                    // Prepare employee data
                    $employeeData = [];
                    // dd($callCounts);
                    // First, initialize with call counts
                    foreach ($callCounts as $count) {
                        $employeeData[$count->emp_code] = [
                            'emp_code' => $count->emp_code,
                            'emp_name' => $count->emp_name,
                            'emp_status' => $count->emp_status == 1 ? 'Active' : 'Inactive',
                            'stateName' => $count->stateName,
                            'dm_name' => $count->dm_name,
                            'mtm_type' => $count->mtm_type,
                            'emp_mobile' => $count->emp_mobile,
                            'total_calls' => strval($count->total_calls),
                            'productive_calls' => strval($count->productive_calls),
                            'product_sales' => array_fill_keys($allProducts, "0"),
                            'grand_total' => $count->grand_total,
                        ];
                    }

                    // Then, add product sales
                    foreach ($productSales as $sale) {
                        if (isset($employeeData[$sale->emp_code]) && $sale->product_name) {
                            $employeeData[$sale->emp_code]['product_sales'][$sale->product_name] = $sale->total_quantity_sold;
                        }
                    }

                    // Prepare final matrix
                    $finalMatrix = [];
                    // First row is headers
                    $finalMatrix[] = array_merge(
                        ['EMPLOYEE CODE', 'EMPLOYEE NAME',  'STATUS', 'STATE',  'DESIGNATION', 'MARKET TYPE', 'MOBILE', 'TC', 'PC'],
                        $allProducts,
                        ['GRAND TOTAL']
                    );

                    // Add data rows
                    foreach ($employeeData as $empData) {
                        $row = [
                            $empData['emp_code'],
                            $empData['emp_name'],
                            $empData['emp_status'],
                            $empData['stateName'],
                            $empData['dm_name'],
                            $empData['mtm_type'],
                            $empData['emp_mobile'],
                            $empData['total_calls'],
                            $empData['productive_calls']
                        ];

                        // Add product sales
                        foreach ($allProducts as $product) {
                            $row[] = $empData['product_sales'][$product];
                        }

                        // grand total last column
                        $row[] = $empData['grand_total'];

                        $finalMatrix[] = $row;
                    }
                    // dd($employeeData);
                }

                $title = 'Daily Sales Report - ' . ($reportType == 'brand_wise' ? 'Brand Wise' : 'Product Wise') . ' - ' . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

                // Generate and download the Excel file
                if ($reportType == 'brand_wise') {
                    return Excel::download(new DSRReportExport($finalMatrix, $reportType, $start_date, $end_date), $title . '.xlsx');
                } else if ($reportType == 'product_wise') {
                    return Excel::download(new DSRReportExport($finalMatrix, $reportType, $start_date, $end_date), $title . '.xlsx');
                }
            } else {
                return 'pdf type is not availble';
            }
        } else if ($report == 'attendance_employee') {
            $query = DB::table('attendance')
                ->select([
                    'employee_master.emp_code as empCode',
                    'employee_master.emp_name as empName',
                    'employee_master.emp_status as empStatus',
                    'attendance.atd_type as type',
                    'attendance.atd_login_time as loginTime',
                    'attendance.atd_logout_time as logoutTime',
                    'joinemp.emp_name as joinEmpCode',
                    'attendance.atd_remarks as remarks',
                    'designation_master.dm_name as designation',
                    'market_type_master.mtm_type as marketType',
                    DB::raw('GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ", ") as stateName'),
                    DB::raw('COUNT(DISTINCT call_master.call_code) as totalCalls'),
                    DB::raw('SUM(CASE WHEN call_master.product_order_type = 1 THEN 1 ELSE 0 END) as productiveCalls'),
                    DB::raw('IFNULL(SUM(call_master.grand_total), 0) as totalAmount'),
                ])
                ->whereBetween('attendance.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->join('employee_master', 'attendance.emp_code', '=', 'employee_master.emp_code')
                ->leftJoin('employee_master as joinemp', 'attendance.join_emp_code', '=', 'joinemp.emp_code')
                ->join('designation_master', 'designation_master.dm_id', '=', 'employee_master.designation_id')
                ->leftJoin('call_master', function ($join) {
                    $join->on('attendance.emp_code', '=', 'call_master.emp_code')
                        ->whereRaw('DATE(call_master.created_at) = DATE(attendance.created_at)');
                })
                ->join('market_type_master', 'market_type_master.mtm_id', '=', 'employee_master.market_type')
                ->join('emp_state_relation', 'employee_master.emp_code', '=', 'emp_state_relation.emp_code')
                ->join('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                ->groupBy(
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'employee_master.emp_status',
                    'attendance.atd_type',
                    'attendance.atd_login_time',
                    'attendance.atd_logout_time',
                    'joinemp.emp_name',
                    'attendance.atd_remarks',
                    'designation_master.dm_name',
                    'market_type_master.mtm_type'
                )
                ->orderBy('attendance.atd_login_time', 'asc');

            if ($employee != 'all') {
                $query->where("attendance.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $title = 'Attendance Report -' . ucfirst($employee) . '(' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'EMPLOYEE CODE' => 'empCode',
                'EMPLOYEE NAME' => 'empName',
                'DESIGNATION' => 'designation',
                'STATE' => 'stateName',
                'MARKET TYPE' => 'marketType',
                'STATUS' => function ($status) {
                    return $status->empStatus == 1 ? 'Active' : 'Inactive';
                },
                'LOG IN DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->loginTime));
                },
                'LOG IN TIME' => function ($data) {
                    return date('H:i:s', strtotime($data->loginTime));
                },
                'LOG OUT DATE' => function ($data) {
                    return $data->logoutTime ? date('d-m-Y', strtotime($data->logoutTime)) : '';
                },
                'LOG OUT TIME' => function ($data) {
                    return $data->logoutTime ? date('H:i:s', strtotime($data->logoutTime)) : '';
                },
                'WORK TYPE' => function ($calltype) {
                    if ($calltype->type == '1') {
                        return 'Solo Call';
                    } elseif ($calltype->type == '2') {
                        return 'Join Call';
                    } elseif ($calltype->type == '3') {
                        return 'Other';
                    } elseif ($calltype->type == '4') {
                        return 'Absent';
                    } else {
                        return null;
                    }
                },
                'JOIN CALL WITH' => 'joinEmpCode',
                'REMARKS' => 'remarks',
                'TC' => 'totalCalls',
                'PC' => 'productiveCalls',
                'TOTAL AMOUNT' => 'totalAmount'
            ];
        } else if ($report == 'attendance_summary') {
            $query = DB::table('attendance')
                ->select([
                    'employee_master.emp_code as empCode',
                    'employee_master.emp_name as empName',
                    DB::raw('GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ", ") as stateName'),
                    DB::raw('COUNT(DISTINCT call_master.call_code) as totalCalls'),
                    DB::raw('(SELECT COUNT(DISTINCT DATE(a.created_at)) 
                 FROM attendance a 
                 WHERE a.emp_code = employee_master.emp_code 
                 AND a.created_at BETWEEN "' . date("Y-m-d 00:00:00", strtotime($start_date)) . '" 
                 AND "' . date("Y-m-d 23:59:59", strtotime($end_date)) . '") as workingDays'),
                    DB::raw('SUM(call_master.grand_total) as secondarySales'),
                    DB::raw('ROUND(COUNT(DISTINCT call_master.call_code) / 
                NULLIF((SELECT COUNT(DISTINCT DATE(a.created_at)) 
                       FROM attendance a 
                       WHERE a.emp_code = employee_master.emp_code 
                       AND a.created_at BETWEEN "' . date("Y-m-d 00:00:00", strtotime($start_date)) . '" 
                       AND "' . date("Y-m-d 23:59:59", strtotime($end_date)) . '"), 0), 2) as dailyCallAverage'),
                    DB::raw('ROUND(SUM(call_master.grand_total) / 
                NULLIF((SELECT COUNT(DISTINCT DATE(a.created_at)) 
                       FROM attendance a 
                       WHERE a.emp_code = employee_master.emp_code 
                       AND a.created_at BETWEEN "' . date("Y-m-d 00:00:00", strtotime($start_date)) . '" 
                       AND "' . date("Y-m-d 23:59:59", strtotime($end_date)) . '"), 0), 2) as dailySaleAverage'),
                ])
                ->whereBetween('attendance.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->join('employee_master', 'attendance.emp_code', '=', 'employee_master.emp_code')
                ->join('designation_master', 'designation_master.dm_id', '=', 'employee_master.designation_id')
                ->join('call_master', function ($join) {
                    $join->on('attendance.emp_code', '=', 'call_master.emp_code')
                        ->whereRaw('DATE(call_master.created_at) = DATE(attendance.created_at)');
                })
                ->join('customer_master as buyer', 'call_master.client_code', '=', 'buyer.cm_code')
                ->whereNot('call_master.call_status', 4)
                ->whereIn('buyer.cm_type', [4, 5])  // Filter for cm_type = 5 for calls and sales
                ->join('emp_state_relation', 'employee_master.emp_code', '=', 'emp_state_relation.emp_code')
                ->join('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                ->groupBy('employee_master.emp_code', 'employee_master.emp_name')
                ->orderBy('employee_master.emp_name', 'asc');

            // Apply filters
            if ($employee != 'all') {
                $query->where("attendance.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            if ($state != 'all') {
                $stateRecord = StateMaster::find($state);
                if ($stateRecord) {
                    $stateName = $stateRecord->state_name;

                    $query->where("state_master.state_id", $state);
                }
            }

            $data = $query->get();

            $emp = ($employee == 'all') ? 'All Employee' : $employee;

            $title = 'Attendance Summary Report - ' .  $emp . ' - ' . $stateName . ' - ' . date('d-m-Y', strtotime($start_date)) . ' - ' . date('d-m-Y', strtotime($end_date));
            return Excel::download(new AttendanceSummaryExport($data, $title), $title . '.xlsx');
        } else if ($report == 'expense_employee') {
            $query = DB::table('expense_master')
                ->join('employee_master', 'expense_master.emp_code', '=', 'employee_master.emp_code')
                ->join('designation_master', 'employee_master.designation_id', '=', 'designation_master.dm_id')
                ->join('expense_type', 'expense_master.em_expense_type', '=', 'expense_type.et_id')
                ->join('market_type_master', 'employee_master.market_type', '=', 'market_type_master.mtm_id')
                ->select('expense_master.*', 'employee_master.emp_code', 'employee_master.emp_status', 'employee_master.emp_name', 'designation_master.dm_name', 'market_type_master.mtm_type', 'expense_type.et_name')
                ->orderBy('expense_master.created_at')
                ->whereBetween('expense_master.em_date', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ]);
            if ($employee != 'all') {
                $query = $query->where('expense_master.emp_code', $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $title = 'Expense Report ' . '(' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->em_date));
                },
                'EMPLOYEE CODE' => 'emp_code',
                'EMPLOYEE NAME' => 'emp_name',
                'EXPENSE TYPE' => 'et_name',
                'EXPENSE AMOUNT' => 'em_amount',
                'APPROVED AMOUNT' => 'em_approved_amount',
                'EXPENSE DETAILS' => 'em_expense_detail',
                'EXPENSE STATUS' => function ($data) {
                    if ($data->em_expense_status == 0) {
                        return 'Pending';
                    } elseif ($data->em_expense_status == 1) {
                        return 'Confirm';
                    } elseif ($data->em_expense_status == 2) {
                        return 'Rejected';
                    } elseif ($data->em_expense_status == 3) {
                        return 'Partial Approved';
                    }
                },
                //for image get url into uploads/expense_images
                'IMAGE' => function ($data) {
                    // return asset('uploads/expense_images/' . $data->em_bill_picture);
                    return $data->em_bill_picture ? asset('uploads/expense_images/' . $data->em_bill_picture) : 'No Image';
                },
            ];
        } else if ($report == 'expense_state') {
            $query = DB::table('expense_master')
                ->select(
                    'employee_master.emp_code as empCode',
                    'employee_master.emp_name as empName',
                    'employee_master.emp_status as empStatus',
                    'expense_master.em_date as date',
                    'expense_type.et_name as expType',
                    'expense_master.em_amount as expAmount',
                    'expense_master.em_approved_amount as expAppAmt',
                    'expense_master.em_expense_detail as expDetail',
                    'expense_master.em_expense_status as expStatus',
                    'expense_master.em_bill_picture as expBillPic',
                    DB::raw("GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ', ') as stateName"),
                    'market_type_master.mtm_type as marketType',
                )
                ->join('employee_master', 'expense_master.emp_code', '=', 'employee_master.emp_code')
                ->join('expense_type', 'expense_master.em_expense_type', '=', 'expense_type.et_id')
                ->join('emp_state_relation', 'emp_state_relation.emp_code', '=', 'employee_master.emp_code')
                ->join('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                ->join('market_type_master', 'market_type_master.mtm_id', '=', 'employee_master.market_type')
                ->groupBy(
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'employee_master.emp_status',
                    'market_type_master.mtm_type',
                    'expense_master.em_date',
                    'expense_type.et_name',
                    'expense_master.em_amount',
                    'expense_master.em_approved_amount',
                    'expense_master.em_expense_detail',
                    'expense_master.em_expense_status',
                    'expense_master.em_bill_picture'
                )
                ->orderBy('expense_master.created_at')
                ->whereBetween('expense_master.em_date', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ]);

            if ($state != 'all') {
                $stateRecord = StateMaster::find($state);
                if ($stateRecord) {
                    $stateName = $stateRecord->state_name;

                    $query->where("state_master.state_id", $state);
                }
            }

            $title = 'Expense State Wise - ' . ucfirst($stateName) . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->date));
                },
                'STATE' => function ($data) {
                    return $data->stateName ?: 'No State';
                },
                'EMPLOYEE CODE' => 'empCode',
                'EMPLOYEE NAME' => 'empName',
                'EXPENSE TYPE' => 'expType',
                'EXPENSE AMOUNT' => 'expAmount',
                'APPROVED AMOUNT' => 'expAppAmt',
                'EXPENSE DETAILS' => 'expDetail',
                'EXPENSE STATUS' => function ($data) {
                    if ($data->expStatus == 0) {
                        return 'Pending';
                    } elseif ($data->expStatus == 1) {
                        return 'Confirm';
                    } elseif ($data->expStatus == 2) {
                        return 'Rejected';
                    } elseif ($data->expStatus == 3) {
                        return 'Partial Approved';
                    }
                },
                'IMAGE' => function ($data) {
                    // return asset('uploads/expense_images/' . $data->em_bill_picture);
                    return $data->expBillPic ? asset('uploads/expense_images/' . $data->expBillPic) : 'No Image';
                },
            ];
        } else if ($report == 'attendance_state') {
            $query = DB::table('attendance')
                ->select(
                    'employee_master.emp_code as empCode',
                    'employee_master.emp_name as empName',
                    'employee_master.emp_status as empStatus',
                    'attendance.created_at as date',
                    'attendance.atd_type as type',
                    'attendance.atd_login_time as loginTime',
                    'attendance.atd_logout_time as logoutTime',
                    'joinemp.emp_name as joinEmpCode',
                    'attendance.atd_remarks as remarks',
                    'designation_master.dm_name as designation',
                    DB::raw("GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ', ') as stateName"),
                    'market_type_master.mtm_type as marketType',
                )
                ->join('employee_master', 'attendance.emp_code', '=', 'employee_master.emp_code')
                ->leftJoin('employee_master as joinemp', 'attendance.join_emp_code', '=', 'joinemp.emp_code')
                ->join('designation_master', 'designation_master.dm_id', '=', 'employee_master.designation_id')
                ->join('emp_state_relation', 'emp_state_relation.emp_code', '=', 'employee_master.emp_code')
                ->join('state_master', 'emp_state_relation.state_id', '=', 'state_master.state_id')
                ->join('market_type_master', 'market_type_master.mtm_id', '=', 'employee_master.market_type')
                ->leftJoin('call_master', 'attendance.emp_code', '=', 'call_master.emp_code')
                ->groupBy('employee_master.emp_code', 'attendance.created_at', 'employee_master.emp_name', 'employee_master.emp_status', 'attendance.atd_type', 'attendance.atd_type', 'attendance.atd_login_time', 'attendance.atd_logout_time', 'joinemp.emp_name', 'attendance.atd_remarks', 'designation_master.dm_name', 'market_type_master.mtm_type')
                ->orderBy('attendance.created_at')
                ->whereBetween('attendance.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ]);

            if ($state != 'all') {
                $stateRecord = StateMaster::find($state);
                if ($stateRecord) {
                    $stateName = $stateRecord->state_name;

                    $query->where("state_master.state_id", $state);
                }
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $title = 'Attendance State Wise -' . ucfirst($stateName) . '(' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'STATE' => 'stateName',
                'EMPLOYEE CODE' => 'empCode',
                'EMPLOYEE NAME' => 'empName',
                'STATUS' => function ($status) {
                    return $status->empStatus == 1 ? 'Active' : 'Inactive';
                },
                'MARKET TYPE' => 'marketType',
                'DESIGNATION' => 'designation',
                'DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->date));
                },
                'LOG IN TIME' => function ($data) {
                    return date('H:i:s', strtotime($data->loginTime));
                },
                'LOG OUT TIME' => function ($data) {
                    if ($data->logoutTime != null)
                        return date('H:i:s', strtotime($data->logoutTime));
                    else {
                        return null;
                    }
                },
                'WORK TYPE' => function ($calltype) {
                    if ($calltype->type == '1') {
                        return 'Solo Call';
                    } elseif ($calltype->type == '2') {
                        return 'Join Call';
                    } elseif ($calltype->type == '3') {
                        return 'Other';
                    } elseif ($calltype->type == '4') {
                        return 'Absent';
                    } else {
                        return null;
                    }
                },
                'JOIN CALL WITH' => 'joinEmpCode',
                'REMARKS' => 'remarks',
                // 'TC' => 'tc',
                // 'PC' => 'productive_call',
                //'TOTAL AMOUNT' => 'total_amount',
            ];
        } else if ($report == 'order_report') {
            $query = DB::table("call_master")
                ->select(
                    'call_master.call_code',
                    'call_master.emp_code',
                    'call_master.invoice_number',
                    'call_master.product_order_type',
                    'call_master.client_code',
                    'call_master.grand_total',
                    'call_master.party_code',
                    'call_master.call_status',
                    'call_master.created_at as date',
                    'call_master.is_telephonic as telephonic',
                    'call_master.call_status as callStatus',
                    'call_master.start_time',
                    'call_master.stop_time',
                    'employee_master.emp_name as empName',
                    'buyer.cm_name as buyerName',
                    'buyer.cm_mobile as buyerMobile',
                    'buyer.cm_area as buyerArea',
                    'seller.cm_name as sellerName',
                    'seller.cm_mobile as sellerMobile',
                    'seller.cm_area as sellerArea',
                    DB::raw('COALESCE(beat_master.beat_name, "N/A") as beatName'),
                    'empMtm.mtm_type as empMarketType',
                    DB::raw('COALESCE(nonproductive_reason.npr_reason, "N/A") as npReason')
                )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('market_type_master as empMtm', 'employee_master.market_type', '=', 'empMtm.mtm_id')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->leftJoin('cm_beat_relation', 'buyer.cm_code', '=', 'cm_beat_relation.cm_code')
                ->leftJoin('beat_master', 'cm_beat_relation.beat_code', '=', 'beat_master.beat_code')
                ->leftJoin('nonproductive_reason', 'nonproductive_reason.npr_id', '=', 'call_master.reason_id')
                ->whereBetween('call_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->orderBy('call_master.created_at', 'asc');

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            if ($marketType) {
                $marketTypeRecord = MarketTypeMaster::find($marketType);
                if ($marketTypeRecord) {
                    $query->where("employee_master.market_type", $marketType);
                } else {
                    return;
                }
            }

            // Apply employee filter if not 'all'
            if ($employee !== 'all') {
                $query->where("call_master.emp_code", $employee);
            }

            switch ($custReportType) {
                case 'retailer':
                    $query->where("buyer.cm_type", 5);
                    break;
                case 'ss':
                    $query->where("buyer.cm_type", 2);
                    break;
                case 'distributor':
                    $query->where("buyer.cm_type", 3);
                    break;
                case 'dealer':
                    $query->where("buyer.cm_type", 4);
                    break;
                default:
                    return;
            }

            $title = 'Order Report - ' . ucfirst($custReportType) . ' - ' . ($marketTypeRecord->mtm_type ?? 'All Markets') . ' - ' . ucfirst($employee) . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->date));
                },
                'START TIME' => 'start_time',
                'STOP TIME' => 'stop_time',
                'EMPLOYEE NAME' => 'empName',
                'MARKET TYPE' => 'empMarketType',
                'BEAT NAME' => 'beatName',
            ];

            // Add customer-specific columns based on the report type
            switch ($custReportType) {
                case 'ss':
                    $columns += [
                        'SS NAME' => 'buyerName',
                        'SS AREA' => 'buyerArea',
                        'SS NUMBER' => 'buyerMobile',
                        'COMPANY NAME' => 'sellerName',
                        'COMPANY NUMBER' => 'sellerMobile',
                    ];
                    break;
                case 'distributor':
                    $columns += [
                        'DISTRIBUTOR NAME' => 'buyerName',
                        'DISTRIBUTOR AREA' => 'buyerArea',
                        'DISTRIBUTOR MOBILE' => 'buyerMobile',
                        'SS NAME' => 'sellerName',
                        'SS MOBILE' => 'sellerMobile',
                    ];
                    break;
                case 'dealer':
                    $columns += [
                        'DEALER NAME' => 'buyerName',
                        'DEALER AREA' => 'buyerArea',
                        'DEALER NUMBER' => 'buyerMobile',
                        'SS NAME' => 'sellerName',
                        'SS NUMBER' => 'sellerMobile',
                    ];
                    break;
                case 'retailer':
                    $columns += [
                        'RETAILER NAME' => 'buyerName',
                        'RETAILER AREA' => 'buyerArea',
                        'RETAILER MOBILE' => 'buyerMobile',
                        'DISTRIBUTOR NAME' => 'sellerName',
                        'DISTRIBUTOR MOBILE' => 'sellerMobile',
                    ];
                    break;
                default:
                    return;
            }

            $columns += [
                'ORDER AMOUNT' => 'grand_total',
                'ORDER TYPE' => function ($oType) {
                    return $oType->product_order_type == '1' ? 'PRODUCTIVE' : ($oType->product_order_type == '0' ? 'NON PRODUCTIVE' : null);
                },
                'NP REASON' => 'npReason',
                'TELEPHONIC' => function ($telType) {
                    return $telType->telephonic == '1' ? 'YES' : ($telType->telephonic == '0' ? 'NO' : null);
                },
                'CALL STATUS' => function ($status) {
                    switch ($status->callStatus) {
                        case '0':
                            return 'PENDING';
                        case '1':
                            return 'CONFIRM';
                        case '2':
                            return 'DISPATCH';
                        case '3':
                            return 'DELIVERED';
                        case '4':
                            return 'CANCEL';
                        case '5':
                            return 'PARTIAL DELIVERED';
                        default:
                            return null;
                    }
                },
                'INVOICE' => function ($oType) {
                    $url = $oType->product_order_type == '1' ? url('/') . '/view_order_invoice/' . $oType->call_code : null;

                    return $url ? '=HYPERLINK("' . $url . '", "View")' : null;
                },
            ];
        } else if ($report == 'pending_order_report') {
            $query = DB::table("call_master")
                ->select(
                    'call_master.created_at as date',
                    'call_master.invoice_number as invoice',
                    'call_master.call_code as call_code',
                    'employee_master.emp_name as empName',
                    'employee_master.emp_code as empCode',
                    'buyer.cm_name as buyerName',
                    'seller.cm_name as sellerName',
                    'order_master.quantity as totalQty',
                    'order_master.pending_qty as pendingQty',
                    'product_master.product_name as productName',
                    'product_master.barcode as productBarcode'
                )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->leftJoin('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                ->leftJoin('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                ->whereBetween('call_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->where('call_master.call_status', '0')
                ->orderBy('call_master.created_at', 'asc');

            if ($employee != 'all') {
                $query->where("call_master.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $results = $query->get();
            
            $organizedResults = [];
            foreach ($results as $row) {
                $dateKey = date('Y-m-d', strtotime($row->date));
                if (!isset($organizedResults[$dateKey])) {
                    $organizedResults[$dateKey] = [];
                }
                $organizedResults[$dateKey][] = [
                    'date' => date('d-m-Y', strtotime($row->date)),
                    'invoice' => $row->invoice,
                    'empCode' => $row->empCode,
                    'empName' => $row->empName,
                    'buyerName' => $row->buyerName,
                    'sellerName' => $row->sellerName,
                    'totalQty' => $row->totalQty,
                    'pendingQty' => $row->pendingQty,
                    'productBarcode' => $row->productBarcode,
                    'productName' => $row->productName,
                ];
            }
            // dd($organizedResults);
            $title = 'Pending Order Report - ' . ucfirst($custReportType) . ' - ' .  ucfirst($employee) . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\PendingOrderExport($organizedResults, $title), "$title.xlsx");
        } else if ($report == 'payment_collection') {
            $query = DB::table("payment_collection")
                ->select(
                    "payment_collection.created_at as date",
                    "employee_master.emp_code as empCode",
                    "employee_master.emp_name as empName",
                    "customer_master.cm_name as partyName",
                    "customer_master.cm_mobile as partyMobile",
                    "customer_master.cm_area as partyArea",
                    "customer_master.cm_type as partyType",
                    "payment_type.pt_name as paymMode",
                    "bank_master.bank_name as bankName",
                    "payment_collection.pc_amount as amount",
                    "payment_collection.pc_cheque_no as chequeNo",
                    "payment_collection.pc_cheque_date as chequeDate",
                    "payment_collection.pc_note as note",
                    "payment_collection.pc_account_no as accountNo",
                    "payment_collection.pc_cheque_photo as iamgeUrl",
                )
                ->join("employee_master", "payment_collection.emp_code", "=", "employee_master.emp_code")
                ->join("customer_master", "payment_collection.cm_code", "=", "customer_master.cm_code")
                ->leftJoin("bank_master", "payment_collection.pc_bank_id", "=", "bank_master.bank_id")
                ->join("payment_type", "payment_collection.pc_type", "=", "payment_type.pt_id")
                ->whereBetween('payment_collection.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ]);

            if ($custReportType == 'ss') {
                $query->where("customer_master.cm_type", 2);
            } else if ($custReportType == 'distributor') {
                $query->where("customer_master.cm_type", 3);
            } else if ($custReportType == 'dealer') {
                $query->where("customer_master.cm_type", 4);
            } else if ($custReportType == 'retailer') {
                $query->where("customer_master.cm_type", 5);
            } else {
                return;
            }

            if ($employee != 'all') {
                $query->where("payment_collection.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $title = 'Payment Collection Report ' . '-' . ucfirst($custReportType) . ' - ' . ucfirst($employee) . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'EMPLOYEE CODE' => 'empCode',
                'EMPLOYEE NAME' => 'empName',
                'PARTY TYPE' => function ($type) {
                    if ($type->partyType == '2') {
                        return 'SS';
                    } elseif ($type->partyType == '3') {
                        return 'DISTRIBUTOR';
                    } elseif ($type->partyType == '4') {
                        return 'DEALER';
                    } elseif ($type->partyType == '5') {
                        return 'RETAILER';
                    } else {
                        return null;
                    }
                },
                'PARTY AREA' => 'partyArea',
                'PARTY NAME' => 'partyName',
                'PARTY MOBILE' => 'partyMobile',
                'PAYMENT MODE' => 'paymMode',
                'BANK NAME' => 'bankName',
                'ACCOUNT NO' => 'accountNo',
                'AMOUNT' => 'amount',
                'CHEQUE NO' => 'chequeNo',
                'CHEQUE DATE' => 'chequeDate',
                'CREATED DATE' => 'date',
                'NOTE' => 'note',
                'IMAGE URL' => function ($data) {
                    // return asset('uploads/expense_images/' . $data->em_bill_picture);
                    return $data->iamgeUrl ? asset('uploads/payment_collection_images/' . $data->iamgeUrl) : 'No Image';
                },
            ];
        } else if ($report == 'beatwise_retailer_report') {
            $query = DB::table('beat_datewise_master')
                ->select('customer_master.cm_name as cmName', 'beat_master.beat_name as beatName', 'employee_master.emp_name as empName', 'employee_master.emp_code as empCode', 'employee_master.emp_status')
                ->join('beat_master', 'beat_datewise_master.beat_code', '=', 'beat_master.beat_code')
                ->join('employee_master', 'beat_datewise_master.emp_code', '=', 'employee_master.emp_code')
                ->join('cm_beat_relation', 'beat_master.beat_code', '=', 'cm_beat_relation.beat_code')
                ->join('customer_master', 'cm_beat_relation.cm_code', '=', 'customer_master.cm_code')
                ->whereBetween('beat_datewise_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])->distinct();

            if ($employee != 'all') {
                $query->where("beat_datewise_master.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            $title = 'Beatwise Retailer Report -' . $employee . '(' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'EMPLOYEE CODE' => 'empCode',
                'EMPLOYEE NAME' => 'empName',
                'BEAT NAME' => 'beatName',
                'RETAILER NAME' => 'cmName',
            ];
        } else if ($report == 'month_on_month_retail_sales') {
            // Get the start and end dates
            $startOfMonth = Carbon::parse($start_date)->startOfMonth();
            $endOfMonth = Carbon::parse($end_date)->endOfMonth();

            // Create the month range string
            $startMonth = $startOfMonth->format('F');
            $endMonth = $endOfMonth->format('F');
            $monthRange = ($startMonth === $endMonth) ? $startMonth : "$startMonth to $endMonth";

            // Initialize an empty array to store the select expressions
            $selectExpressions = [];

            // Initialize an empty array to store the column names
            $columns = [
                'STATE NAME' => 'stateName',
                'TOWN NAME' => 'townName',
                'SHOP NAME' => 'shopName',
                'BEAT NAME' => 'beatName',
                // 'TOTAL SALES' => 'totalSales'
            ];

            // Loop through each month within the range
            $currentMonth = $startOfMonth->copy();
            while ($currentMonth <= $endOfMonth) {
                $monthName = $currentMonth->format('F');
                $selectExpressions[] = DB::raw("SUM(CASE WHEN MONTH(call_master.created_at) = {$currentMonth->format('m')} THEN call_master.grand_total ELSE 0 END) AS $monthName");

                // Add month name to the columns array
                $columns[$monthName] = $monthName;

                // Move to the next month
                $currentMonth->addMonth();
            }

            $query = DB::table('customer_master')
                ->select(array_merge($selectExpressions, [
                    'customer_master.cm_name as shopName',
                    'beat_master.beat_name as beatName',
                    'town_master.town_name as townName',
                    'state_master.state_name as stateName',
                    DB::raw('SUM(call_master.grand_total) as totalSales')
                ]))
                ->join('cm_beat_relation', 'cm_beat_relation.cm_code', '=', 'customer_master.cm_code')
                ->join('beat_master', 'beat_master.beat_code', '=', 'cm_beat_relation.beat_code')
                ->join('beat_datewise_master', 'beat_datewise_master.beat_code', '=', 'beat_master.beat_code')
                ->join('town_master', 'beat_master.town_id', '=', 'town_master.town_id')
                ->join('district_master', 'town_master.district_id', '=', 'district_master.dm_id')
                ->join('state_master', 'district_master.state_id', '=', 'state_master.state_id')
                ->join('call_master', 'call_master.client_code', '=', 'customer_master.cm_code')
                ->where('call_master.created_at', '>=', $start_date)
                ->where('call_master.created_at', '<=', $end_date)
                ->groupBy('stateName', 'townName', 'shopName', 'beatName');


            if ($state != 'all') {
                $stateRecord = StateMaster::find($state);
                if ($stateRecord) {
                    $stateName = $stateRecord->state_name;

                    $query->where("state_master.state_id", $state);
                }
            }

            if ($town != 'all') {
                $townRecord = TownMaster::find($town);
                if ($townRecord) {
                    $townName = $townRecord->town_name;

                    $query->where("town_master.town_id", $town);
                }
            }

            $title = "Month On Month Retailer Sales Report - State($stateName) Town($townName) (MONTH RANGE: $monthRange)";
        } else if ($report == 'target_vs_achivement') {
            $title = 'Target Vs Achivement Report - ' . $employee . '(' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';
            if ($downloadType == 'json') {
                $query = DB::table('target_master')
                    ->select(
                        'employee_master.emp_name as empName',
                        'employee_master.emp_code as empCode',
                        'target_master.tm_type as type',
                        'target_master.tm_primary_target as priTarget',
                        'target_master.tm_secondary_target as secTarget',
                        'target_master.tm_start_date as startDate',
                        'target_master.tm_end_date as endDate',
                        'designation_master.dm_name as designation',
                        'employee_master.emp_status',
                        DB::raw("SUM(call_master.grand_total) as totalSellAmount"),
                        DB::raw("SUM(call_master.total_quantity) as totalSellQty"),
                        DB::raw("SUM(CASE WHEN customer_master.cm_type IN (1,2,3) THEN call_master.grand_total ELSE 0 END) as primaryTotal"),
                        DB::raw("SUM(CASE WHEN customer_master.cm_type IN (4,5) THEN call_master.grand_total ELSE 0 END) as secondaryTotal"),
                        DB::raw("SUM(CASE WHEN customer_master.cm_type IN (1,2,3) THEN call_master.total_quantity ELSE 0 END) as primaryQty"),
                        DB::raw("SUM(CASE WHEN customer_master.cm_type IN (4,5) THEN call_master.total_quantity ELSE 0 END) as secondaryQty")
                    )
                    ->join('employee_master', 'target_master.emp_code', '=', 'employee_master.emp_code')
                    ->join('call_master', 'target_master.emp_code', '=', 'call_master.emp_code')
                    ->join('customer_master', 'call_master.client_code', '=', 'customer_master.cm_code')
                    ->join('designation_master', 'designation_master.dm_id', '=', 'employee_master.designation_id')
                    ->groupBy(
                        'employee_master.emp_name',
                        'employee_master.emp_code',
                        'target_master.tm_type',
                        'target_master.tm_start_date',
                        'target_master.tm_end_date',
                        'designation_master.dm_name',
                        'target_master.tm_primary_target',
                        'target_master.tm_secondary_target',
                        'employee_master.emp_status'
                    )
                    ->where('target_master.tm_start_date', '>=', $start_date)
                    ->where('target_master.tm_end_date', '<=', $end_date);

                if ($employee != 'all') {
                    $query->where('target_master.emp_code', $employee);
                }

                if ($employeeStatus != 'all') {
                    if ($employeeStatus == 'enable') {
                        $query->where("employee_master.emp_status", 1);
                    } elseif ($employeeStatus == 'disable') {
                        $query->where("employee_master.emp_status", 0);
                    }
                } else {
                    $query->whereIn("employee_master.emp_status", [0, 1]);
                }


                $result = $query->get();

                // Transform the result into the desired format
                $transformedData = [];
                foreach ($result as $data) {
                    $transformedData[] = [
                        'employee_code' => $data->empCode,
                        'employee_name' => $data->empName,
                        'designation' => $data->designation,
                        'start_date' => date('d-m-Y', strtotime($data->startDate)),
                        'end_date' => date('d-m-Y', strtotime($data->endDate)),
                        'type' => ($data->type == '1') ? 'Amount' : (($data->type == '2') ? 'Pcs' : 'Not Set'),
                        'primary_target' => $data->priTarget,
                        'primary_achievement' => ($data->type == 1) ? $data->primaryTotal : $data->primaryQty,
                        'primary_percent' => ($data->priTarget != 0) ? number_format(($data->type == 1) ? (($data->primaryTotal / $data->priTarget) * 100) : (($data->primaryQty / $data->priTarget) * 100), 0) . '%' : '0%',
                        'secondary_target' => $data->secTarget,
                        'secondary_achievement' => ($data->type == 1) ? $data->secondaryTotal : $data->secondaryQty,
                        'secondary_percent' => ($data->secTarget != 0) ? number_format(($data->type == 1) ? (($data->secondaryTotal / $data->secTarget) * 100) : (($data->secondaryQty / $data->secTarget) * 100), 0) . '%' : '0%'
                    ];
                }

                // return response json if downloadtype json
                return response()->json(['target' => $transformedData]);
            } else {
                // download type is excel
                $selectYear = $request->select_year;
                $targetType = $request->search_target_type;

                if ($targetType == 'amount') {
                    $tType = 1;
                } elseif ($targetType == 'pcs') {
                    $tType = 2;
                } elseif ($targetType == 'volume') {
                    $tType = 3;
                }

                // get selected year data 
                $baseQuery = DB::table('target_master')
                    ->select(
                        'employee_master.emp_name as empName',
                        'employee_master.emp_code as empCode',
                        'target_master.tm_primary_target as priTarget',
                        'target_master.tm_secondary_target as secTarget',
                        'target_master.tm_start_date',
                        'target_master.tm_end_date',
                        'designation_master.dm_name as designation',
                        DB::raw('MONTH(target_master.tm_start_date) as month'),
                        DB::raw('YEAR(target_master.tm_start_date) as year'),
                        'target_master.tm_type',
                        'target_master.type', // Monthly, Quarterly, Yearly
                    )
                    ->join('employee_master', 'target_master.emp_code', '=', 'employee_master.emp_code')
                    ->join('call_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                    ->join('designation_master', 'designation_master.dm_id', '=', 'employee_master.designation_id')
                    ->where('target_master.tm_type', $tType) // 1-amount,2-pcs,3-volume
                    ->whereYear('target_master.tm_start_date', '=', $selectYear)
                    ->where('call_master.product_order_type', 1)
                    ->whereYear('call_master.created_at', '=', $selectYear);

                // employee fileter
                if ($employee !== 'all') {
                    $baseQuery->where('target_master.emp_code', $employee);
                }

                $targets = $baseQuery->get();

                // Initialize arrays for final data structure
                $employeeData = null;
                $monthlyData = [];
                $quarterlyData = [];
                $yearlyData = [];
                // dd($targets->first());
                if ($targets->isNotEmpty()) {
                    //employee data
                    $employeeData = [
                        'empName' => $targets->first()->empName,
                        'empCode' => $targets->first()->empCode,
                        'designation' => $targets->first()->designation,
                    ];

                    // all month data
                    foreach (range(1, 12) as $month) {
                        $monthlyData[$month] = [
                            'priTarget' => 0,
                            'secTarget' => 0,
                            // 'achievement' => 0,
                            'primaryAchievement' => 0,
                            'secondaryAchievement' => 0,
                            //'achievementPercentage' => 0,
                            'primaryAchievementPercentage' => 0,
                            'secondaryAchievementPercentage' => 0,
                        ];
                    }

                    // Process the targets based on the 'type' (Monthly, Quarterly, Yearly)
                    foreach ($targets as $target) {
                        $month = $target->month;
                        $type = $target->type;

                        // Get the achievement data between the tm_start_date and tm_end_date
                        $achievementQuery = DB::table('call_master')
                            ->select(
                                // DB::raw('SUM(call_master.grand_total) as total_achievement'),
                                DB::raw('SUM(CASE WHEN customer_master.cm_type IN (1, 2, 3) THEN call_master.grand_total ELSE 0 END) as primary_achievement'),
                                DB::raw('SUM(CASE WHEN customer_master.cm_type IN (4,5) THEN call_master.grand_total ELSE 0 END) as secondary_achievement'),
                            )
                            ->join('customer_master', 'call_master.client_code', '=', 'customer_master.cm_code')
                            ->where('call_master.emp_code', $target->empCode)
                            ->whereBetween('call_master.created_at', [$target->tm_start_date, $target->tm_end_date]);

                        // Fetch the achievement data for this target
                        $achievementData = $achievementQuery->first();

                        // Set the achievement based on target type
                        if ($type == 1) { // Monthly target
                            $monthlyData[$month] = [
                                'priTarget' => number_format($target->priTarget, 2, '.', ''),
                                'secTarget' => number_format($target->secTarget, 2, '.', ''),
                                'primaryAchievement' => number_format($achievementData->primary_achievement ?? 0, 2, '.', ''),
                                'secondaryAchievement' => number_format($achievementData->secondary_achievement ?? 0, 2, '.', ''),
                                'primaryAchievementPercentage' => $target->priTarget > 0
                                    ? number_format(($achievementData->primary_achievement ?? 0) / $target->priTarget * 100, 2, '.', '')
                                    : '0.00', // Ensure it shows 0.00
                                'secondaryAchievementPercentage' => $target->secTarget > 0
                                    ? number_format(($achievementData->secondary_achievement ?? 0) / $target->secTarget * 100, 2, '.', '')
                                    : '0.00', // Ensure it shows 0.00
                            ];
                        }

                        // If the target type is Quarterly (2)
                        if ($type == 2) {
                            $quarter = ceil($month / 3);
                            if (!isset($quarterlyData["Q{$quarter}-{$selectYear}"])) {
                                $quarterlyData["Q{$quarter}-{$selectYear}"] = [
                                    'priTarget' => '0.00',
                                    'secTarget' => '0.00',
                                    // 'achievement' => '0.00',
                                    'primaryAchievement' => '0.00',
                                    'secondaryAchievement' => '0.00',
                                    //'achievementPercentage' => '0.00',
                                    'primaryAchievementPercentage' => '0.00',
                                    'secondaryAchievementPercentage' => '0.00',
                                ];
                            }
                            $quarterlyData["Q{$quarter}-{$selectYear}"]['priTarget'] += $target->priTarget;
                            $quarterlyData["Q{$quarter}-{$selectYear}"]['secTarget'] += $target->secTarget;
                            // $quarterlyData["Q{$quarter}-{$selectYear}"]['achievement'] += $achievementData->total_achievement ?? 0; // Add achievement
                            $quarterlyData["Q{$quarter}-{$selectYear}"]['primaryAchievement'] += $achievementData->primary_achievement ?? 0; // Add achievement
                            $quarterlyData["Q{$quarter}-{$selectYear}"]['secondaryAchievement'] += $achievementData->secondary_achievement ?? 0; // Add achievement
                        }

                        // If the target type is Yearly (3)
                        if ($type == 3) {
                            // Initialize yearly data if not set
                            if (!isset($yearlyData['priTarget'])) {
                                $yearlyData['priTarget'] = '0.00';
                            }
                            if (!isset($yearlyData['secTarget'])) {
                                $yearlyData['secTarget'] = '0.00';
                            }
                            if (!isset($yearlyData['primaryAchievement'])) {
                                $yearlyData['primaryAchievement'] = '0.00';
                            }
                            if (!isset($yearlyData['secondaryAchievement'])) {
                                $yearlyData['secondaryAchievement'] = '0.00';
                            }

                            // Sum yearly data
                            $yearlyData['priTarget'] += $target->priTarget;
                            $yearlyData['secTarget'] += $target->secTarget;
                            //$yearlyData['achievement'] += $achievementData->total_achievement ?? 0; // Add achievement
                            $yearlyData['primaryAchievement'] += $achievementData->primary_achievement ?? 0; // Add achievement
                            $yearlyData['secondaryAchievement'] += $achievementData->secondary_achievement ?? 0; // Add achievement
                        }
                    }

                    // Calculate the quarterly totals and percentages
                    for ($quarter = 1; $quarter <= 4; $quarter++) {
                        $quarterData = collect();

                        // Loop through months to aggregate data by quarters
                        foreach ($monthlyData as $month => $data) {
                            if (in_array($month, range((($quarter - 1) * 3) + 1, $quarter * 3))) {
                                $quarterData->push($data);
                            }
                        }

                        if ($quarterData->isNotEmpty()) {
                            $quarterTotal = [
                                'priTarget' => number_format($quarterData->sum('priTarget'), 2, '.', ''),
                                'secTarget' => number_format($quarterData->sum('secTarget'), 2, '.', ''),
                                //'achievement' => $quarterData->sum('achievement'), // Sum the achievements
                                'primaryAchievement' => number_format($quarterData->sum('primaryAchievement'), 2, '.', ''), // Sum the achievements
                                'secondaryAchievement' => number_format($quarterData->sum('secondaryAchievement'), 2, '.', ''), // Sum the achievements
                            ];

                            // Calculate achievement percentage for the quarter
                            $quarterTotal['primaryAchievementPercentage'] = number_format($quarterTotal['priTarget'] > 0
                                ? ($quarterTotal['primaryAchievement'] / $quarterTotal['priTarget']) * 100
                                : 0, 2, '.', '');

                            $quarterTotal['secondaryAchievementPercentage'] = number_format($quarterTotal['secTarget'] > 0
                                ? ($quarterTotal['secondaryAchievement'] / $quarterTotal['secTarget']) * 100
                                : 0, 2, '.', '');

                            $quarterlyData["Q{$quarter}-{$selectYear}"] = $quarterTotal;
                        }
                    }

                    // Process yearly data (sum of all monthly data)
                    $yearlyData = [
                        'priTarget' => number_format(array_sum(array_column($monthlyData, 'priTarget')), 2, '.', ''),
                        'secTarget' => number_format(array_sum(array_column($monthlyData, 'secTarget')), 2, '.', ''),
                        //'achievement' => array_sum(array_column($monthlyData, 'achievement')),
                        'primaryAchievement' => number_format(array_sum(array_column($monthlyData, 'primaryAchievement')), 2, '.', ''),
                        'secondaryAchievement' => number_format(array_sum(array_column($monthlyData, 'secondaryAchievement')), 2, '.', ''),
                    ];

                    // Calculate achievement percentage for the year
                    $yearlyData['primaryAchievementPercentage'] = number_format(($yearlyData['priTarget'] > 0
                        ? ($yearlyData['primaryAchievement'] / $yearlyData['priTarget']) * 100
                        : 0), 2, '.', '');

                    $yearlyData['secondaryAchievementPercentage'] = number_format($yearlyData['secTarget'] > 0
                        ? ($yearlyData['secondaryAchievement'] / $yearlyData['secTarget']) * 100
                        : 0, 2, '.', '');

                    // Prepare the processed data for the export
                    $processedData = [
                        'employee' => $employeeData,
                        'targets' => [
                            'monthly' => $monthlyData,
                            'quarterly' => $quarterlyData,
                            'yearly' => $yearlyData
                        ],
                        'type' => ucfirst($targetType),
                        'year' => $selectYear,
                    ];
                    // dd($processedData['targets']['quarterly']);

                    $fileName = "Target Vs Achievement Report - " . ucfirst($targetType) . " - " . $employeeData['empCode'] . " - " . $selectYear . ".xlsx";
                    return Excel::download(
                        new TargetVsAchievementReportExport($processedData),
                        $fileName
                    );
                } else {
                    // if no data
                    return response()->json(['message' => 'No data available for the selected year'], 404);
                }
            }
        } else if ($report == 'employee_map_report') {

            $empCode = $request->search_employee;
            $employee = DB::table('employee_master as empMas')

                ->select(
                    'empMas.emp_code',
                    'empMas.emp_name',
                    'empMas.emp_mobile',
                    'empMas.emp_email',
                    'empMas.emp_status',
                    'dm.dm_name',
                    'mtm.mtm_type',
                    DB::raw("GROUP_CONCAT(DISTINCT state_master.state_name SEPARATOR ', ') as stateName"),
                    DB::raw("GROUP_CONCAT(DISTINCT district_master.dm_name SEPARATOR ', ') as distName"),
                    'reportEmp.emp_code as repoEmpCode',
                    'reportEmp.emp_name as repoEmpName',
                    DB::raw("GROUP_CONCAT(DISTINCT CONCAT(teamEmp.emp_name, ' (', teamEmp.emp_code,') ') SEPARATOR ', ') as team_members")
                )
                ->where('empMas.emp_code', $empCode)
                ->join('designation_master as dm', 'empMas.designation_id', '=', 'dm.dm_id')
                ->join('market_type_master as mtm', 'empMas.market_type', '=', 'mtm.mtm_id')
                ->join('emp_state_relation as esr', 'empMas.emp_code', '=', 'esr.emp_code')
                ->join('state_master', 'esr.state_id', '=', 'state_master.state_id')
                ->join('emp_dist_relation as edr', 'empMas.emp_code', '=', 'edr.emp_code')
                ->join('district_master', 'edr.district_id', '=', 'district_master.dm_id')
                ->leftJoin('employee_master as reportEmp', 'empMas.reporting_emp_code', '=', 'reportEmp.emp_code')
                ->leftJoin('employee_master as teamEmp', 'empMas.emp_code', '=', 'teamEmp.reporting_emp_code')
                ->groupBy(
                    'empMas.emp_code',
                    'empMas.emp_name',
                    'empMas.emp_mobile',
                    'empMas.emp_email',
                    'empMas.emp_status',
                    'dm.dm_name',
                    'mtm.mtm_type',
                    'repoEmpCode',
                    'repoEmpName',
                )
                ->first();
            // dd($employee);
            $empCustomerRel = DB::table('customer_emp_relation')
                ->where('emp_code', $empCode)
                ->pluck('cm_code');
            // ->get();
            // dd($empCustomerRel);

            $customerMaster = CustomerMaster::where('cm_status', 1)->whereIn('cm_code', $empCustomerRel)->get();
            // dd($customerMaster);
            $beatCode = BeatDatewiseMaster::where('emp_code', $empCode)->where('bdm_status', 1)->pluck('beat_code');
            // dd($beatCode);
            $beatData = BeatMaster::with('town')->where('beat_status', 1)->whereIn('beat_code', $beatCode)->get();
            // dd($beatData);
            // Step 3: Check if the employee exists

            // Get retailers for each beat
            $beatRetailers = [];
            foreach ($beatCode as $code) {
                $retailers = CustomerMaster::select(
                    'customer_master.cm_code',
                    'customer_master.cm_name',
                    'customer_master.cm_address',
                    'customer_master.cm_area',
                    'customer_master.cm_mobile',
                    'customer_master.cm_email',
                    'customer_master.cm_status',
                    'customer_master.cm_type'
                )
                    ->join('cm_beat_relation', 'customer_master.cm_code', '=', 'cm_beat_relation.cm_code')
                    ->where('cm_beat_relation.beat_code', $code)
                    ->where('customer_master.cm_status', 1)
                    ->where('customer_master.cm_type', 5)  // Assuming 5 is the type for retailers
                    ->get();

                if ($retailers->count() > 0) {
                    $beatRetailers[$code] = $retailers;
                }
            }
            if ($employee) {
                // Step 4: Export employee details to Excel
                $fileName = 'Employee Map Report' . ' - ' . $employee->emp_code . '.xlsx';
                return Excel::download(new EmployeeMapExport($employee, $customerMaster, $beatData, $beatRetailers), $fileName);
            } else {
                // Return a 404 if employee is not found
                return response()->json([
                    'message' => 'Employee not found',
                ], 404);
            }
        } else if ($report == 'non_visitor_party_report') {
            if ($custReportType == 'retailer') {
                try {
                    $callMasterRetailerData = CallMaster::where('emp_code', $employee)
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->pluck('client_code')
                        ->unique()
                        ->toArray();

                    // Step 2: Get visited retailer data
                    $retailerData = CustomerMaster::where('cm_type', 5)
                        ->where('cm_status', 1)
                        ->whereIn('cm_code', $callMasterRetailerData)
                        ->pluck('cm_code')
                        ->toArray();

                    $totalVisitShop = count($retailerData);

                    // Step 3: Get beat codes of visited retailers
                    $orderedBeatCode = CmBeatRelation::whereIn('cm_code', $retailerData)
                        ->pluck('beat_code')
                        ->unique()
                        ->toArray();

                    // Step 4: Get all retailers in these beats with their beat information
                    $beatRetailers = CmBeatRelation::whereIn('beat_code', $orderedBeatCode)
                        ->with(['customer' => function ($query) {
                            $query->where('cm_type', 5)
                                ->where('cm_status', 1)
                                ->select('cm_code', 'cm_name', 'cm_mobile', 'cm_area');
                        }, 'beat:beat_code,beat_name'])
                        ->get()
                        ->groupBy('beat_code');

                    $totalShop = $beatRetailers->flatten(1)->unique('cm_code')->count();
                    $notVisitShop = $totalShop - $totalVisitShop;

                    // Prepare export data beat-wise
                    $exportData = [];
                    $grandTotal = [
                        'total' => $totalShop,
                        'visited' => $totalVisitShop,
                        'nonVisited' => $notVisitShop
                    ];

                    foreach ($beatRetailers as $beatCode => $retailers) {
                        $beatName = $retailers->first()->beat->beat_name ?? 'Unknown Beat';

                        // Get retailers for current beat
                        $retailerCodes = $retailers->pluck('cm_code')->toArray();

                        // Calculate beat statistics
                        $totalInBeat = count($retailerCodes);
                        $visitedInBeat = count(array_intersect($retailerCodes, $retailerData));
                        $notVisitedInBeat = $totalInBeat - $visitedInBeat;

                        // Add beat header
                        $exportData[] = ['Beat: ' . $beatName];

                        // Add beat summary
                        // $exportData[] = [
                        //     'Total Shops' => $totalInBeat,
                        //     'Visited Shops' => $visitedInBeat,
                        //     'Non-Visited Shops' => $notVisitedInBeat
                        // ];

                        // $exportData[] = [''];  // Spacing
                        // $exportData[] = ['Code', 'Name', 'Mobile', 'Area'];

                        // Add non-visited retailers for this beat
                        $nonVisitedRetailers = $retailers->filter(function ($relation) use ($retailerData) {
                            return !in_array($relation->cm_code, $retailerData);
                        });

                        if ($nonVisitedRetailers->isNotEmpty()) {
                            foreach ($nonVisitedRetailers as $relation) {
                                if ($relation->customer) {
                                    $exportData[] = [
                                        'Code' => $relation->customer->cm_code,
                                        'Name' => $relation->customer->cm_name,
                                        'Mobile' => $relation->customer->cm_mobile,
                                        'Area' => $relation->customer->cm_area ?? 'N/A'
                                    ];
                                }
                            }
                        } else {
                            $exportData[] = ['No non-visited retailers for this beat'];
                        }

                        $exportData[] = [''];  // Spacing between beats
                    }

                    // Title for the export
                    $title = 'Non-Visit Retailer Report - ' .
                        ($employee ?? 'All Employees') .
                        ' (' . date('d-m-Y', strtotime($start_date)) .
                        ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

                    return Excel::download(
                        new NonVisitPartyReportExport($exportData, $grandTotal, $title, true),
                        $title . '.xlsx'
                    );
                } catch (\Exception $e) {
                    // \Log::error('Error generating retailer report: ' . $e->getMessage());
                    return back()->with('error', 'An error occurred while generating the report. Please try again.');
                }
            } else {
                try {
                    // Define customer types mapping
                    $customerTypeMap = [
                        'ss' => 2,
                        'distributor' => 3,
                        'dealer' => 4
                    ];

                    // Get customer type
                    $customerType = $customerTypeMap[$custReportType];

                    // Get visited customer codes from CallMaster
                    $visitedCustomerCodes = CallMaster::where('emp_code', $employee)
                        ->whereBetween('call_master.created_at', [
                            date("Y-m-d 00:00:00", strtotime($start_date)),
                            date("Y-m-d 23:59:59", strtotime($end_date))
                        ])
                        ->pluck('client_code')
                        ->unique()
                        ->toArray();

                    // Get all assigned customers of the specified type
                    $allAssignedCustomers = CustomerMaster::where('cm_type', $customerType)
                        ->where('cm_status', 1)
                        ->whereHas('cust_emp_relation', function ($query) use ($employee) {
                            $query->where('emp_code', $employee);
                        })
                        ->get();

                    // Calculate totals
                    $totalCustomers = $allAssignedCustomers->count();
                    $visitedCustomers = $allAssignedCustomers->whereIn('cm_code', $visitedCustomerCodes)->count();
                    $nonVisitedCustomers = $totalCustomers - $visitedCustomers;

                    // Prepare export data for non-visited customers
                    $exportData = [];
                    $nonVisitedCustomerData = $allAssignedCustomers->whereNotIn('cm_code', $visitedCustomerCodes);

                    foreach ($nonVisitedCustomerData as $customer) {
                        $exportData[] = [
                            'Code' => $customer->cm_code,
                            'Name' => $customer->cm_name,
                            'Mobile' => $customer->cm_mobile,
                            'Area' => $customer->cm_area ?? 'N/A'
                        ];
                    }

                    // Prepare grand total
                    $grandTotal = [
                        'total' => $totalCustomers,
                        'visited' => $visitedCustomers,
                        'nonVisited' => $nonVisitedCustomers
                    ];

                    // Title for the export
                    $title = 'Non-Visit ' . ucfirst($custReportType) . ' Report - ' .
                        ($employee ?? 'All Employees') .
                        ' (' . date('d-m-Y', strtotime($start_date)) .
                        ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

                    return Excel::download(
                        new NonVisitPartyReportExport($exportData, $grandTotal, $title, false),
                        $title . '.xlsx'
                    );
                } catch (\Exception $e) {
                    return back()->with('error', 'An error occurred while generating the report. Please try again.' . $e->getMessage());
                }
            }
        } else if ($report == 'product_wise_sales_report') {
            // product, type, brand
            if ($productCategory == 'product') {
                $query = DB::table('call_master')
                    ->select(
                        DB::raw("CONCAT(product_master.product_name, ' (', product_master.barcode, ')') as productName"),
                        DB::raw('SUM(order_master.quantity) as totalQty'),
                        DB::raw('SUM(order_master.grand_total) as totalAmount'),
                        DB::raw('ROUND(CASE WHEN SUM(order_master.quantity) != 0 THEN SUM(order_master.grand_total) / SUM(order_master.quantity) ELSE 0 END,2) as rate')  // Division by zero check
                    )
                    ->leftJoin('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                    ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                    ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                    ->leftJoin("customer_master as buyer", "call_master.client_code", "=", "buyer.cm_code")
                    ->whereBetween('call_master.created_at', [
                        date("Y-m-d 00:00:00", strtotime($start_date)),
                        date("Y-m-d 23:59:59", strtotime($end_date))
                    ])
                    ->groupBy('product_master.product_name', 'product_master.barcode')
                    ->orderBy('totalAmount', 'desc');

                if ($employee != 'all') {
                    $query->where("employee_master.emp_code", $employee);
                }

                if ($employeeStatus != 'all') {
                    if ($employeeStatus == 'enable') {
                        $query->where("employee_master.emp_status", 1);
                    } elseif ($employeeStatus == 'disable') {
                        $query->where("employee_master.emp_status", 0);
                    }
                } else {
                    $query->whereIn("employee_master.emp_status", [0, 1]);
                }

                if ($primSecond == 'primary') {
                    $query->whereIn("buyer.cm_type", [1, 2, 3]);
                } elseif ($primSecond == 'secondary') {
                    $query->whereIn("buyer.cm_type", [4, 5]);
                } else {
                    $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);
                }
            } else if ($productCategory == 'type') {
                $query = DB::table('call_master')
                    ->select(
                        'product_type.pt_name as productType',
                        DB::raw('SUM(order_master.quantity) as totalQty'),
                        DB::raw('SUM(order_master.grand_total) as totalAmount'),
                        DB::raw('ROUND(CASE WHEN SUM(order_master.quantity) != 0 THEN SUM(order_master.grand_total) / SUM(order_master.quantity) ELSE 0 END,2) as rate')  // Division by zero check
                    )
                    ->leftJoin('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                    ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                    ->join('product_type', 'product_master.pm_pt_id', '=', 'product_type.pt_id')
                    ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                    ->leftJoin("customer_master as buyer", "call_master.client_code", "=", "buyer.cm_code")
                    ->whereBetween('call_master.created_at', [
                        date("Y-m-d 00:00:00", strtotime($start_date)),
                        date("Y-m-d 23:59:59", strtotime($end_date))
                    ])
                    ->groupBy('product_type.pt_name')
                    ->orderBy('totalAmount', 'desc');

                if ($employee != 'all') {
                    $query->where("employee_master.emp_code", $employee);
                }

                if ($employeeStatus != 'all') {
                    if ($employeeStatus == 'enable') {
                        $query->where("employee_master.emp_status", 1);
                    } elseif ($employeeStatus == 'disable') {
                        $query->where("employee_master.emp_status", 0);
                    }
                } else {
                    $query->whereIn("employee_master.emp_status", [0, 1]);
                }

                if ($primSecond == 'primary') {
                    $query->whereIn("buyer.cm_type", [1, 2, 3]);
                } elseif ($primSecond == 'secondary') {
                    $query->whereIn("buyer.cm_type", [4, 5]);
                } else {
                    $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);
                }
            } else if ($productCategory == 'brand') {
                $query = DB::table('call_master')
                    ->select(
                        'brand_master.brand_name as productBrand',
                        DB::raw('SUM(order_master.quantity) as totalQty'),
                        DB::raw('SUM(order_master.grand_total) as totalAmount'),
                        DB::raw('ROUND(CASE WHEN SUM(order_master.quantity) != 0 THEN SUM(order_master.grand_total) / SUM(order_master.quantity) ELSE 0 END,2) as rate')  // Division by zero check
                    )
                    ->leftJoin('order_master', 'call_master.call_code', '=', 'order_master.call_code')
                    ->join('product_master', 'order_master.product_code', '=', 'product_master.product_code')
                    ->join('brand_master', 'product_master.product_brand', '=', 'brand_master.brand_id')
                    ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                    ->leftJoin("customer_master as buyer", "call_master.client_code", "=", "buyer.cm_code")
                    ->whereBetween('call_master.created_at', [
                        date("Y-m-d 00:00:00", strtotime($start_date)),
                        date("Y-m-d 23:59:59", strtotime($end_date))
                    ])
                    ->groupBy('brand_master.brand_name')
                    ->orderBy('totalAmount', 'desc');

                if ($employee != 'all') {
                    $query->where("employee_master.emp_code", $employee);
                }

                if ($employeeStatus != 'all') {
                    if ($employeeStatus == 'enable') {
                        $query->where("employee_master.emp_status", 1);
                    } elseif ($employeeStatus == 'disable') {
                        $query->where("employee_master.emp_status", 0);
                    }
                } else {
                    $query->whereIn("employee_master.emp_status", [0, 1]);
                }

                if ($primSecond == 'primary') {
                    $query->whereIn("buyer.cm_type", [1, 2, 3]);
                } elseif ($primSecond == 'secondary') {
                    $query->whereIn("buyer.cm_type", [4, 5]);
                } else {
                    $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);
                }
            }

            $salesType = ($primSecond == 'primary') ? 'Primary' : (($primSecond == 'secondary') ? 'Secondary' : 'Primary & Secondary');
            $emp = ($employee == 'all') ? 'All Employee' : $employee;

            $title = 'Product Wise Sales Report - ' . ucfirst($productCategory) . ' Wise' .  ' - ' . $salesType . ' - ' . $emp . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            return Excel::download(new ProductWiseSalesReportExport($query->get(), $title), $title . '.xlsx');
        } else if ($report == 'new_party_report') {
            $query = DB::table('customer_master')
                ->select(
                    'customer_master.cm_code',
                    'customer_master.cm_name',
                    'customer_master.cm_mobile',
                    'customer_master.cm_email',
                    'customer_master.cm_address',
                    'customer_master.cm_pincode',
                    'customer_master.cm_gst',
                    'customer_master.cm_pan',
                    'customer_master.cm_status',
                    'customer_master.cm_outstanding_amount',
                    'customer_master.cm_contact_person',
                    'customer_master.cm_area',
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'customer_master.cm_type',
                    'market_type_master.mtm_type',
                    'town_master.town_name',
                    'customer_master.created_at'
                )
                ->join('market_type_master', 'customer_master.cm_market_type', '=', 'market_type_master.mtm_id')
                ->join('town_master', 'customer_master.cm_town_id', '=', 'town_master.town_id')
                ->join('employee_master', 'customer_master.created_emp_code', '=', 'employee_master.emp_code')
                ->whereBetween('customer_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->orderBy('customer_master.created_at', 'desc');
            // ->get();
            // dd($query->get());

            if ($employee != 'all') {
                $query->where("employee_master.emp_code", $employee);
            }

            if ($employeeStatus != 'all') {
                if ($employeeStatus == 'enable') {
                    $query->where("employee_master.emp_status", 1);
                } elseif ($employeeStatus == 'disable') {
                    $query->where("employee_master.emp_status", 0);
                }
            } else {
                $query->whereIn("employee_master.emp_status", [0, 1]);
            }

            if ($custReportType == 'all') {
                $query->whereIn("customer_master.cm_type", [1, 2, 3, 4, 5]);
            } else if ($custReportType == 'ss') {
                $query->where("customer_master.cm_type", 2);
            } else if ($custReportType == 'distributor') {
                $query->where("customer_master.cm_type", 3);
            } else if ($custReportType == 'dealer') {
                $query->where("customer_master.cm_type", 4);
            } else if ($custReportType == 'retailer') {
                $query->where("customer_master.cm_type", 5);
            }

            $emp = ($employee == 'all') ? 'All Employees' : $employee;

            $cust = ($custReportType == 'all') ? 'All Customers' : $custReportType;

            $title = 'New Party Report - ' . ucfirst($cust) . ' - ' . $emp .  ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            return Excel::download(new NewPartyReport($query->get(), $title), $title . '.xlsx');
        } else if ($report == 'visit_report') {
            $query = DB::table("call_master")
                ->select(
                    'call_master.call_code',
                    'call_master.emp_code',
                    'call_master.invoice_number',
                    'call_master.product_order_type',
                    'call_master.client_code',
                    'call_master.grand_total',
                    'call_master.party_code',
                    'call_master.call_status',
                    'call_master.created_at as date',
                    "call_master.is_telephonic as telephonic",
                    "call_master.call_status as callStatus",
                    'call_master.start_time',
                    'call_master.stop_time',
                    'employee_master.emp_name as empName',
                    'buyer.cm_name as buyerName',
                    'buyer.cm_mobile as buyerMobile',
                    'buyer.cm_area as buyerArea',
                    'seller.cm_name as sellerName',
                    'seller.cm_mobile as sellerMobile',
                    'seller.cm_area as sellerArea',
                    DB::raw('COALESCE(beat_master.beat_name, "N/A") as beatName'),
                    "empMtm.mtm_type as empMarketType",
                    DB::raw('COALESCE(nonproductive_reason.npr_reason, "N/A") as npReason')
                )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('market_type_master as empMtm', 'employee_master.market_type', '=', 'empMtm.mtm_id')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->leftJoin('customer_master as seller', 'call_master.party_code', '=', 'seller.cm_code')
                ->leftJoin('cm_beat_relation', 'buyer.cm_code', '=', 'cm_beat_relation.cm_code')
                ->leftJoin('beat_master', 'cm_beat_relation.beat_code', '=', 'beat_master.beat_code')
                ->leftJoin('nonproductive_reason', 'nonproductive_reason.npr_id', '=', 'call_master.reason_id')
                ->orderBy('call_master.created_at', 'asc')
                ->whereBetween('call_master.created_at', [
                    date("Y-m-d", strtotime($start_date)),
                    date("Y-m-d", strtotime($end_date . ' + 1 day'))
                ]);

            // Apply employee status filter if not 'all'
            if ($employeeStatus != 'all') {
                $query->where("employee_master.emp_status", $employeeStatus == 'enable' ? 1 : 0);
            }

            if ($marketType) {
                $marketTypeRecord = MarketTypeMaster::find($marketType);
                if ($marketTypeRecord) {
                    $query->where("employee_master.market_type", $marketType);
                } else {
                    return;
                }
            }

            // Apply employee filter if not 'all'
            if ($employee != 'all') {
                $query->where("call_master.emp_code", $employee);
            }

            // Fetch data for all customer types
            $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);

            $title = 'Visit Report - All Type - ' . ($marketTypeRecord->mtm_type ?? 'All Markets') . ' - ' . ucfirst($employee) . ' (' . date('d-m-Y', strtotime($start_date)) . ' to ' . date('d-m-Y', strtotime($end_date)) . ')';

            $columns = [
                'DATE' => function ($data) {
                    return date('d-m-Y', strtotime($data->date));
                },
                'START TIME' => 'start_time',
                'STOP TIME' => 'stop_time',
                'EMPLOYEE NAME' => 'empName',
                'BEAT NAME' => 'beatName',
                'PARTY NAME' => 'buyerName',
                'PARTY NUMBER' => 'buyerMobile',
                'DISTRIBUTOR NAME' => 'sellerName',
                'PURPOSE OF VISIT' => 'npReason',
            ];
        } elseif ($report == 'date_wise_km_report') {
            $LogEmp = EmployeeMaster::where('emp_code', $employee)->first();

            $start_date_full = date('Y-m-d 00:00:00', strtotime($start_date));
            $end_date_full = date('Y-m-d 23:59:59', strtotime($end_date));

            // Fetch login coordinates with correct login time
            $login_coordinates = Attendance::select(
                DB::raw('ST_X(atd_login_coordinates) as longitude'),
                DB::raw('ST_Y(atd_login_coordinates) as latitude'),
                'atd_login_time as time',
                'created_at'
            )
                ->where('emp_code', $employee)
                ->whereBetween('created_at', [$start_date_full, $end_date_full])
                ->get();

            // Fetch call records
            $query = CallMaster::select(
                'call_master.call_code',
                DB::raw('ST_X(coordinates) as longitude'),
                DB::raw('ST_Y(coordinates) as latitude'),
                'call_master.product_order_type',
                'customer_master.cm_name',
                'call_master.created_at',
                'call_master.grand_total as gTotal',
                'nonproductive_reason.npr_reason as reason'
            )
                ->leftJoin('nonproductive_reason', 'nonproductive_reason.npr_id', '=', 'call_master.reason_id')
                ->join('customer_master', 'customer_master.cm_code', '=', 'call_master.client_code')
                ->whereBetween('call_master.created_at', [$start_date_full, $end_date_full])
                ->where('call_master.emp_code', $employee)
                ->where('call_master.accuracy', '<=', 2000)
                ->orderBy('call_master.created_at', 'asc')
                ->get();

            // Fetch logout coordinates with correct logout time
            $logout_coordinates = Attendance::select(
                DB::raw('ST_X(atd_logout_coordinates) as longitude'),
                DB::raw('ST_Y(atd_logout_coordinates) as latitude'),
                'atd_logout_time as time',
                'created_at'
            )
                ->where('emp_code', $employee)
                ->whereNotNull('atd_logout_time')  // Ensure logout time exists
                ->whereBetween('created_at', [$start_date_full, $end_date_full])
                ->get();

            $data = [
                'start_date' => $start_date,
                'end_date' => $end_date,
                'query' => $query,
                'login_coordinates' => $login_coordinates,
                'logout_coordinates' => $logout_coordinates,
                'LogEmp' => $LogEmp,
            ];

            return Excel::download(new class($data) implements FromView {
                private $data;

                public function __construct($data)
                {
                    $this->data = $data;
                }

                public function view(): View
                {
                    return view('reports_view.date_wise_km_report', $this->data);
                }
            }, 'date_wise_km_report.xlsx');
        } else if ($report == 'booking_vs_fulfillment_report') {
            $selectYear = $request->select_year;
            $selectMonth = $request->select_month;
            $start_date = $selectYear . '-' . $selectMonth . '-01';
            $end_date = date('Y-m-t', strtotime($start_date));

            $query = DB::table("call_master")
                ->select(
                    'employee_master.emp_code',
                    'employee_master.emp_name',
                    'empMtm.mtm_type as empMarketType',
                    DB::raw("SUM(call_master.grand_total) as booking"), // Total amount
                    DB::raw("SUM(CASE WHEN call_master.call_status = 3 THEN call_master.grand_total ELSE 0 END) as delivered"),
                    DB::raw("SUM(CASE WHEN call_master.call_status = 0 THEN call_master.grand_total ELSE 0 END) as pending"),
                    DB::raw("SUM(CASE WHEN call_master.call_status = 4 THEN call_master.grand_total ELSE 0 END) as cancelled")
                )
                ->join('employee_master', 'call_master.emp_code', '=', 'employee_master.emp_code')
                ->join('market_type_master as empMtm', 'employee_master.market_type', '=', 'empMtm.mtm_id')
                ->join('customer_master as buyer', 'buyer.cm_code', '=', 'call_master.client_code')
                ->where('call_master.product_order_type', 1) // productive
                ->whereBetween('call_master.created_at', [
                    date("Y-m-d 00:00:00", strtotime($start_date)),
                    date("Y-m-d 23:59:59", strtotime($end_date))
                ])
                ->groupBy('employee_master.emp_code', 'employee_master.emp_name', 'empMtm.mtm_type')
                ->orderBy('booking', 'desc');

            if ($employee !== 'all') {
                $query->where("call_master.emp_code", $employee);
            }

            if ($primSecond == 'primary') {
                $query->whereIn("buyer.cm_type", [1, 2, 3]);
            } elseif ($primSecond == 'secondary') {
                $query->whereIn("buyer.cm_type", [4, 5]);
            } else {
                $query->whereIn("buyer.cm_type", [1, 2, 3, 4, 5]);
            }

            $data = $query->get();

            $title = 'Booking Vs Fulfillment Report - ' . ucfirst($primSecond ?? 'both') . ' - ' . ucfirst($employee) . ' - ' . $selectMonth . ' - ' . $selectYear;;
            return Excel::download(new BookingVsFulfillmentExport($data, $title), "{$title}.xlsx");
        } else {
            return 'Report Type Not Match';
        }

        if ($downloadType == 'pdf') {
            return PdfReport::of($title, [], $query, $columns)
                // ->setOrientation('landscape')
                ->setPaper('a2')
                ->setCss([
                    'table tr th'   => 'border:1px solid',
                    'table tr td'   => 'border:1px solid'
                ])
                ->download($title);
        } elseif ($downloadType == 'excel') {
            return ExcelReport::of($title, [], $query, $columns)
                // ->showTotal([
                //     'ORDER AMOUNT' => 'point'
                // ])
                ->download($title);
        } elseif ($downloadType == 'json') {
            return ExcelReport::of($title, [], $query, $columns)
                ->download($title);
        } else {
            return 'Download Type Not Match';
        }
    }
}
