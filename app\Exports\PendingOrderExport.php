<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;

class PendingOrderExport implements FromCollection, WithTitle, ShouldAutoSize, WithStyles, WithEvents
{
    protected $data;
    protected $title;

    public function __construct($data, $title)
    {
        $this->data = $data;
        $this->title = $title;
    }

    public function collection()
    {
        $exportData = [
            [$this->title], // Title row
            [ // Column headers
                'Invoice',
                'Employee Code',
                'Employee Name',
                'Buyer Name',
                'Seller Name',
                'Product Barcode',
                'Product Name',
                'Total Quantity',
                'Pending Quantity',
            ]
        ];

        foreach ($this->data as $date => $orders) {
            $exportData[] = ['Date: ' . date('d-m-Y', strtotime($date))];
            foreach ($orders as $order) {
                $exportData[] = [
                    $order['invoice'],
                    $order['empCode'],
                    $order['empName'],
                    $order['buyerName'],
                    $order['sellerName'],
                    $order['productBarcode'],
                    $order['productName'],
                    $order['totalQty'],
                    $order['pendingQty'],
                ];
            }
            $exportData[] = []; // Empty row for separation
        }
        return collect($exportData);
    }

    public function title(): string
    {
        return 'Pending Orders';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]], // Title row
            2 => ['font' => ['bold' => true]], // Column headers
            //3 => ['font' => ['bold' => true]], // First Date row
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet;
                $lastColumn = $sheet->getHighestColumn();

                // Merge cells for the title
                $sheet->mergeCells("A1:{$lastColumn}1");

                // Center align the title
                $sheet->getStyle("A1:{$lastColumn}1")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

                // Add borders to all cells
                $sheet->getStyle("A1:{$lastColumn}" . $sheet->getHighestRow())->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        ],
                    ],
                ]);
            },
        ];
    }
}
