<div class="page">
    <div class="header-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12" id="cols">
                    @include('orders.orders_header')
                </div>
            </div>
        </div>
    </div>
    <div class="page-content container-fluid table_content_page">
        @include('components.success_error_message')
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('retailer_order_filter') }}</h3>
                <div class="panel-actions" style="right:15px !important">
                    <button id="toggleFormButton" class="waves-effect waves-classic btn btn-primary">
                        <i class="bi bi-funnel fa-lg" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <div class="panel-body container-fluid" id="retailerOrderFilterFormContainer"
                style="padding:15px 0px 0px 0px !important;display:none;">
                <form id="retailerOrderFilterForm" class="add_form">
                    @csrf
                    <div class="col-md-12">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Select Date') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control form-control-sm" name="date"
                                        id="date" placeholder="{{ __('Select Date') }}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Search Shop') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchShop form-control" name="clientCode" id="clientCode">
                                        <option></option>
                                        {{-- append dynamically using ajax --}}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Invoice No') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control form-control-sm" name="invoice"
                                        id="invoice" placeholder="{{ __('Enter Invoice No') }}" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Distributor') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchDistr form-control" name="partyCode" id="partyCode">
                                        <option></option>
                                        @foreach ($delDistList as $dist)
                                            <option value="{{ $dist->cm_code }}">{{ $dist->cm_name }}
                                                ({{ $dist->cm_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Employee') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchEmply form-control" name="employee" id="employee">
                                        <option></option>
                                        @foreach ($empList as $emp)
                                            <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                                ({{ $emp->emp_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Order Type') }} </label>
                                <div style="padding:0; position: sticky;" class="col-sm-8 col-md-8">
                                    <select class="select2_searchType form-control" name="orderType" id="orderType">
                                        <option></option>
                                        <option value="pending">{{ __('Pending') }}</option>
                                        <option value="confirm">{{ __('Confirm') }}</option>
                                        <option value="cancel">{{ __('Cancel') }}</option>
                                        <option value="delivered">{{ __('Delivered') }}</option>
                                        <option value="partialDelivered">{{ __('Partial Delivered') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="clear-both submit-toolbar p-10">
                        <button class="btn btn-primary btn-sm waves-effect waves-classic"><i class="fa-filter mr-10"
                                aria-hidden="true"></i>{{ __('Apply Filter') }}</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <div class="header-area sub_topmenubar" style="box-shadow: 2px 0px 2px 0px grey !important;">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-12" id="cols">
                                {{-- get from orders/orders_header.blade.php file --}}
                                @yield('orders_sub_header')
                            </div>
                        </div>
                    </div>
                </div>
                <h3 class="panel-title">{{ __('retailer_order_list') }}</h3>
                <div class="panel-actions" style="top:68px !important">
                    {{-- <input type="text" id="orderSearchInput" placeholder="Search..."> --}}
                    <button id="approvedButton" class="btn btn-primary btn-sm waves-effect waves-classic"
                        onclick="update_confirm_order_status()">{{ __('Confirm') }}</button>
                    {{-- <button class="btn btn-primary btn-sm waves-effect waves-classic"
                        onclick="update_cancel_order_status()">Cancel</button> --}}
                    <button class="btn btn-primary btn-sm waves-effect waves-classic delivered_all"
                        onclick="update_pend_deli_order_status()">{{ __('Partial Deliver') }}y</button>
                    <button id="deliveredBtn" class="btn btn-primary btn-sm waves-effect waves-classic"
                        onclick="update_deliver_order_status()">{{ __('Delivered') }} </button>
                </div>
            </div>
            <div class="panel-body p-0">
                <div id="retailer_orders_list_table"> </div>
            </div>
        </div>
    </div>
</div>


{{-- button onclick search area hide and unhide --}}
<script>
    $(document).ready(function() {
        $('#toggleFormButton').on('click', function() {
            $('#retailerOrderFilterFormContainer').toggle();
        });
    });
</script>

<script type="text/javascript">
    mark_sidebar_active("orders");

    var dataGrid
    $(document).ready(function() {
        var pageSize = 10;
        dataGrid = $("#retailer_orders_list_table").dxDataGrid({

            // function PATH: public/js/customFunction.js
            dataSource: customServerSidePagination("#orderSearchInput",
                "{{ route('getRetailerOrderAjaxData') }}"),

            remoteOperations: {
                paging: true, // Enable remote paging
                sorting: true
            },
            //columnHidingEnabled: true,
            allowColumnResizing: true,
            columnAutoWidth: true,
            selection: {
                mode: "multiple"
            },
            showRowLines: true,
            rowAlternationEnabled: true,
            showBorders: true,
            filterRow: {
                visible: false
            },
            columnChooser: {
                enabled: true,
                mode: "select"
            },
            columnFixing: {
                enabled: true
            },
            export: {
                enabled: false,
            },
            paging: {
                pageSize: pageSize
            },
            pager: {
                showPageSizeSelector: false,
                showNavigationButtons: true,
                allowedPageSizes: [5, 10, 20],
                showInfo: true
            },
            //  editing: {
            //     mode: "form",
            //     allowUpdating: true,
            //     allowDeleting: true,
            //     allowAdding: true,
            //     useIcons: true,
            // },
            headerFilter: {
                visible: true
            },
            columns: [{
                    dataField: "call_code",
                    caption: "View",
                    dataType: "string",
                    showInColumnChooser: false,
                    allowFiltering: false,
                    allowSorting: false,
                    width: 70,
                    fixed: true,
                    fixedPosition: "right",
                    cellTemplate: function(container, options) {
                        if (options.data.product_order_type === 0) {
                            // If product_order_type is 0, do not show the icon
                            return;
                        } else {
                            container.append(
                                "<div id='disFlex'>" +
                                "<a id='pointerCursor' onClick='retailer_order_edit(\"" +
                                options
                                .value +
                                "\")' ><i class='bi bi-eye-fill fa-lg' style='font-size:22;'></i></a>" +
                                "</div>"
                            );
                        }
                    },
                },
                // {
                //     dataField: "autoIncrementId",
                //     caption: "No",
                //     dataType: "autoinc",
                //     fixed: true,
                // },
                // {
                //     dataField: "call_code",
                //     caption: "code"
                // },
                {
                    dataField: "invoice_number",
                    caption: "Invoice No"
                },
                // {
                //     dataField: "start_time",
                //     caption: "Start Time"
                // },
                // {
                //     dataField: "stop_time",
                //     caption: "Stop Time"
                // },
                {
                    dataField: "empName",
                    caption: "Employee"
                },
                {
                    dataField: "buyerName", //client
                    caption: "Retailer"
                },
                {
                    dataField: "buyerMobile",
                    caption: "Mobile"
                },
                {
                    dataField: "grand_total",
                    caption: "Grand Total"
                },
                {
                    dataField: "sellerName", //party
                    caption: "Distributor"
                },
                {
                    dataField: "date",
                    caption: "Date",
                    dataType: "date",
                    format: "dd-MM-yyyy HH:mm:ss",
                },
                {
                    dataField: "call_status",
                    caption: "Status",
                    dataType: "status",
                    cellTemplate: function(container, options) {
                        if (options.data.call_status == 0) {
                            $('<span>')
                                .addClass('badge badge-warning')
                                .css('font-size', '+=11')
                                .text('Pending')
                                .appendTo(container);
                        } else if (options.data.call_status == 1) {
                            $('<span>')
                                .addClass('badge badge-success')
                                .css('font-size', '+=11')
                                .text('Confirm')
                                .appendTo(container);
                        } else if (options.data.call_status == 2) {
                            $('<span>')
                                .addClass('badge badge-primary')
                                .css('font-size', '+=11')
                                .text('Dispatch')
                                .appendTo(container);
                        } else if (options.data.call_status == 3) {
                            $('<span>')
                                .addClass('badge badge-info')
                                .css('font-size', '+=11')
                                .text('Delivered')
                                .appendTo(container);
                        } else if (options.data.call_status == 4) {
                            $('<span>')
                                .addClass('badge badge-danger')
                                .css('font-size', '+=11')
                                .text('Cancel')
                                .appendTo(container);
                        } else if (options.data.call_status == 5) {
                            $('<span>')
                                .addClass('badge badge-secondary')
                                .css('font-size', '+=11')
                                .text('Partial Delivered')
                                .appendTo(container);
                        }
                    },
                    lookup: {
                        dataSource: [{
                                value: 0,
                                text: 'Pending'
                            },
                            {
                                value: 1,
                                text: 'Confirm'
                            },
                            {
                                value: 2,
                                text: 'Dispatch'
                            },
                            {
                                value: 3,
                                text: 'Delivered'
                            },
                            {
                                value: 4,
                                text: 'Cancel'
                            },
                            {
                                value: 5,
                                text: 'Partial Delivered'
                            },
                        ],
                        displayExpr: 'text',
                        valueExpr: 'value'
                    }
                }
            ],
            onToolbarPreparing: function(e) {
                var toolbarItems = e.toolbarOptions.items;
                toolbarItems.push({
                    location: "after",
                    widget: "dxButton",
                    options: {
                        icon: "exportxlsx",
                        onClick: function() {
                            exportAllRetailerOrderData();
                        }
                    }
                });
            }
        }).dxDataGrid('instance');

        // Add event listener for the search input
        $("#orderSearchInput").on("input", function() {
            dataGrid.refresh();
        });

    });

    initializeDateRangePicker();

    $('#retailerOrderFilterForm').on('submit', function(e) {
        e.preventDefault();
        handleRetaiOrderFormSubmit();
    });

    function initializeDateRangePicker() {
        var today = moment().startOf('day');

        $('#date').daterangepicker({
            startDate: today,
            endDate: today,
            autoUpdateInput: false,
            maxDate: today, // Disable future dates
            locale: {
                format: 'YYYY-MM-DD',
                cancelLabel: 'Clear'
            }
        });

        $('#date').on('apply.daterangepicker', function(ev, picker) {
            var start_date = picker.startDate.format('YYYY-MM-DD');
            var end_date = picker.endDate.format('YYYY-MM-DD');
            $(this).val(start_date + ',' + end_date);
        });

        $('#date').val(today.format('YYYY-MM-DD') + ',' + today.format('YYYY-MM-DD'));
    }

    function handleRetaiOrderFormSubmit() {
        var date = $('#date').val();
        var clientCode = $('#clientCode').val();
        var invoice = $('#invoice').val();
        var partyCode = $('#partyCode').val();
        var employee = $('#employee').val();
        var orderType = $('#orderType').val();

        if (!date) {
            alert("Please select date.");
            return; // Stop further execution
        }

        $('#loader').show(); // Show loading indicator
        $.ajax({
            url: "{{ route('getRetailerFilterList') }}",
            type: "POST",
            data: {
                date: date,
                clientCode: clientCode,
                invoice: invoice,
                partyCode: partyCode,
                employee: employee,
                orderType: orderType,
            },
            dataType: "JSON",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                $('#loader').hide(); // Hide loading indicator
                if (data.status === "success") {
                    getRetailerOrderTableData(data.data);
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#loader').hide(); // Hide loading indicator
                // toastr.error('Error: ' + textStatus);
                if (jqXHR.status === 422) {
                    var errors = jqXHR.responseJSON.errors;
                    var errorMessage = "";
                    $.each(errors, function(key, value) {
                        errorMessage += value +
                            "<br>"; // Append each error message
                    });
                    toastr.error(errorMessage); // Show error message using toastr
                } else {
                    toastr.error('Error: ' + textStatus); // Show generic error message
                }
            }
        });
    }

    function getRetailerOrderTableData(newData) {

        const retailerOrderData = newData.map((item, index) => {
            return {
                ...item,
                autoIncrementId: index + 1
            };
        });

        dataGrid.option("dataSource", retailerOrderData);
    }

    function exportAllRetailerOrderData() {
        var date = $('#date').val();
        var clientCode = $('#clientCode').val();
        var invoice = $('#invoice').val();
        var partyCode = $('#partyCode').val();
        var employee = $('#employee').val();
        var orderType = $('#orderType').val();

        //var formData = $("#retailerOrderFilterForm").serialize();
        // var searchTerm = $("#orderSearchInput").val();

        $.ajax({
            url: "{{ route('getRetailerOrderAjaxData') }}",
            dataType: "json",
            data: {
                date: date,
                clientCode: clientCode,
                invoice: invoice,
                partyCode: partyCode,
                employee: employee,
                orderType: orderType,
                // search: searchTerm,
                export: true
            },
            success: function(response) {
                var workbook = new ExcelJS.Workbook();
                var worksheet = workbook.addWorksheet("Retailer Order List");

                // Add column headers
                worksheet.columns = [{
                        header: "Invoice No",
                        key: "invoice_number",
                        width: 20
                    },
                    {
                        header: "Employee",
                        key: "empName",
                        width: 20
                    },
                    {
                        header: "Retailer",
                        key: "buyerName",
                        width: 20
                    },
                    {
                        header: "Mobile",
                        key: "buyerMobile",
                        width: 20
                    },
                    {
                        header: "Grand Total",
                        key: "grand_total",
                        width: 15
                    },
                    {
                        header: "Distributor",
                        key: "sellerName",
                        width: 20
                    },
                    {
                        header: "Date",
                        key: "date",
                        width: 20
                    },
                    {
                        header: "Status",
                        key: "call_status",
                        width: 15
                    }
                ];

                // Add data
                response.data.forEach(function(item, index) {
                    worksheet.addRow({
                        invoice_number: item.invoice_number,
                        empName: item.empName,
                        buyerName: item.buyerName,
                        grand_total: item.grand_total,
                        sellerName: item.sellerName,
                        date: item.date,
                        call_status: getStatusText(item.call_status)
                    });
                });

                // Export to Excel
                workbook.xlsx.writeBuffer().then(function(buffer) {
                    saveAs(new Blob([buffer], {
                        type: "application/octet-stream"
                    }), "Retailer Order List.xlsx");
                });
            },
            error: function() {
                alert("Data loading error");
            }
        });
    }

    function getStatusText(status) {
        switch (status) {
            case 0:
                return "Pending";
            case 1:
                return "Confirm";
            case 2:
                return "Dispatch";
            case 3:
                return "Delivered";
            case 4:
                return "Cancel";
            case 5:
                return "Partial Delivered";
            default:
                return "";
        }
    }

    function retailer_order_edit(call_code) {
        $.ajax({
            url: "edit_retailer_order/" + call_code,
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.status === "success") {
                    var data = response.data;
                    var modal = $("#retailer_order_detail_edit_modal");
                    modal.modal("show");

                    modal.find("#call_code").val(data.call_code);
                    modal.find("#invoice_number").val(data.invoice_number || null);

                    //from
                    modal.find('#from_cust_name').text(data.customer_party.cm_name)
                    modal.find('#from_cust_address').text(data.customer_party.cm_address)
                    modal.find('#from_cust_gst').text(data.customer_party.cm_gst)
                    modal.find('#from_cust_mobile').text(data.customer_party.cm_mobile)

                    //to
                    modal.find('#to_cust_name').text(data.customer_cli.cm_name)
                    modal.find('#to_cust_address').text(data.customer_cli.cm_address)
                    modal.find('#to_cust_gst').text(data.customer_cli.cm_gst)
                    modal.find('#to_cust_mobile').text(data.customer_cli.cm_mobile)

                    modal.find('#call_done_by_name').text(data.employee.emp_name + ' (' + data.employee
                        .emp_code + ')');

                    modal.find('#order_packaging_charge').text(data.packaging_charge)
                    modal.find('#order_transportation_charge').text(data.transportation_charge)
                    modal.find('#order_transportation_name').text(data.transportation_name)
                    modal.find('#order_grand_total').text(data.grand_total)

                    modal.find('#cancel_description').val(data.cancel_description);
                    var createdDate = new Date(data.created_at);
                    var formattedDate = createdDate.getDate() + ' ' + getMonthName(createdDate.getMonth()) +
                        ' ' + createdDate.getFullYear();

                    modal.find('#order_date').text(formattedDate);

                    function getMonthName(monthIndex) {
                        var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July',
                            'August', 'September', 'October', 'November', 'December'
                        ];
                        return months[monthIndex];
                    }

                    // Fill table with product details
                    var tableBody = modal.find('.orderTable tbody');
                    tableBody.empty(); // Clear existing rows
                    $.each(data.orders, function(index, order) {
                        var productName = order.product ? order.product.product_name : '';
                        var unitSize = order.product ? order.product.unit_size : '';
                        var hsnSac = order.product && order.product.product_hsn_sac_code !== null ?
                            order.product.product_hsn_sac_code : '';
                        var uom = order.product && order.product.uom ? order.product.uom.uom_name :
                            '';
                        var productMrp = order.product ? order.product.product_mrp : '';
                        var row = `
                        <tr>
                            <td class="text-left">${productName}</td>
                            <td class="text-left" contenteditable="true" onBlur="updateOrder('${order.order_code}', 'quantity', this.textContent)">${order.quantity}</td>
                            <td class="text-left">${unitSize} (${uom})</td>
                            <td class="text-left">${order.mrp}</td>
                            <td class="text-left">${hsnSac}</td>
                            <td class="text-left" contenteditable="true" onBlur="updateOrder('${order.order_code}', 'rate_basic', this.textContent)">${order.rate_basic}</td>
                            <td class="text-left">${order.total_basic_rate}</td>
                            <td class="text-left">${order.gst_amount}<br>(${order.gst} %)</td>
                            <td class="text-left">${order.grand_total}</td>
                        </tr>
                    `;
                        tableBody.append(row);
                    });

                }
            }
        });
    }

    function updateOrder(orderId, field, value) {
        $.ajax({
            url: "updateOrder/" + orderId,
            type: "POST",
            data: {
                field: field,
                value: value,
                _token: '{{ csrf_token() }}' // Add this if you're using Laravel
            },
            success: function(data) {
                if (data.status == "success") {
                    toastr.success(data.message);
                    //find call_code and update the grand total
                    var call_code = $("#call_code").val();
                    retailer_order_edit(call_code);
                } else {
                    toastr.error(data.message);
                }
            }
        });
    }

    function updateAllOrderConfirmStatus() {
        event.preventDefault();
        var data = new FormData($("#edit_retailer_order_form")[0]);

        $.ajax({
            url: "{{ route('update_status_conf_order') }}",
            type: "POST",
            data: data,
            dataType: "JSON",
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.status == "success") {
                    $("#retailer_order_detail_edit_modal").modal("hide");
                    toastr.success(data.message);
                    reloadretailerOrderDataTable();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                if (jqXHR.responseJSON && jqXHR.responseJSON.errors) {
                    // If response contains validation errors
                    var errors = jqXHR.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        toastr.error(value);
                    });
                } else {
                    var errorMessage = "An error occurred while updating the ss orders";
                    if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        // If response contains a message
                        errorMessage = jqXHR.responseJSON.message;
                    } else if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.error) {
                        // If the order is already cancelled
                        errorMessage = jqXHR.responseJSON.error;
                    }
                    toastr.error(errorMessage);
                }
            }
        });
    }

    function updateAllOrderCancelStatus() {
        event.preventDefault();
        var data = new FormData($("#edit_retailer_order_form")[0]);

        $.ajax({
            url: "{{ route('update_status_cancel_order') }}",
            type: "POST",
            data: data,
            dataType: "JSON",
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.status == "success") {
                    $("#retailer_order_detail_edit_modal").modal("hide");
                    toastr.success(data.message);
                    reloadretailerOrderDataTable();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                if (jqXHR.responseJSON && jqXHR.responseJSON.errors) {
                    // If response contains validation errors
                    var errors = jqXHR.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        toastr.error(value);
                    });
                } else {
                    var errorMessage = "An error occurred while updating the ss orders";
                    if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        // If response contains a message
                        errorMessage = jqXHR.responseJSON.message;
                    } else if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.error) {
                        // If the order is already cancelled
                        errorMessage = jqXHR.responseJSON.error;
                    }
                    toastr.error(errorMessage);
                }
            }
        });
    }

    function updateAllOrderDelieveredStatus() {
        event.preventDefault();
        var data = new FormData($("#edit_retailer_order_form")[0]);

        $.ajax({
            url: "{{ route('update_status_deliev_order') }}",
            type: "POST",
            data: data,
            dataType: "JSON",
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.status == "success") {
                    $("#retailer_order_detail_edit_modal").modal("hide");
                    toastr.success(data.message);
                    reloadretailerOrderDataTable();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                if (jqXHR.responseJSON && jqXHR.responseJSON.errors) {
                    // If response contains validation errors
                    var errors = jqXHR.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        toastr.error(value);
                    });
                } else {
                    var errorMessage = "An error occurred while updating the ss orders";
                    if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        // If response contains a message
                        errorMessage = jqXHR.responseJSON.message;
                    } else if (jqXHR.status === 422 && jqXHR.responseJSON && jqXHR.responseJSON.error) {
                        // If the order is already cancelled
                        errorMessage = jqXHR.responseJSON.error;
                    }
                    toastr.error(errorMessage);
                }
            }
        });
    }

    function update_confirm_order_status() {
        event.preventDefault();
        var keys = dataGrid.getSelectedRowKeys();
        if (keys.length === 0) {
            toastr.error("Please select at least one row to confirm");
        } else {
            $.ajax({
                url: "{{ route('order_confirm_status_update') }}",
                type: "POST",
                data: {
                    call_code: keys,
                    call_status: 1 // 1 = confirm
                },
                dataType: "json",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status === "success") {
                        toastr.success(response.message);
                        reloadretailerOrderDataTable();
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }
    }

    function update_cancel_order_status() {
        event.preventDefault();
        var keys = dataGrid.getSelectedRowKeys();
        if (keys.length === 0) {
            toastr.error("Please select at least one row to confirm");
        } else {
            $.ajax({
                url: "{{ route('order_cancel_status_update') }}",
                type: "POST",
                data: {
                    call_code: keys,
                    call_status: 4 // 4 = cancel
                },
                dataType: "json",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status === "success") {
                        toastr.success(response.message);
                        reloadretailerOrderDataTable();
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }
    }

    function update_deliver_order_status() {
        event.preventDefault();
        var keys = dataGrid.getSelectedRowKeys();
        if (keys.length === 0) {
            toastr.error("Please select at least one row to confirm");
        } else {
            $.ajax({
                url: "{{ route('order_delievered_status_update') }}",
                type: "POST",
                data: {
                    call_code: keys,
                    call_status: 3 // 3 = delievered
                },
                dataType: "json",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status === "success") {
                        toastr.success(response.message);
                        reloadretailerOrderDataTable();
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }
    }

    function update_pend_deli_order_status() {
        event.preventDefault();
        var keys = dataGrid.getSelectedRowKeys();
        if (keys.length === 0) {
            toastr.error("Please select at least one row to confirm");
        } else {
            $.ajax({
                url: "{{ route('order_pend_deli_status_update') }}",
                type: "POST",
                data: {
                    call_code: keys,
                    call_status: 5 // 5 = partial delivered
                },
                dataType: "json",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status === "success") {
                        toastr.success(response.message);
                        reloadretailerOrderDataTable();
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }
    }

    // reloadDataTable() function called from public/js/customFunction.js
    // function reloadretailerOrderDataTable() {
    //     const url = "{{ route('retailerOrderUpdateTableRefresh') }}";
    //     // const sortField = 'ccm_code';
    //     reloadDataTable(url, sortField = null);
    // }
    function reloadretailerOrderDataTable() {
        handleRetaiOrderFormSubmit();
    }
</script>

<script>
    $(".select2_searchEmply").select2({
        //maximumSelectionLength: 4,
        placeholder: "{{ __('Select Employee') }}",
        allowClear: true
    });
    $(".select2_searchShop").select2({
        //maximumSelectionLength: 4,
        placeholder: "{{ __('Select Shop') }}",
        allowClear: true
    });
    $(".select2_searchType").select2({
        //maximumSelectionLength: 4,
        placeholder: "{{ __('Select Order Type') }}",
        allowClear: true
    });
    $(".select2_searchDistr").select2({
        //maximumSelectionLength: 4,
        placeholder: "{{ __('Select Distributor') }}",
        allowClear: true
    });
</script>

<script>
    $(document).ready(function() {
        $('.select2_searchShop').select2({
            placeholder: '{{ __('Search Shop') }}',
            minimumInputLength: 2,
            ajax: {
                url: "{{ route('searchRetailerByName') }}",
                dataType: 'json',
                delay: 500, // Delay in milliseconds before making the AJAX request
                processResults: function(data) {
                    // Process the JSON data received from the server
                    var options = [];

                    // Loop through the data and construct Select2 option objects
                    $.each(data, function(index, retailer) {
                        options.push({
                            id: retailer
                                .cm_code, // Assuming cm_code is the unique identifier
                            text: retailer.cm_name + ' (' + retailer.cm_area +
                                ')' // Assuming cm_name is the text to display
                        });
                    });

                    // Return the constructed options
                    return {
                        results: options
                    };
                },
                // Additional parameters to send with the request
                data: function(params) {
                    return {
                        term: params.term // Search term entered by the user
                    };
                }
            }
        });
    });
</script>
