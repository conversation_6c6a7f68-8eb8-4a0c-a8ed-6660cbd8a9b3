<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;

class NonVisitPartyReportExport implements FromCollection, WithHeadings, WithTitle, WithEvents, ShouldAutoSize
{
    protected $exportData;
    protected $grandTotal;
    protected $title;
    protected $isRetailer;

    public function __construct($exportData, $grandTotal, $title, $isRetailer = false)
    {
        $this->exportData = $exportData;
        $this->grandTotal = $grandTotal;
        $this->title = $title;
        $this->isRetailer = $isRetailer;
    }

    public function collection()
    {
        // Create summary rows
        $summaryData = [
            [$this->title],
            [''],  // Empty row for spacing
            ['Total Summary'],
            [
                'Total Customers: ' . $this->grandTotal['total'],
                'Total Visited: ' . $this->grandTotal['visited'],
                'Total Non-Visited: ' . $this->grandTotal['nonVisited']
            ],
            [''],  // Empty row before details
            // Add headers
            ['Code', 'Name', 'Mobile', 'Area']
        ];

        if ($this->isRetailer) {
            return collect(array_merge($summaryData, $this->exportData));
        } else {
            // For non-retailer cases, directly add customer data
            $customerData = [];
            foreach ($this->exportData as $customer) {
                $customerData[] = [
                    $customer['Code'],
                    $customer['Name'],
                    $customer['Mobile'],
                    $customer['Area'] ?? 'N/A'
                ];
            }
            return collect(array_merge($summaryData, $customerData));
        }
    }

    public function headings(): array
    {
        return [];  // Headers are handled in collection()
    }

    public function title(): string
    {
        return 'Non-Visit Customer Report';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Set column widths
                $sheet->getColumnDimension('A')->setWidth(15);
                $sheet->getColumnDimension('B')->setWidth(30);
                $sheet->getColumnDimension('C')->setWidth(15);
                $sheet->getColumnDimension('D')->setWidth(20);

                // Bold formatting for headers and summaries
                foreach ($sheet->getRowIterator() as $row) {
                    $cellValue = $sheet->getCell('A' . $row->getRowIndex())->getValue();
                    if (
                        ($this->isRetailer && strpos($cellValue, 'Beat:') === 0) ||
                        $cellValue === 'Total Summary' ||
                        $cellValue === 'Code' ||
                        $row->getRowIndex() === 1
                    ) {
                        $sheet->getStyle('A' . $row->getRowIndex() . ':D' . $row->getRowIndex())
                            ->getFont()->setBold(true);
                    }
                }
            }
        ];
    }
}
