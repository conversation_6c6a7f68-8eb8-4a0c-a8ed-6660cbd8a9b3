<div class="page">
    <div class="header-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12" id="cols">
                    <ul class="nav nav-tabs nav-tabs-line" role="tablist" id="navReportTab">
                        <li class="nav-item" role="presentation">
                            <a href="{{ route('reports') }}" class="nav-link active" role="tab"
                                aria-selected="false">{{ __('Reports') }}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="page-content container-fluid table_content_page" id="addformContainer">
        @include('components.success_error_message')
        <div class="panel panel-bordered">
            <div class="panel-heading">
                <h3 class="panel-title">{{ __('Reports') }}</h3>
            </div>
            <div class="panel-body container-fluid" style="padding:15px 0px 0px 0px !important;">
                <form id="add_reports" class="add_form" autocomplete="off">
                    @csrf
                    <div class="col-md-12">
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row" id="dateField" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Select Date') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <input type="text" class="form-control form-control-sm" name="date"
                                        id="date" placeholder="Select Date" />
                                </div>
                            </div>
                            {{-- year options --}}
                            <div class="form-group row" id="year" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Year') }} </label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_year form-control form-control-sm" name="select_year"
                                        id="select_year">
                                        {{-- append dynamically --}}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="month" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Month') }} </label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_month form-control form-control-sm" name="select_month"
                                        id="select_month">
                                        <option value="">Select Month</option>
                                        <option value="01">January</option>
                                        <option value="02">February</option>
                                        <option value="03">March</option>
                                        <option value="04">April</option>
                                        <option value="05">May</option>
                                        <option value="06">June</option>
                                        <option value="07">July</option>
                                        <option value="08">August</option>
                                        <option value="09">September</option>
                                        <option value="10">October</option>
                                        <option value="11">November</option>
                                        <option value="12">December</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="employee" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Employee') }} </label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_employee form-control form-control-sm" name="search_employee"
                                        id="search_employee">
                                        <option value=""></option>
                                        <option value="all">{{ __('All') }}</option>
                                        @foreach ($emp_list as $emp)
                                            <option value="{{ $emp->emp_code }}">{{ $emp->emp_name }}
                                                ({{ $emp->emp_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="prodReportType" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Report Type') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_r_type form-control form-control-sm" name="r_type"
                                        id="r_type">
                                        <option value=""></option>
                                        <option value="brand_wise">{{ __('Brand Wise') }}</option>
                                        <option value="product_wise">{{ __('Product Wise') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="state" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('State') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_state form-control form-control-sm" name="search_state"
                                        id="search_state">
                                        <option></option>
                                        <option value="all">All State</option>
                                        @foreach ($stateData as $state)
                                            <option value="{{ $state->state_id }}">
                                                {{ $state->state_name }} ({{ $state->state_zone }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="custReportType" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Report Type') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_report_type form-control form-control-sm"
                                        name="cust_report_type" id="cust_report_type">
                                        <option></option>
                                        <option value="retailer">{{ __('Retailer') }}</option>
                                        <option value="ss">{{ __('SS') }}</option>
                                        <option value="distributor">{{ __('Distributors') }}</option>
                                        <option value="dealer">{{ __('Dealer') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="productType" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Product Category') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_product_category form-control form-control-sm"
                                        name="product_category" id="product_category">
                                        <option></option>
                                        <option value="product">{{ __('Product Wise') }}</option>
                                        <option value="type">{{ __('Type Wise') }}</option>
                                        <option value="brand">{{ __('Brand Wise') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Report') }} <span
                                        class="required">*</span></label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_report form-control form-control-sm" name="report"
                                        id="report">
                                        <option value=""></option>
                                        <option value="dbr">{{ __('DBR') }}</option>
                                        <option value="dsr">{{ __('DSR') }}</option>
                                        <option value="attendance_employee">Attendance (Employee)</option>
                                        <option value="attendance_summary">Attendance Summary</option>
                                        <option value="attendance_state">Attendance (State)</option>
                                        <option value="expense_employee">Expense (Employee)</option>
                                        <option value="expense_state">Expense (State)</option>
                                        <option value="order_report">{{ __('Order Report') }}</option>
                                        <option value="pending_order_report">Pending Order Report</option>
                                        <option value="payment_collection">{{ __('Payment Collection') }}</option>
                                        <option value="visit_report">Visit Report</option>
                                        <option value="date_wise_km_report">Date Wise KM Report</option>
                                        <option value="beatwise_retailer_report">{{ __('Beat Wise Retailer Report') }}
                                        </option>
                                        <option value="month_on_month_retail_sales">
                                            {{ __('Month On Month Retail Sales') }}
                                        </option>
                                        <option value="target_vs_achivement">{{ __('Target Vs Achievment') }}</option>
                                        <option value="employee_map_report">{{ __('Employee Map Report') }}</option>
                                        <option value="non_visitor_party_report">{{ __('Non Visitor Party Report') }}
                                        </option>
                                        <option value="product_wise_sales_report">
                                            {{ __('Product Wise Sales Report') }}</option>
                                        <option value="new_party_report">{{ __('New Party Report') }}</option>
                                        <option value="booking_vs_fulfillment_report">Booking Vs Fulfillment Report
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="target_type" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Type') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_target_type form-control form-control-sm"
                                        name="search_target_type" id="search_target_type">
                                        <option></option>
                                        <option value="amount">{{ __('Amount') }}</option>
                                        <option value="pcs">{{ __('Pcs') }}</option>
                                        <option value="volume">{{ __('Volume') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="town" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Town') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_town form-control form-control-sm" name="search_town"
                                        id="search_town">
                                        <option></option>
                                        <option value="all">{{ __('All Town') }}</option>
                                        @foreach ($townData as $town)
                                            <option value="{{ $town->town_id }}">
                                                {{ $town->town_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="marketType" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Market Type') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_market_type form-control form-control-sm"
                                        name="market_type" id="market_type">
                                        <option></option>
                                        @foreach ($marketTypeData as $mType)
                                            <option value="{{ $mType->mtm_id }}">{{ $mType->mtm_type }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="brand" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Brand') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_search_brand form-control form-control-sm"
                                        name="search_brand" id="search_brand">
                                        <option></option>
                                        @foreach ($brandData as $brand)
                                            <option value="{{ $brand->brand_id }}">{{ $brand->brand_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="primSecond" style="display: none;">
                                <label
                                    class="col-sm-4 col-md-4 col-form-label">{{ __('Primary Or Secondary') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_primSecond form-control form-control-sm" name="prima_secon"
                                        id="prima_secon">
                                        <option value="both">{{ __('Both') }}</option>
                                        <option value="primary">{{ __('Primary') }}</option>
                                        <option value="secondary">{{ __('Secondary') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" id="empStatus" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Employee Status') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="select2_emp_status form-control form-control-sm" name="emp_status"
                                        id="emp_status">
                                        <option value="all">{{ __('All') }}</option>
                                        <option value="enable">{{ __('Enable') }}</option>
                                        <option value="disable">{{ __('Disable') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" style="display: none;">
                                <label class="col-sm-4 col-md-4 col-form-label">{{ __('Download Type') }}</label>
                                <div style="padding:0;" class="col-sm-8 col-md-8">
                                    <select class="form-control form-control-sm" name="d_type" id="d_type">
                                        <option value="pdf">{{ __('Pdf') }}</option>
                                        <option value="excel" selected>{{ __('Excel') }}</option>
                                        {{-- <option value="json">json</option> --}}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="clear-both submit-toolbar p-10">
                        <button class="btn btn-primary btn-sm waves-effect waves-classic"><i
                                class="fa-file-text-o mr-10" aria-hidden="true"></i>{{ __('Get Report') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() {
        //default show date field
        $('#dateField').show();
        $('#report').change(function() {
            var selectedReport = $(this).val();
            var selectedEmployee = $('#search_employee').val();

            // Reset all values
            $('#employee, #prodReportType, #custReportType, #marketType, #brand, #state, #town, #empStatus, #primSecond')
                .hide()
                .find('select')
                .each(function() {
                    $(this).val(null).trigger('change');
                });

            if (selectedReport === 'dbr') {
                $('#employee, #prodReportType, #primSecond').show();
                // if (selectedEmployee === 'all') {
                //     $('#empStatus').show();
                // }
            } else if (selectedReport === 'dsr') {
                $('#prodReportType').show();
            } else if (selectedReport === 'attendance_employee') {
                $('#employee').show();
                if (selectedEmployee === 'all') {
                    $('#empStatus').show();
                }
            } else if (selectedReport === 'attendance_summary') {
                $('#employee,#state').show();
            } else if (selectedReport === 'beatwise_retailer_report') {
                $('#employee').show();
            } else if (selectedReport === 'employee_summary_report') {
                $('#employee, #empStatus').show();
            } else if (selectedReport === 'expense_employee') {
                $('#employee').show();
            } else if (selectedReport === 'attendance_state') {
                $('#state').show();
            } else if (selectedReport === 'expense_state') {
                $('#state').show();
            } else if (selectedReport === 'order_report') {
                $('#employee,#custReportType,#marketType').show();
                if (selectedEmployee === 'all') {
                    $('#empStatus').show();
                }
            } else if (selectedReport === 'pending_order_report') {
                $('#employee,#custReportType').show();
                if (selectedEmployee === 'all') {
                    $('#empStatus').show();
                }
            } else if (selectedReport === 'payment_collection') {
                $('#employee, #custReportType').show();
            } else if (selectedReport === 'visit_report') {
                $('#employee').show();
            } else if (selectedReport === 'date_wise_km_report') {
                $('#employee').show();
            } else if (selectedReport === 'month_on_month_retail_sales') {
                $('#state, #town').show();
            } else if (selectedReport === 'target_vs_achivement') {
                $('#employee').show();
            } else if (selectedReport === 'employee_map_report') {
                $('#employee').show();
            } else if (selectedReport === 'non_visitor_party_report') {
                $('#employee,#custReportType').show();
            } else if (selectedReport === 'product_wise_sales_report') {
                $('#primSecond,#employee,#productType').show();
            } else if (selectedReport === 'new_party_report') {
                $('#employee,#custReportType').show();
            } else if (selectedReport === 'booking_vs_fulfillment_report') {
                $('#employee,#primSecond').show();
            } else {
                $('#employee, #prodReportType, #custReportType, #marketType, #brand, #state, #town, #empStatus')
                    .val(null)
                    .hide();
            }

            if (selectedReport === 'dbr' || selectedReport === 'employee_map_report' ||
                selectedReport ===
                'non_visitor_party_report' || selectedReport === 'target_vs_achivement' ||
                selectedReport === 'date_wise_km_report' || selectedReport ===
                'booking_vs_fulfillment_report') {
                $('#search_employee option[value="all"]').remove();
            } else {
                if ($('#search_employee option[value="all"]').length === 0) {
                    $('#search_employee').prepend('<option value="all">All</option>');
                }

            }

            if (selectedReport === 'new_party_report') {
                $('#cust_report_type').prepend('<option value="all">All</option>');
            } else {
                $('#cust_report_type option[value="all"]').remove();
            }

            // hide unhide datefield
            if (selectedReport === 'employee_map_report' ||
                selectedReport === 'target_vs_achivement' ||
                selectedReport === 'booking_vs_fulfillment_report') {

                $('#dateField').hide();
                if (selectedReport === 'target_vs_achivement') {
                    $('#target_type').show();
                } else {
                    $('#target_type').hide();
                }

                if (selectedReport === 'booking_vs_fulfillment_report') {
                    $('#month').show();
                } else {
                    $('#month').hide();
                }

                if (selectedReport === 'target_vs_achivement' || selectedReport ===
                    'booking_vs_fulfillment_report') {
                    $('#year').show();
                } else {
                    $('#year').hide();
                }
            } else {
                $('#target_type').hide();
                $('#year').hide();
                $('#month').hide();
                $('#dateField').show();
            }
        });

        // get year dynamically , previous,current,future
        const presentYear = new Date().getFullYear();
        const nextYear = presentYear + 1;

        const previousYear = presentYear - 1;

        const yearSelect = document.getElementById('select_year');
        yearSelect.innerHTML = ''; // Clear existing options

        const defaultOption = document.createElement('option');
        defaultOption.textContent = ''; // Empty placeholder
        yearSelect.appendChild(defaultOption);

        const previousYearOption = document.createElement('option');
        previousYearOption.value = previousYear;
        previousYearOption.textContent = previousYear;
        yearSelect.appendChild(previousYearOption);

        const currentYearOption = document.createElement('option');
        currentYearOption.value = presentYear;
        currentYearOption.textContent = presentYear;
        yearSelect.appendChild(currentYearOption);

        const nextYearOption = document.createElement('option');
        nextYearOption.value = nextYear;
        nextYearOption.textContent = nextYear;
        yearSelect.appendChild(nextYearOption);

        $('#search_employee').change(function() {
            var selectedEmployee = $(this).val();
            var selectedReport = $('#report').val(); // Get the selected report value

            // Show empStatus only when selectedEmployee is 'all' and selectedReport is 'dbr'
            if (['attendance_employee', 'attendance_summary', 'dbr', 'expense_employee', 'order_report',
                    'pending_order_report',
                    'payment_collection', 'visit_report',
                    'beatwise_retailer_report', 'target_vs_achivement', 'product_wise_sales_report',
                    'new_party_report'
                ].includes(selectedReport)) {
                if (selectedEmployee === 'all') {
                    $('#empStatus').show();
                } else {
                    $('#empStatus').hide();
                }
            }
        });
    });
</script>
<script type="text/javascript">
    mark_sidebar_active("reports");
    $(document).ready(function() {
        $('#add_reports').formValidation({
            framework: "bootstrap4",
            button: {
                disabled: 'disabled'
            },
            icon: null,
            fields: {
                report: {
                    validators: {
                        notEmpty: {
                            message: 'Report is required'
                        }
                    }
                },
            },
            err: {
                clazz: 'invalid-feedback'
            },
            control: {
                // The CSS class for valid control
                valid: 'is-valid',
                // The CSS class for invalid control
                invalid: 'is-invalid'
            },
            row: {
                invalid: 'has-danger'
            }
        });
    });
</script>
<script>
    $(".select2_report").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Report') }}",
        allowClear: true
    });
    $(".select2_emp_status").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Employee Status') }}",
        allowClear: true
    });
    $(".select2_primSecond").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Primary Or Secondary') }}",
        allowClear: true
    });
    $(".select2_town").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Town') }}",
        allowClear: true
    });
    $(".select2_employee").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Employee') }}",
        allowClear: true
    });
    $(".select2_r_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Report Type') }}",
        allowClear: true
    });
    $(".select2_report_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Report Type') }}",
        allowClear: true
    });
    $(".select2_product_category").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Product Category') }}",
        allowClear: true
    });
    $(".select2_market_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Market Type') }}",
        allowClear: true
    });
    $(".select2_search_brand").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Search Brand') }}",
        allowClear: true
    });
    $(".select2_state").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select a State') }}",
        allowClear: true
    });
    $(".select2_year").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Year') }}",
        allowClear: true
    });
    $(".select2_month").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Month') }}",
        allowClear: true
    });
    $(".select2_target_type").select2({
        // maximumSelectionLength: 4,
        placeholder: "{{ __('Select Target Type') }}",
        allowClear: true
    });
</script>

<script>
    $('#date').daterangepicker({
        autoUpdateInput: false,
        locale: {
            format: 'YYYY-MM-DD'
        }
    });

    $('#date').on('apply.daterangepicker', function(ev, picker) {
        var start_date = picker.startDate.format('YYYY-MM-DD');
        var end_date = picker.endDate.format('YYYY-MM-DD');
        $(this).val(start_date + ',' + end_date);
    });

    $(document).ready(function() {
        $('#add_reports').submit(function(event) {
            event.preventDefault(); // Prevent the form from submitting normally

            var selectedReport = $('#report').val();
            var selectedDate = $('#date').val();
            var selectedEmployee = $('#search_employee').val();
            var selectedReportType = $('#r_type').val();
            var selectedState = $('#search_state').val();
            var selecteMarketType = $('#market_type').val();
            var custReportType = $('#cust_report_type').val();
            var seletedProductCategory = $('#product_category').val();
            var selectedTown = $('#search_town').val();
            var employeeStatus = $('#emp_status').val();
            var primSecond = $('#prima_secon').val();
            var downloadType = $('#d_type').val();
            var selectedYear = $('#select_year').val();
            var selectedMonth = $('#select_month').val();
            var selectedTargetType = $('#search_target_type').val();

            if (!selectedReport || (selectedReport !== 'employee_map_report' && selectedReport !==
                    'target_vs_achivement' && selectedReport !== 'booking_vs_fulfillment_report' && !
                    selectedDate)) {
                toastr.error("Please select both date and report before proceeding.");
                return;
            }

            if (['order_report', 'dbr', 'product_wise_sales_report', 'new_party_report',
                    'target_vs_achivement', 'employee_map_report', 'attendance_employee',
                    'attendance_summary',
                    'expense_employee', 'pending_order_report', 'payment_collection', 'visit_report',
                    'date_wise_km_report', 'beatwise_retailer_report', 'non_visitor_party_report',
                    'booking_vs_fulfillment_report'
                ].includes(selectedReport)) {
                if (!selectedEmployee) {
                    toastr.error("Please select employee.");
                    return;
                }
            }

            if (['order_report', 'pending_order_report', 'payment_collection',
                    'new_party_report', 'non_visitor_party_report'
                ].includes(selectedReport)) {
                if (!custReportType) {
                    toastr.error("Please select report type.");
                    return;
                }
            }

            if (['dbr', 'dsr'].includes(selectedReport)) {
                if (!selectedReportType) {
                    toastr.error("Please select report type.");
                    return;
                }
            }

            if (selectedReport == 'product_wise_sales_report') {
                if (!seletedProductCategory) {
                    toastr.error("Please Product Category.");
                    return;
                }
            }

            if (selectedReport == 'target_vs_achivement') {
                if (!selectedYear) {
                    toastr.error("Please select year.");
                    return;
                }
                if (!selectedTargetType) {
                    toastr.error("Please select target type.");
                    return;
                }
            }

            if (selectedReport == 'booking_vs_fulfillment_report') {
                if (!selectedYear) {
                    toastr.error("Please select year.");
                    return;
                }
                if (!selectedMonth) {
                    toastr.error("Please select month.");
                    return;
                }
            }

            // Construct the URL for the new tab
            var url = 'fetchReport?report=' + selectedReport + '&date=' + selectedDate;

            // Add optional parameters if they exist
            if (selectedEmployee) url += '&search_employee=' + selectedEmployee;
            if (selectedYear) url += '&select_year=' + selectedYear;
            if (selectedMonth) url += '&select_month=' + selectedMonth;
            if (selectedTargetType) url += '&search_target_type=' + selectedTargetType;
            if (selectedState) url += '&search_state=' + selectedState;
            if (selecteMarketType) url += '&market_type=' + selecteMarketType;
            if (custReportType) url += '&cust_report_type=' + custReportType;
            if (seletedProductCategory) url += '&product_category=' + seletedProductCategory;
            if (selectedTown) url += '&search_town=' + selectedTown;
            if (employeeStatus) url += '&emp_status=' + employeeStatus;
            if (primSecond) url += '&prima_secon=' + primSecond;
            if (downloadType) url += '&d_type=' + downloadType;
            if (selectedReportType) url += '&r_type=' + selectedReportType;

            // Open the URL in a new tab
            window.open(url, '_blank');
        });
    });
</script>
