<?php
    // getConfigDetails helper function from app/Helper/customHelper.php
    $configDetails = getConfigDetails();
?>
<style>
    #whatsapp-dropdown {
        list-style-type: none;
        padding-left: 0;
        margin-left: 0;
    }

    #whatsapp-dropdown .site-menu-item {
        padding-left: 30px;
        /* Adjust for better indentation */
    }

    #whatsapp-dropdown .site-menu-title {
        font-size: 14px;
        /* Adjust font size if needed */
    }

    .site-menubar {
        transition: all 0.5s ease;
        /* Transition effect for smooth unfolding and folding */
    }
</style>

<body class="site-menubar-fold site-menubar-keep pt-20" style="overflow-x: hidden;">
    
    <div class="site-menubar" onmouseover="toggleMenu(true)" onmouseout="toggleMenu(false)">
        <div class="site-menubar-body">
            <div>
                <div>
                    <ul class="site-menu" data-plugin="menu">

                        <li class="site-menu-item " id="dashboard">
                            <a href="<?php echo e(route('dashboard')); ?>">
                                <i class="site-menu-icon md-view-dashboard" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Dashboard')); ?></span>
                            </a>
                        </li>
                        <?php if(isset($configDetails['customerTypes']['ss']) && $configDetails['customerTypes']['ss']['status'] == 1): ?>
                            <li class="site-menu-item" id="customer">
                                <a href="<?php echo e(route('customer_list', ['type' => 'ss'])); ?>">
                                    <i class="site-menu-icon fa-user-plus" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('Customers')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['distributor']) &&
                                $configDetails['customerTypes']['distributor']['status'] == 1): ?>
                            <li class="site-menu-item" id="customer">
                                <a href="<?php echo e(route('customer_list', ['type' => 'distributor'])); ?>">
                                    <i class="site-menu-icon fa-user-plus" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('Distributors')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['dealer']) && $configDetails['customerTypes']['dealer']['status'] == 1): ?>
                            <li class="site-menu-item" id="customer">
                                <a href="<?php echo e(route('customer_list', ['type' => 'dealer'])); ?>">
                                    <i class="site-menu-icon fa-user-plus" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('Dealers')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['retailer']) && $configDetails['customerTypes']['retailer']['status'] == 1): ?>
                            <li class="site-menu-item" id="customer">
                                <a href="<?php echo e(route('customer_list', ['type' => 'retailer'])); ?>">
                                    <i class="site-menu-icon fa-user-plus" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('Retailers')); ?></span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <li class="site-menu-item " id="products">
                            <a href="<?php echo e(route('product.index')); ?>">
                                <i class="site-menu-icon fa-cart-arrow-down" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Products')); ?></span>
                            </a>
                        </li>
                        <?php if(isset($configDetails['customerTypes']['retailer']) && $configDetails['customerTypes']['retailer']['status'] == 1): ?>
                            <li class="site-menu-item" id="orders">
                                <a href="<?php echo e(route('retailer_orders')); ?>">
                                    <i class="site-menu-icon fa-first-order" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('orders')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['dealer']) && $configDetails['customerTypes']['dealer']['status'] == 1): ?>
                            <li class="site-menu-item" id="orders">
                                <a href="<?php echo e(route('dealer_orders')); ?>">
                                    <i class="site-menu-icon fa-first-order" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('orders')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['distributor']) &&
                                $configDetails['customerTypes']['distributor']['status'] == 1): ?>
                            <li class="site-menu-item" id="orders">
                                <a href="<?php echo e(route('distributor_orders')); ?>">
                                    <i class="site-menu-icon fa-first-order" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('orders')); ?></span>
                                </a>
                            </li>
                        <?php elseif(isset($configDetails['customerTypes']['ss']) && $configDetails['customerTypes']['ss']['status'] == 1): ?>
                            <li class="site-menu-item" id="orders">
                                <a href="<?php echo e(route('ss_orders')); ?>">
                                    <i class="site-menu-icon fa-first-order" aria-hidden="true"></i>
                                    <span class="site-menu-title"><?php echo e(__('orders')); ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="site-menu-item " id="employees">
                            <a href="<?php echo e(route('employee.index')); ?>">
                                <i class="site-menu-icon fa-users" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Employees')); ?></span>
                            </a>
                        </li>
                        <li class="site-menu-item " id="reports">
                            <a href="<?php echo e(route('reports')); ?>">
                                <i class="site-menu-icon fa-list-alt" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Reports')); ?></span>
                            </a>
                        </li>
                        <li class="site-menu-item " id="stock">
                            <a href="<?php echo e(route('stock.index')); ?>">
                                <i class="site-menu-icon fa-line-chart" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Stock')); ?></span>
                            </a>
                        </li>
                        <li class="site-menu-item " id="settings">
                            <a href="<?php echo e(route('designations.index')); ?>">
                                <i class="site-menu-icon wb-settings" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('General Settings')); ?></span>
                            </a>
                        </li>
                        <li class="site-menu-item" id="whatsapp">
                            <a onclick="toggleWhatsappDropdown()">
                                <i class="site-menu-icon fa-whatsapp" aria-hidden="true"></i>
                                <span class="site-menu-title"><?php echo e(__('Whatsapp')); ?></span>
                            </a>
                            <!-- Dropdown for Whatsapp -->
                            <ul id="whatsapp-dropdown" class="site-menu-submenu" style="display: none;">
                                
                                <li class="site-menu-item">
                                    <a href="<?php echo e(route('whatsappBroadcasting')); ?>">
                                        <i class="site-menu-icon fa-comment" aria-hidden="true"></i>
                                        <span class="site-menu-title"><?php echo e(__('Broadcasting')); ?></span>
                                    </a>
                                    <a href="<?php echo e(route('templateMapping')); ?>">
                                        <i class="site-menu-icon fa-regular fa-comment-dots" aria-hidden="true"></i>
                                        <span class="site-menu-title"><?php echo e(__('Order Messages')); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>


<script>
    function mark_sidebar_active(item) {
        const menu_item = ['dashboard', 'customer', 'products', 'orders', 'employees', 'reports','stock', 'settings',
            'whatsapp'
        ];

        const boxes = document.querySelectorAll(
            menu_item.map(id => `#${id}`).join(',')
        );
        [].forEach.call(boxes, function(el) {
            el.classList.remove("active");
        });

        const item_toactive = document.querySelector('#' + item);
        item_toactive.classList.add("active");

    }

    function toggleWhatsappDropdown() {
        const dropdown = document.getElementById('whatsapp-dropdown');
        if (dropdown.style.display === "none" || dropdown.style.display === "") {
            dropdown.style.display = "block"; // Show dropdown
        } else {
            dropdown.style.display = "none"; // Hide dropdown
        }
    }

    function toggleMenu(isHover) {
        const body = document.querySelector('body');
        if (isHover) {
            body.classList.remove('site-menubar-fold');
            body.classList.add('site-menubar-unfold');
        } else {
            body.classList.remove('site-menubar-unfold');
            body.classList.add('site-menubar-fold');
        }
    }
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\sfm_v2_laravel\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>