{"__meta": {"id": "Xb056389fb1fb6509e7290dab80351253", "datetime": "2025-06-03 13:13:03", "utime": **********.567337, "method": "GET", "uri": "/sfm_v2_laravel/public/fetchNotifications", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748936582.994611, "end": **********.567357, "duration": 0.5727460384368896, "duration_str": "573ms", "measures": [{"label": "Booting", "start": 1748936582.994611, "relative_start": 0, "end": **********.415971, "relative_end": **********.415971, "duration": 0.4213600158691406, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.415993, "relative_start": 0.42138195037841797, "end": **********.567359, "relative_end": 1.9073486328125e-06, "duration": 0.1513659954071045, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36693040, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET fetchNotifications", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@fetchNotifications", "namespace": null, "prefix": "", "where": [], "as": "fetchNotifications", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=420\" onclick=\"\">app/Http/Controllers/DashboardController.php:420-454</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00566, "accumulated_duration_str": "5.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.497333, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.505341, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 45.583}, {"sql": "select * from `leave_application` where `la_notification_status` = 0 order by `created_at` desc", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 426}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.518568, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:426", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=426", "ajax": false, "filename": "DashboardController.php", "line": "426"}, "connection": "gopi_new", "explain": null, "start_percent": 45.583, "width_percent": 30.742}, {"sql": "select * from `employee_master` where `employee_master`.`emp_code` in ('EMP0001', 'EMP0002', 'EMP0003', 'EMP0004', 'EMP0006', 'EMP0008', 'EMP0010', 'EMP0011', 'EMP0022', 'EMP0024', 'EMP0030', 'EMP0031', 'EMP0032', 'EMP0041', 'EMP0044', 'EMP0048', 'EMP0050', 'EMP0051', 'EMP0055', 'EMP0057', 'EMP0060', 'EMP0062', 'NXC1234')", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0002", "EMP0003", "EMP0004", "EMP0006", "EMP0008", "EMP0010", "EMP0011", "EMP0022", "EMP0024", "EMP0030", "EMP0031", "EMP0032", "EMP0041", "EMP0044", "EMP0048", "EMP0050", "EMP0051", "EMP0055", "EMP0057", "EMP0060", "EMP0062", "NXC1234"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 426}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5310302, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:426", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=426", "ajax": false, "filename": "DashboardController.php", "line": "426"}, "connection": "gopi_new", "explain": null, "start_percent": 76.325, "width_percent": 23.675}]}, "models": {"data": {"App\\Models\\LeaveApplication": {"value": 73, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FLeaveApplication.php&line=1", "ajax": false, "filename": "LeaveApplication.php", "line": "?"}}, "App\\Models\\EmployeeMaster": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmployeeMaster.php&line=1", "ajax": false, "filename": "EmployeeMaster.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 97, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/fetchNotifications", "status_code": "<pre class=sf-dump id=sf-dump-366425475 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-366425475\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-866220151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-866220151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-846832565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-846832565\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6InJFT3VUeGhXK1FycldscnV3RThIM0E9PSIsInZhbHVlIjoia2lrRXNtQ0VqZCt1d1Q3dWUvUURFdmJYZ1RRU3pDQnZLRDRZQU1BUFRpU2Z2MFpwZWxMVmNZOUd6Mm9sRll0VEpwUzNqNzF3NUd3OXZ5QUs4aWVlTmZYRUh3d1VRWTd0U3hFQ1VRRFNhQ29xZGVWaHhTV2tDMVUyRnBCUlhhR0YiLCJtYWMiOiIwMmU2ZjAxZmVjMzBiMjA2NDAxMWMyYjlkNTY2ZGI5OTEzOWEzNGJiZTgxYTA4MDZhODVjM2FlZTc3OTNkNGRjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImMydUtzYzY2UGgxYnQxckJzY1hvMUE9PSIsInZhbHVlIjoieFRCNDFTL2lEenhiUTBNRUF1ZmpUUGVzcWVQaW9HelM4djVaZ2FCZVN6ajE3TWhWTVo2T3RCOE90YWVFeFNlQ1hXUUdtZC9yek0xeXZtM0JLMHZ4aHdvR3Y5NW04WDhmTU15MktmU1M3d0JVUHhuZEdoa0VmZlpZUTEwcDFFek4iLCJtYWMiOiIwYTczZGUzMDc3NTBmYjhjYTkxNDU1NDg4ZGI2NzBkOGZlMGVjYjllZDRkOTY3NjY0NmY5YzQwZjYzMTc1MmI1IiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6IkQrdWdwdEsyUkVJWDdQWWJXNUdBbVE9PSIsInZhbHVlIjoicm9WS283b1pBQjZaOWcvUTZmbnFGdmtEQXdEcUNDVmgwSEwzeFRFK0hzQldjRVcvMlU4VkRGQjV5a1VrdjNNR0ZiWVNDNC96dDczeU55ODJNZjd5M3lWNHl1UWF4Y2trNVd0VDQ4dkhMc3RFVlBoWndjdGc0R1pDaTkrSnpSbXQiLCJtYWMiOiJhOGYyMjMwZTkwMzM5OTJmYWM0MmQ1MjNmMzEzNTJlYWZmNzQ2NWZjNGQ4ZGZhOGFjNWQyODA0YjMyNTA5MjZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-154391570 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:43:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6IjRkajl0TTNXUHUrSVpXZmcyUjJVY3c9PSIsInZhbHVlIjoiZ0krYWJqUHBXbFhLUlMwd0dnZzZOZXJwOFdlRVBtankwRXEvRGZVREQvRC9iSnc0V2dMa2JtR08wWjRpMFFzd1Q4cnlObG5lREhBQnNXaGI1SWFrbi9CaDNScVJkazFIVW9wVzNpV2RuUHUzSEJ4T2d2UUlUZ3h1OVNWR3BCb2IiLCJtYWMiOiIxYzFhMTg4YzQ3OWNmMDcxYzhjZDRlYmJmYmZmZjE0OGI4Y2E3MmQ0NzFmNGFiZTY0OTQyN2U1YmE3MzJhNjczIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:43:03 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6Im1KTW9sZE5udElHMWlKV3ZHWDE1c1E9PSIsInZhbHVlIjoiVDBYTmN6enFLZTZ2M1FCTVoveWtNWjd3bnBZMXRUNjYveTZGNFp5R0lGWEpRNlk4UjFrc3pMbGpIZitqVjdoaEc4ZUdadTVTcXgrSG1QZjlUVWZETjB0NDJuUFM3NC8wMUt0Yi8wZ2IxTElRem8zUER3QTNnRFFBVXg3cUN6QVEiLCJtYWMiOiJhZTgzMTVhZTJlNzAzODRhNTBmYWE4MDgyNjRiYzEwMTkyNGUzYWU4YzViOWUwMjQxODI5MTZlMjg1NDBhODE2IiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:43:03 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRkajl0TTNXUHUrSVpXZmcyUjJVY3c9PSIsInZhbHVlIjoiZ0krYWJqUHBXbFhLUlMwd0dnZzZOZXJwOFdlRVBtankwRXEvRGZVREQvRC9iSnc0V2dMa2JtR08wWjRpMFFzd1Q4cnlObG5lREhBQnNXaGI1SWFrbi9CaDNScVJkazFIVW9wVzNpV2RuUHUzSEJ4T2d2UUlUZ3h1OVNWR3BCb2IiLCJtYWMiOiIxYzFhMTg4YzQ3OWNmMDcxYzhjZDRlYmJmYmZmZjE0OGI4Y2E3MmQ0NzFmNGFiZTY0OTQyN2U1YmE3MzJhNjczIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:43:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6Im1KTW9sZE5udElHMWlKV3ZHWDE1c1E9PSIsInZhbHVlIjoiVDBYTmN6enFLZTZ2M1FCTVoveWtNWjd3bnBZMXRUNjYveTZGNFp5R0lGWEpRNlk4UjFrc3pMbGpIZitqVjdoaEc4ZUdadTVTcXgrSG1QZjlUVWZETjB0NDJuUFM3NC8wMUt0Yi8wZ2IxTElRem8zUER3QTNnRFFBVXg3cUN6QVEiLCJtYWMiOiJhZTgzMTVhZTJlNzAzODRhNTBmYWE4MDgyNjRiYzEwMTkyNGUzYWU4YzViOWUwMjQxODI5MTZlMjg1NDBhODE2IiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:43:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154391570\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1134407620 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134407620\", {\"maxDepth\":0})</script>\n"}}