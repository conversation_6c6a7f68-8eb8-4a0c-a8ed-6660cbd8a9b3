<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\BankController;
use App\Http\Controllers\BeatController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ChangeCallTypeController;
use App\Http\Controllers\ClaimComplainController;
use App\Http\Controllers\ClaimComplainTypeController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ConfigController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\DesignationController;
use App\Http\Controllers\DistrictController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\HeadquarterController;
use App\Http\Controllers\HolidayController;
use App\Http\Controllers\LeaveController;
use App\Http\Controllers\MarketTypeController;
use App\Http\Controllers\OptNotificationController;
use App\Http\Controllers\PriceGroupController;
use App\Http\Controllers\ProductBrandController;
use App\Http\Controllers\ProductMasterController;
use App\Http\Controllers\OrdersController;
use App\Http\Controllers\EmployeesController;
use App\Http\Controllers\ExpenseTypeController;
use App\Http\Controllers\NoCallReasonController;
use App\Http\Controllers\PaymentCollectionController;
use App\Http\Controllers\ProductTypeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RoutePlanController;
use App\Http\Controllers\SetTargetController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\StateController;
use App\Http\Controllers\TownController;
use App\Http\Controllers\UomController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware('debugbar')->group(function () {

    // admin panel login
    Route::get('lang/change', [AuthController::class, 'change'])->name('changeLang');

    Route::middleware('guest')->group(function () {
        Route::get('/', [AuthController::class, 'loginForm'])->name('loginForm');
        Route::get('login', [AuthController::class, 'login'])->name('login');
        Route::post('postLogin', [AuthController::class, 'postLogin'])->name('postLogin');
    });

    // view order invoice
    Route::get('view_order_invoice/{call_code}.pdf', [OrdersController::class, 'view_order_invoice'])->name('view_order_invoice');

    Route::get('app', function () {
        $conf = getConfigDetails();

        // Check if 'app' key exists and is a valid URL
        if (!empty($conf['app']) && filter_var($conf['app'], FILTER_VALIDATE_URL)) {
            return redirect($conf['app']);
        } else {
            // Redirect to a fallback route if the URL is not valid or missing
            return redirect()->route('dashboard');
        }
    })->name('new_apk');

    // admin panel view
    Route::middleware('auth')->group(function () {

        Route::get('logout', [AuthController::class, 'logout'])->name('logout');

        // change password
        Route::get('profile_setting', [AuthController::class, 'profile_setting'])->name('profile_setting');

        Route::post('updateProfile', [AuthController::class, 'updateProfile'])->name('updateProfile');
        Route::post('postChangePassword', [AuthController::class, 'postChangePassword'])->name('postChangePassword');

        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
        //get tc_pc data using ajax
        Route::get('dashboard_charts', [DashboardController::class, 'dashboard_charts'])->name('dashboard_charts');
        Route::get('getTcPcChartData/{period}', [DashboardController::class, 'getTcPcChartData'])->name('getTcPcChartData');

        Route::get('getTopTenSkuChartData/{period}', [DashboardController::class, 'getTopTenSkuChartData'])->name('getTopTenSkuChartData');

        Route::get('getLastTenSlowMoverChartData/{period}', [DashboardController::class, 'getLastTenSlowMoverChartData'])->name('getLastTenSlowMoverChartData');

        Route::get('fetchNotifications', [DashboardController::class, 'fetchNotifications'])->name('fetchNotifications');
        Route::get('fetchRoutePlanApprNotifications', [DashboardController::class, 'fetchRoutePlanApprNotifications'])->name('fetchRoutePlanApprNotifications');
        Route::get('fetchBeatNotifications', [DashboardController::class, 'fetchBeatNotifications'])->name('fetchBeatNotifications');
        //middleware auth with customers prefix
        Route::prefix('customers')->group(function () {

            Route::get('searchRetailerByName', [CustomersController::class, 'searchRetailerByName'])->name('searchRetailerByName');
            // update customer pin
            Route::post('update_customer_pin', [CustomersController::class, 'update_customer_pin'])->name('update_customer_pin');

            Route::get('{type}_list', [CustomersController::class, 'customer_list'])->name('customer_list');
            Route::get('add_{type}', [CustomersController::class, 'add_customer'])->name('add_customer');
            Route::post('save_{type}', [CustomersController::class, 'save_customer'])->name('save_customer');
            Route::get('edit_{type}/{cm_code}', [CustomersController::class, 'edit_customer'])->name('edit_customer');
            Route::post('update_{type}', [CustomersController::class, 'update_customer'])->name('update_customer');

            // dealer pagination using ajax
            Route::get('getDealerAjaxData', [CustomersController::class, 'getDealerAjaxData'])->name('getDealerAjaxData');

            // retailer pagination using ajax
            Route::get('getRetailerAjaxData', [CustomersController::class, 'getRetailerAjaxData'])->name('getRetailerAjaxData');

            //update table without full page refresh for ss , dist , dealer , retailer
            Route::get('updateTableRefresh/{type}', [CustomersController::class, 'updateTableRefresh'])->name('updateTableRefresh');

            // ss filter list function
            Route::get('getFilterSsList', [CustomersController::class, 'getFilterSsList'])->name('getFilterSsList');
            Route::get('getFilterDistributorList', [CustomersController::class, 'getFilterDistributorList'])->name('getFilterDistributorList');
            Route::get('getFilterDealerList', [CustomersController::class, 'getFilterDealerList'])->name('getFilterDealerList');
            Route::get('getFilterRetailerList', [CustomersController::class, 'getFilterRetailerList'])->name('getFilterRetailerList');

            Route::resource('payment_collection', PaymentCollectionController::class);

            Route::get('getFilterPayCollList', [PaymentCollectionController::class, 'getFilterPayCollList'])->name('getFilterPayCollList');

            //update payment collection approved status
            Route::post('pay_collec_approv_status_update', [PaymentCollectionController::class, 'pay_collec_approv_status_update'])->name('pay_collec_approv_status_update');
            //update payment collection rejected status
            Route::post('pay_collect_rejected_status_update', [PaymentCollectionController::class, 'pay_collect_rejected_status_update'])->name('pay_collect_rejected_status_update');
            //update table without full page refresh
            Route::get('payCollectionUpdateTableRefresh', [PaymentCollectionController::class, 'payCollectionUpdateTableRefresh'])->name('payCollectionUpdateTableRefresh');

            // payment collection pagination using ajax
            Route::get('getPayCollAjaxData', [PaymentCollectionController::class, 'getPayCollAjaxData'])->name('getPayCollAjaxData');

            Route::resource('claim_complain', ClaimComplainController::class);
            Route::get('getClaimComplainList', [ClaimComplainController::class, 'getClaimComplainList'])->name('getClaimComplainList');
            Route::get('claimComplainUpdateTableRefresh', [ClaimComplainController::class, 'claimComplainUpdateTableRefresh'])->name('claimComplainUpdateTableRefresh');
        });

        //middleware auth with products prefix
        Route::prefix('products')->group(function () {

            Route::resource('product', ProductMasterController::class);

            // get product by brand id
            Route::get('get_product_type_by_brand_id', [ProductMasterController::class, 'getTypeByBrandId'])->name('getTypeByBrandId');

            Route::get('getFilterProductList', [ProductMasterController::class, 'getFilterProductList'])->name('getFilterProductList');

            //update table without full page refresh
            Route::get('productListUpdateTableRefresh', [ProductMasterController::class, 'productListUpdateTableRefresh'])->name('productListUpdateTableRefresh');

            // product pagination using ajax
            Route::get('getProductAjaxData', [ProductMasterController::class, 'getProductAjaxData'])->name('getProductAjaxData');

            Route::resource('product_type', ProductTypeController::class);
            //update table without full page refresh
            Route::get('productTypeUpdateTableRefresh', [ProductTypeController::class, 'productTypeUpdateTableRefresh'])->name('productTypeUpdateTableRefresh');

            Route::resource('product_brand', ProductBrandController::class);
            //update table without full page refresh
            Route::get('productBrandUpdateTableRefresh', [ProductBrandController::class, 'productBrandUpdateTableRefresh'])->name('productBrandUpdateTableRefresh');

            Route::resource('uom', UomController::class);
            //update table without full page refresh
            Route::get('uomUpdateTableRefresh', [UomController::class, 'uomUpdateTableRefresh'])->name('uomUpdateTableRefresh');

            Route::resource('price_group', PriceGroupController::class);

            Route::get('getFilterPriceGroupList', [PriceGroupController::class, 'getFilterPriceGroupList'])->name('getFilterPriceGroupList');
            //update table without full page refresh
            Route::get('priceGroupListTableRefresh', [PriceGroupController::class, 'priceGroupListTableRefresh'])->name('priceGroupListTableRefresh');

            // price group pagination using ajax
            Route::get('getPriceGroupAjaxData', [PriceGroupController::class, 'getPriceGroupAjaxData'])->name('getPriceGroupAjaxData');
            Route::get('getPriceData', [PriceGroupController::class, 'getPriceData'])->name('getPriceData');
        });

        //middleware auth with orders prefix
        Route::prefix('orders')->group(function () {
            //use this for open modal when udpate status
            Route::post('update_status_conf_order', [OrdersController::class, 'update_status_conf_order'])->name('update_status_conf_order');
            Route::post('update_status_cancel_order', [OrdersController::class, 'update_status_cancel_order'])->name('update_status_cancel_order');
            Route::post('update_status_deliev_order', [OrdersController::class, 'update_status_deliev_order'])->name('update_status_deliev_order');

            Route::post('updateOrder/{id}', [OrdersController::class, 'updateOrderDetail'])->name('updateOrder');

            Route::get('ss_orders', [OrdersController::class, 'ss_orders'])->name('ss_orders');
            Route::get('edit_ss_order/{call_code}', [OrdersController::class, 'edit_ss_order'])->name('edit_ss_order');
            Route::get('ssOrderUpdateTableRefresh', [OrdersController::class, 'ssOrderUpdateTableRefresh'])->name('ssOrderUpdateTableRefresh');

            Route::get('distributor_orders', [OrdersController::class, 'distributor_orders'])->name('distributor_orders');
            Route::get('edit_distributor_order/{call_code}', [OrdersController::class, 'edit_distributor_order'])->name('edit_distributor_order');
            Route::get('distOrderUpdateTableRefresh', [OrdersController::class, 'distOrderUpdateTableRefresh'])->name('distOrderUpdateTableRefresh');

            Route::get('dealer_orders', [OrdersController::class, 'dealer_orders'])->name('dealer_orders');
            Route::get('edit_dealer_order/{call_code}', [OrdersController::class, 'edit_dealer_order'])->name('edit_dealer_order');
            Route::get('dealerOrderUpdateTableRefresh', [OrdersController::class, 'dealerOrderUpdateTableRefresh'])->name('dealerOrderUpdateTableRefresh');

            Route::get('retailer_orders', [OrdersController::class, 'retailer_orders'])->name('retailer_orders');
            Route::get('edit_retailer_order/{call_code}', [OrdersController::class, 'edit_retailer_order'])->name('edit_retailer_order');
            Route::get('retailerOrderUpdateTableRefresh', [OrdersController::class, 'retailerOrderUpdateTableRefresh'])->name('retailerOrderUpdateTableRefresh');

            //Route::get('dispatch_order_list', [OrdersController::class, 'dispatch_order_list'])->name('dispatch_order_list');
            Route::get('getDispatchOrderList', [OrdersController::class, 'getDispatchOrderList'])->name('getDispatchOrderList');

            Route::post('order_confirm_status_update', [OrdersController::class, 'order_confirm_status_update'])->name('order_confirm_status_update');
            Route::post('order_cancel_status_update', [OrdersController::class, 'order_cancel_status_update'])->name('order_cancel_status_update');
            Route::post('order_delievered_status_update', [OrdersController::class, 'order_delievered_status_update'])->name('order_delievered_status_update');
            Route::post('order_pend_deli_status_update', [OrdersController::class, 'order_pend_deli_status_update'])->name('order_pend_deli_status_update');

            Route::post('getSsFilterList', [OrdersController::class, 'getSsFilterList'])->name('getSsFilterList');
            Route::post('getDistributorFilterList', [OrdersController::class, 'getDistributorFilterList'])->name('getDistributorFilterList');
            Route::post('getDealerFilterList', [OrdersController::class, 'getDealerFilterList'])->name('getDealerFilterList');
            Route::post('getRetailerFilterList', [OrdersController::class, 'getRetailerFilterList'])->name('getRetailerFilterList');

            // orders pagination using ajax
            Route::get('getSsOrderAjaxData', [OrdersController::class, 'getSsOrderAjaxData'])->name('getSsOrderAjaxData');
            Route::get('getDistributorOrderAjaxData', [OrdersController::class, 'getDistributorOrderAjaxData'])->name('getDistributorOrderAjaxData');
            Route::get('getDealerOrderAjaxData', [OrdersController::class, 'getDealerOrderAjaxData'])->name('getDealerOrderAjaxData');
            Route::get('getRetailerOrderAjaxData', [OrdersController::class, 'getRetailerOrderAjaxData'])->name('getRetailerOrderAjaxData');
            Route::get('getDispatchOrderAjaxData', [OrdersController::class, 'getDispatchOrderAjaxData'])->name('getDispatchOrderAjaxData');
        });

        //middleware auth with employees prefix
        Route::prefix('employees')->group(function () {

            Route::resource('employee', EmployeesController::class);
            //employee pin reset
            Route::post('update_employee_pin', [EmployeesController::class, 'update_employee_pin'])->name('update_employee_pin');

            Route::get('notification_history', [EmployeesController::class, 'notification_history'])->name('notification_history');

            //delete employee notification history
            Route::post('deleteEmpNotifHistory', [EmployeesController::class, 'deleteEmpNotifHistory'])->name('deleteEmpNotifHistory');

            //employee sent notification
            Route::post('sentNotification', [EmployeesController::class, 'sentNotification'])->name('sentNotification');
            Route::post('sentBulkNotification', [EmployeesController::class, 'sentBulkNotification'])->name('sentBulkNotification');

            // employee reset imei no
            Route::get('edit_reset_imei/{emp_code}', [EmployeesController::class, 'edit_reset_imei'])->name('edit_reset_imei');
            //update reset imei no
            Route::post('update_reset_imei', [EmployeesController::class, 'update_reset_imei'])->name('update_reset_imei');

            Route::post('add_emp_to_cust_rel/{type}', [EmployeesController::class, 'addEmployeeCustomerRel'])->name('add_emp_to_cust_rel');

            Route::get('getFilterEmployeeList', [EmployeesController::class, 'getFilterEmployeeList'])->name('getFilterEmployeeList');

            //update employee table without full page refresh
            Route::get('employeeUpdateTableRefresh', [EmployeesController::class, 'employeeUpdateTableRefresh'])->name('employeeUpdateTableRefresh');

            Route::get('employee_trace', [EmployeesController::class, 'employee_trace'])->name('employee_trace');

            Route::get('get_filter_records', [EmployeesController::class, 'get_filter_records'])->name('get_filter_records');

            Route::resource('change_call_type', ChangeCallTypeController::class);

            Route::get('getFilterCallTypeList', [ChangeCallTypeController::class, 'getFilterCallTypeList'])->name('getFilterCallTypeList');

            //update table without full page refresh
            Route::get('callTypeTableRefresh', [ChangeCallTypeController::class, 'callTypeTableRefresh'])->name('callTypeTableRefresh');

            Route::resource('set_target', SetTargetController::class);

            Route::get('fetch_target_data', [SetTargetController::class, 'fetchTargetData'])->name('set_target.fetch_target_data');

            //update table without full page refresh
            Route::post('primTargetUpdate', [SetTargetController::class, 'primTargetUpdate'])->name('primTargetUpdate');
            Route::get('getTargetFilterList', [SetTargetController::class, 'getTargetFilterList'])->name('getTargetFilterList');

            Route::get('setTargetUpdateTableRefresh', [SetTargetController::class, 'setTargetUpdateTableRefresh'])->name('setTargetUpdateTableRefresh');

            Route::get('target_vs_achievement', [EmployeesController::class, 'target_vs_achievement'])->name('target_vs_achievement');

            Route::resource('expense', ExpenseController::class);

            //update expense approved status
            Route::post('expenseConfirmStatusUpdate', [ExpenseController::class, 'expenseConfirmStatusUpdate'])->name('expenseConfirmStatusUpdate');
            //update expense rejected status
            Route::post('expenseRejectedStatusUpdate', [ExpenseController::class, 'expenseRejectedStatusUpdate'])->name('expenseRejectedStatusUpdate');

            Route::get('getExpenseList', [ExpenseController::class, 'getExpenseList'])->name('getExpenseList');
            Route::get('expenseUpdateTableRefresh', [ExpenseController::class, 'expenseUpdateTableRefresh'])->name('expenseUpdateTableRefresh');

            //beat
            Route::resource('beat', BeatController::class);

            Route::get('new_beat_requests', [BeatController::class, 'newBeatRequests'])->name('newBeatRequests');
            //update new beat status
            Route::post('newBeatRequestUpdate', [BeatController::class, 'newBeatRequestUpdate'])->name('newBeatRequestUpdate');

            // shwo beat route view on modal edit
            Route::get('beat_route_view', [BeatController::class, 'beatRouteView'])->name('beatRouteView');

            //update table without full page refresh
            Route::get('beatListTableRefresh', [BeatController::class, 'beatListTableRefresh'])->name('beatListTableRefresh');

            // beat pagination using ajax
            Route::get('getBeatAjaxData', [BeatController::class, 'getBeatAjaxData'])->name('getBeatAjaxData');

            //route plan
            Route::resource('route_plan', RoutePlanController::class);

            Route::get('new_route_plan_requests', [RoutePlanController::class, 'newRoutePlanRequests'])->name('newRoutePlanRequests');
            //update route plan status
            Route::post('newRoutePlanRequestUpdate', [RoutePlanController::class, 'newRoutePlanRequestUpdate'])->name('newRoutePlanRequestUpdate');

            Route::get('fetch_route_plan_data', [RoutePlanController::class,'fetchRoutePlanData'])->name('fetchRoutePlanData');

            //update table without full page refresh
            Route::get('getRoutePlanList', [RoutePlanController::class, 'getRoutePlanList'])->name('getRoutePlanList');
            Route::get('routePlanUpdateTableRefresh', [RoutePlanController::class, 'routePlanUpdateTableRefresh'])->name('routePlanUpdateTableRefresh');

            Route::resource('leave', LeaveController::class);

            Route::get('getFilterLeaveList', [LeaveController::class, 'getFilterLeaveList'])->name('getFilterLeaveList');

            //update table without full page refresh
            Route::get('leaveUpdateTableRefresh', [LeaveController::class, 'leaveUpdateTableRefresh'])->name('leaveUpdateTableRefresh');
        });

        //middleware auth with reports prefix
        Route::prefix('reports')->group(function () {
            Route::get('reports', [ReportController::class, 'reports'])->name('reports');
            Route::get('fetchReport', [ReportController::class, 'fetchReport'])->name('fetchReport');
        });

        //middleware auth with settings prefix
        Route::prefix('settings')->group(function () {

            //designation
            Route::resource('designations', DesignationController::class);
            //update table without full page refresh

            // get state using server side pagination testing
            Route::get('stateData', [StateController::class, 'getStateAjaxData'])->name('stateAjaxData');

            Route::get('designationUpdateTableRefresh', [DesignationController::class, 'designationUpdateTableRefresh'])->name('designationUpdateTableRefresh');

            //location prefix
            Route::group(['prefix' => 'locations'], function () {

                //state
                Route::resource('state', StateController::class);

                //headquarter
                Route::resource('headquarter', HeadquarterController::class);
                //update table without full page refresh
                Route::get('headquarterUpdateTableRefresh', [HeadquarterController::class, 'headquarterUpdateTableRefresh'])->name('headquarterUpdateTableRefresh');

                //district
                Route::resource('district', DistrictController::class);
                //update table without full page refresh
                Route::get('districtUpdateTableRefresh', [DistrictController::class, 'districtUpdateTableRefresh'])->name('districtUpdateTableRefresh');

                //town
                Route::resource('town', TownController::class);
                //update table without full page refresh
                Route::get('townUpdateTableRefresh', [TownController::class, 'townUpdateTableRefresh'])->name('townUpdateTableRefresh');

                Route::post('custStateAdd', [StateController::class, 'custStateAdd'])->name('custStateAdd');
                Route::post('custDistAdd', [DistrictController::class, 'custDistAdd'])->name('custDistAdd');
                Route::post('custTownAdd', [TownController::class, 'custTownAdd'])->name('custTownAdd');
            });

            //customer prefix
            Route::group(['prefix' => 'customer'], function () {
                // ss prefix
                Route::get('ss_prefix', [SettingsController::class, 'ss_prefix'])->name('ss_prefix');
                Route::post('save_ss_prefix', [SettingsController::class, 'save_ss_prefix'])->name('save_ss_prefix');

                // distributor prefix
                Route::get('distributor_prefix', [SettingsController::class, 'distributor_prefix'])->name('distributor_prefix');
                Route::post('save_distributor_prefix', [SettingsController::class, 'save_distributor_prefix'])->name('save_distributor_prefix');

                // dealer prefix
                Route::get('dealer_prefix', [SettingsController::class, 'dealer_prefix'])->name('dealer_prefix');
                Route::post('save_dealer_prefix', [SettingsController::class, 'save_dealer_prefix'])->name('save_dealer_prefix');

                // retailer prefix
                Route::get('retailer_prefix', [SettingsController::class, 'retailer_prefix'])->name('retailer_prefix');
                Route::post('save_retailer_prefix', [SettingsController::class, 'save_retailer_prefix'])->name('save_retailer_prefix');
            });

            //expense type list
            Route::resource('expense_type', ExpenseTypeController::class);
            //update table without full page refresh
            Route::get('expTypeUpdateTblRef', [ExpenseTypeController::class, 'expTypeUpdateTblRef'])->name('expTypeUpdateTblRef');

            //order prefix
            Route::group(['prefix' => 'order'], function () {

                //no call reason list
                Route::resource('no_call_reason', NoCallReasonController::class);
                //update table without full page refresh
                Route::get('noCallReasUpdateTblRef', [NoCallReasonController::class, 'noCallReasUpdateTblRef'])->name('noCallReasUpdateTblRef');

                //invoice prefix
                Route::get('invoice_prefix', [SettingsController::class, 'invoice_prefix'])->name('invoice_prefix');
                Route::post('save_invoice_prefix', [SettingsController::class, 'save_invoice_prefix'])->name('save_invoice_prefix');

                // packagin charge
                Route::get('packaging_charge', [SettingsController::class, 'packaging_charge'])->name('packaging_charge');
                Route::post('save_packaging_charge', [SettingsController::class, 'save_packaging_charge'])->name('save_packaging_charge');

                Route::resource('market_type', MarketTypeController::class);
                Route::get('marketTypeUpdateTblRef', [MarketTypeController::class, 'marketTypeUpdateTblRef'])->name('marketTypeUpdateTblRef');
            });

            //claim complain type list
            Route::resource('claim_complain_type', ClaimComplainTypeController::class);
            //bank list table update without full page refresh
            Route::get('claimComplainTypeUpdateTableRefresh', [ClaimComplainTypeController::class, 'claimComplainTypeUpdateTableRefresh'])->name('claimComplainTypeUpdateTableRefresh');

            //bank list
            Route::resource('bank', BankController::class);
            //bank list table update without full page refresh
            Route::get('bankUpdateTableRefresh', [BankController::class, 'bankUpdateTableRefresh'])->name('bankUpdateTableRefresh');

            //category list
            Route::resource('category', CategoryController::class);
            //category list table update without full page refresh
            Route::get('categoryUpdateTableRefresh', [CategoryController::class, 'categoryUpdateTableRefresh'])->name('categoryUpdateTableRefresh');

            // get district by state id
            Route::get('get_district_by_state_id', [SettingsController::class, 'getDistrictByStateId'])->name('get_district_by_state_id');

            Route::get('empDistGetByStateId', [SettingsController::class, 'empDistGetByStateId'])->name('empDistGetByStateId');

            // get town by dist id
            Route::get('get_town_by_dist_id', [SettingsController::class, 'getTownByDistId'])->name('get_town_by_dist_id');

            Route::resource('holiday', HolidayController::class);

            //holiday list table update without full page refresh
            Route::get('holidayUpdateTableRefresh', [HolidayController::class, 'holidayUpdateTableRefresh'])->name('holidayUpdateTableRefresh');

            Route::resource('config', ConfigController::class);

            //config list table update without full page refresh
            Route::get('configUpdateTableRefresh', [ConfigController::class, 'configUpdateTableRefresh'])->name('configUpdateTableRefresh');

            Route::resource('config_notification', OptNotificationController::class);

            //config notification list table update without full page refresh
            Route::get('confNotifiUpdateTableRefresh', [OptNotificationController::class, 'confNotifiUpdateTableRefresh'])->name('confNotifiUpdateTableRefresh');
        });

        Route::resource('company', CompanyController::class);
        Route::get('companyUpdateTableRefresh', [CompanyController::class, 'companyUpdateTableRefresh'])->name('companyUpdateTableRefresh');
    });

    Route::fallback(function () {
        return response()->view('errors.404', [], 404);
    });
});
