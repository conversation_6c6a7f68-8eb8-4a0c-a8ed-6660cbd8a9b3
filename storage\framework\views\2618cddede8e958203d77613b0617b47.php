<?php
    // getConfigDetails helper function from app/Helper/customHelper.php
    $configDetails = getConfigDetails();
?>
<div class="row mb--10">
    <div class="col-lg-6 col-xl-3 mb-3">
        <a href="<?php echo e(route('employee.index')); ?>" style="color: #757575">
            <div class="card p-20 flex-row justify-content-between">
                <div class="white">
                    <i class="icon-3x fa fa-users" aria-hidden="true" style="color: var(--color);"></i>
                </div>
                <div class="counter counter-md counter text-right">
                    <div class="counter-number-group">
                        <span class="counter-number"><?php echo e($totalEmpCount); ?></span>
                    </div>
                    <div class="counter-label text-capitalize font-size-16"><?php echo e(__('Total Employees')); ?></div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-lg-6 col-xl-3 mb-3">
        <a href="<?php echo e(route('customer_list', ['type' => 'retailer'])); ?>" style="color: #757575">
            <div class="card p-20 flex-row justify-content-between">
                <div class="white">
                    <i class="icon-3x fas fa-address-card" aria-hidden="true" style="color: var(--color);"></i>
                </div>
                <div class="counter counter-md counter text-right">
                    <div class="counter-number-group">
                        <span class="counter-number"><?php echo e($totalRetailerCount); ?></span>
                    </div>
                    <div class="counter-label text-capitalize font-size-16"><?php echo e(__('Total Retailers')); ?></div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-lg-6 col-xl-3 mb-3">
        <a href="<?php echo e(route('product.index')); ?>" style="color: #757575">
            <div class="card p-20 flex-row justify-content-between">
                <div class="white">
                    <i class="icon-3x fas fa-bag-shopping" aria-hidden="true" style="color: var(--color);"></i>
                </div>
                <div class="counter counter-md counter text-right">
                    <div class="counter-number-group">
                        <span class="counter-number"><?php echo e($totalProductCount); ?></span>
                    </div>
                    <div class="counter-label text-capitalize font-size-16"><?php echo e(__('Total Products')); ?></div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-lg-6 col-xl-3 mb-3">
        <a href="<?php echo e(route('customer_list', ['type' => 'distributor'])); ?>" style="color: #757575">
            <div class="card p-20 flex-row justify-content-between">
                <div class="white">
                    <i class="icon-3x fa fa-user" aria-hidden="true" style="color: var(--color);"></i>
                </div>
                <div class="counter counter-md counter text-right">
                    <div class="counter-number-group">
                        <span class="counter-number"><?php echo e($totalDistCount); ?></span>
                    </div>
                    <div class="counter-label text-capitalize font-size-16"><?php echo e(__('Total Distributors')); ?></div>
                </div>
            </div>
        </a>
    </div>
</div>
<div class="row mb--10">
    <?php if(isset($configDetails['customerTypes']['ss']) && $configDetails['customerTypes']['ss']['status'] == 1): ?>
        <div class="col-lg-6 col-xl-3 mb-3">
            <a href="<?php echo e(route('ss_orders')); ?>" style="color: #757575">
                <div class="card p-20 flex-row justify-content-between">
                    <div class="white">
                        <i class="icon-3x fa fa-dolly" aria-hidden="true" style="color: var(--color);"></i>
                    </div>
                    <div class="counter counter-md counter text-right">
                        <div class="counter-number-group">
                            <span class="counter-number"><?php echo e($ss_order); ?></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16"><?php echo e(__('ss_orders')); ?></div>
                    </div>
                </div>
            </a>
        </div>
    <?php endif; ?>
    <?php if(isset($configDetails['customerTypes']['distributor']) &&
            $configDetails['customerTypes']['distributor']['status'] == 1): ?>
        <div class="col-lg-6 col-xl-3 mb-3">
            <a href="<?php echo e(route('distributor_orders')); ?>" style="color: #757575">
                <div class="card p-20 flex-row justify-content-between">
                    <div class="white">
                        <i class="icon-3x fas fa-shipping-fast" aria-hidden="true" style="color: var(--color);"></i>
                    </div>
                    <div class="counter counter-md counter text-right">
                        <div class="counter-number-group">
                            <span class="counter-number"><?php echo e($distributor_order); ?></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16"><?php echo e(__('distributor_orders')); ?></div>
                    </div>
                </div>
            </a>
        </div>
    <?php endif; ?>

    <?php if(isset($configDetails['customerTypes']['dealer']) && $configDetails['customerTypes']['dealer']['status'] == 1): ?>
        <div class="col-lg-6 col-xl-3 mb-3">
            <a href="<?php echo e(route('dealer_orders')); ?>" style="color: #757575">
                <div class="card p-20 flex-row justify-content-between">
                    <div class="white">
                        <i class="icon-3x fas fa-handshake" aria-hidden="true" style="color: var(--color);"></i>
                    </div>
                    <div class="counter counter-md counter text-right">
                        <div class="counter-number-group">
                            <span class="counter-number"><?php echo e($dealer_order); ?></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16"><?php echo e(__('dealer_orders')); ?></div>
                    </div>
                </div>
            </a>
        </div>
    <?php endif; ?>
    <?php if(isset($configDetails['customerTypes']['retailer']) && $configDetails['customerTypes']['retailer']['status'] == 1): ?>
        <div class="col-lg-6 col-xl-3 mb-3">
            <a href="<?php echo e(route('retailer_orders')); ?>" style="color: #757575">
                <div class="card p-20 flex-row justify-content-between">
                    <div class="white">
                        <i class="icon-3x fa fa-clipboard-list" aria-hidden="true" style="color: var(--color);"></i>
                    </div>
                    <div class="counter counter-md counter text-right">
                        <div class="counter-number-group">
                            <span class="counter-number"><?php echo e($retailer_order); ?></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16"><?php echo e(__('retailer_orders')); ?></div>
                    </div>
                </div>
            </a>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\sfm_v2_laravel\resources\views/admin/dashboard_box_content.blade.php ENDPATH**/ ?>