{"__meta": {"id": "Xeb877868f92f8bf10f2320ecd6c5722e", "datetime": "2025-06-03 13:16:56", "utime": **********.731696, "method": "GET", "uri": "/sfm_v2_laravel/public/dashboard", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.035006, "end": **********.731715, "duration": 0.6967089176177979, "duration_str": "697ms", "measures": [{"label": "Booting", "start": **********.035006, "relative_start": 0, "end": **********.543517, "relative_end": **********.543517, "duration": 0.5085110664367676, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.543529, "relative_start": 0.5085229873657227, "end": **********.731718, "relative_end": 3.0994415283203125e-06, "duration": 0.18818902969360352, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 37158584, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "layouts.stylesheets", "param_count": null, "params": [], "start": **********.706072, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/stylesheets.blade.phplayouts.stylesheets", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fstylesheets.blade.php&line=1", "ajax": false, "filename": "stylesheets.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.709581, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.sidebar", "param_count": null, "params": [], "start": **********.714227, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/sidebar.blade.phplayouts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "admin.dashboard", "param_count": null, "params": [], "start": **********.717447, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard.blade.phpadmin.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "admin.dashboard_box_content", "param_count": null, "params": [], "start": **********.718631, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard_box_content.blade.phpadmin.dashboard_box_content", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard_box_content.blade.php&line=1", "ajax": false, "filename": "dashboard_box_content.blade.php", "line": "?"}}, {"name": "layouts.scripts", "param_count": null, "params": [], "start": **********.723375, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/scripts.blade.phplayouts.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.724551, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dashboard", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=83\" onclick=\"\">app/Http/Controllers/DashboardController.php:83-143</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.040720000000000006, "accumulated_duration_str": "40.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.590586, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 1.007}, {"sql": "select count(*) as aggregate from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.599128, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:40", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=40", "ajax": false, "filename": "DashboardController.php", "line": "40"}, "connection": "gopi_new", "explain": null, "start_percent": 1.007, "width_percent": 4.789}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 5 and `cm_status` = 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.607071, "duration": 0.02125, "duration_str": "21.25ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=44", "ajax": false, "filename": "DashboardController.php", "line": "44"}, "connection": "gopi_new", "explain": null, "start_percent": 5.796, "width_percent": 52.186}, {"sql": "select count(*) as aggregate from `product_master` where `product_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6320171, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=45", "ajax": false, "filename": "DashboardController.php", "line": "45"}, "connection": "gopi_new", "explain": null, "start_percent": 57.981, "width_percent": 2.112}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 3 and `cm_status` = 1", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.636251, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:49", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=49", "ajax": false, "filename": "DashboardController.php", "line": "49"}, "connection": "gopi_new", "explain": null, "start_percent": 60.093, "width_percent": 2.063}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 2 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [2, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.640782, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 62.156, "width_percent": 1.081}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 3 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [3, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.644362, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 63.237, "width_percent": 0.86}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 4 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [4, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.647764, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 64.096, "width_percent": 0.835}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 5 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [5, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 54}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6519449, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 64.931, "width_percent": 1.326}, {"sql": "select `emp_code`, `emp_name`, `emp_mobile`, `emp_status`, `emp_pin`, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59') as `total_call_count`, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' and `product_order_type` = 1) as `total_product_call` from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": ["2025-06-03 00:00:00", "2025-06-03 23:59:59", "2025-06-03 00:00:00", "2025-06-03 23:59:59", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.658616, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 66.257, "width_percent": 3.315}, {"sql": "select `atd_id`, `emp_code`, `atd_type`, `atd_login_time`, `atd_logout_time`, `atd_remarks` from `attendance` where `attendance`.`emp_code` in ('EMP0001', 'EMP0006', 'EMP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', '<PERSON>MP0038', '<PERSON>MP0039', '<PERSON>MP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', '<PERSON>MP0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', '<PERSON>MP0062', '<PERSON>MP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.664236, "duration": 0.00958, "duration_str": "9.58ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 69.573, "width_percent": 23.527}, {"sql": "select `emp_code`, MA<PERSON>(created_at) as last_call_time, MI<PERSON>(created_at) as first_call_time, SUM(CASE WHEN call_status != 4 THEN grand_total ELSE 0 END) as total_grand_total from `call_master` where `call_master`.`emp_code` in ('<PERSON>MP0001', 'EMP0006', '<PERSON>MP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', '<PERSON>MP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', '<PERSON>MP0043', '<PERSON>MP0044', '<PERSON>MP0045', '<PERSON>MP0046', '<PERSON>MP0047', '<PERSON><PERSON>0048', '<PERSON><PERSON>0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', '<PERSON>MP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', 'EMP0062', 'EMP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' group by `emp_code`", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.67717, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 93.099, "width_percent": 3.07}, {"sql": "select * from `emp_state_relation` where `emp_state_relation`.`emp_code` in ('<PERSON>MP0001', '<PERSON>MP0006', '<PERSON>MP0007', 'EMP0008', 'EMP0009', 'EMP0010', 'EMP0012', 'EMP0013', '<PERSON>MP0014', 'EMP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', 'EMP0051', 'EMP0052', 'EMP0053', 'EMP0055', 'EMP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', '<PERSON>MP0061', 'EMP0062', '<PERSON>MP0064', '<PERSON>MP0065', 'NXC1234')", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6848202, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 96.169, "width_percent": 1.965}, {"sql": "select * from `state_master` where `state_master`.`state_id` in (7, 13, 14, 21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.689417, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 98.134, "width_percent": 0.958}, {"sql": "select `state_name`, `state_id` from `state_master`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 123}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.693572, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:124", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=124", "ajax": false, "filename": "DashboardController.php", "line": "124"}, "connection": "gopi_new", "explain": null, "start_percent": 99.091, "width_percent": 0.909}]}, "models": {"data": {"App\\Models\\EmployeeMaster": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmployeeMaster.php&line=1", "ajax": false, "filename": "EmployeeMaster.php", "line": "?"}}, "App\\Models\\EmpStateRelation": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmpStateRelation.php&line=1", "ajax": false, "filename": "EmpStateRelation.php", "line": "?"}}, "App\\Models\\StateMaster": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FStateMaster.php&line=1", "ajax": false, "filename": "StateMaster.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 97, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1035367643 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1035367643\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-933508754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-933508754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1023454634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023454634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; sfm_session=eyJpdiI6IjdPMzhVYnp5VTQ0R2FqUWpJMlFxR1E9PSIsInZhbHVlIjoiZytzb0ZnaWtnY3pTS1I2Z0xpM0wyMHJYa09xTTBzbDNvbXVUT0lLeEFtcXJ2RXQrZE1NdnpnUVl6UHJkZlhJU2ZUc1RBcnBkV01odHhodzVqZHlGcGtuanNqRjdCRUx3QmNCRzRRRlJtMmE0Smg0MUpjNUFEbXV4MUVXRnQ2TWgiLCJtYWMiOiIyNThjZmMxNmVkMjRmODg4YThiMWNlMWY5NmQyYzY5OWI4N2Y2MWJmNjY1NzlhNmUxMzM2NTNlMWEyYWNkZjgyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJmalFIazQ0cUFUUXZSbmJ3YUVOVXc9PSIsInZhbHVlIjoiNFYrU2ZKUmJUclg2MWZQLzFocTBrc0ZkRUxuRXd4L1BrZGtRVTVQUjd4TmkrZHM4dmxsV3FyejJXZ0NGTng2R1ZnSkRXdm05QS91alRVeUdNZmtyQXNFYnpmdzAwT0ozRU40cVpvUHVYY2lKUzFOTWYxdERlQWY4eW1JWlFUc1IiLCJtYWMiOiI5NTJlYmEzNzgyNTc5NmY5NzI3ZjhmNjkxNzdmZjc5N2UzMDFiYjhiY2NhOTc5MzA5OWY1NmJkOGU1NGViMzhiIiwidGFnIjoiIn0%3D; qserve_session=eyJpdiI6Ii9Lb1pud1FCb0NxNmE1R0JiNDI3VlE9PSIsInZhbHVlIjoiNCszQ0dEdXpLREJaanNoSXNVTzdqcW5KWDRsdVJPYUhFQXhLTEtkN0ZRSHJLQ0JKWEtkbVgzaTI1VXluRU4zQTFZSkxHakVHcllwK2Z0TENIRzZxSWJZMWlqUkFYNkxrWE4xUTFNUGJWQ3JLbzdUUS9qMzNXQ2NPYnRxNFNpNHYiLCJtYWMiOiI0MTA0MGM0NjA2OTM5MmYyOGNjMmE2NzZkNzY0Njc2NzRhZWM1YjJlZjY2YmQ1N2Y1ZmEzNDQwNzIyODZjOTY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1832843892 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:46:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6IndDVkFTSjBndHZLenlUc1paY0FJV0E9PSIsInZhbHVlIjoiRWJKRkhmUG5oTEQrcThmZS9GLy9sbGM0YmsvNkFSSWJJaWRHWllBUytoRnp4amlBR2lzWTFHb1JaMzA1MzBoNnBOUmExQURyUk50bW90QWc4SXdpZmtJSlFweGNEL2JpdzlBc1JXTWJMMUY1TVlhanZRR2dYSzB0eERXemVCMlIiLCJtYWMiOiI1N2MxMjhlOWQ5MWZlZmJjZjk3ZWZjZGQyMjVkYzQ4NjY0NjhhZWFmNzA4YTVmNGMyZDZlMmE3NTIyMWMyMDFhIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:46:56 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IlR0VE1saVpiN1I4azd4R1RlbVRLMGc9PSIsInZhbHVlIjoiU2E4S2JoUndwdkhKalNjRGQ5ZWR1WVUwbWRheC9RMlIxT1FUSkV0Z2Q0ZU1WNE9kaldvdUJYWnhOb0N0NFJoMnluSVZjM0NqOFFETW9JaTg4djl6OGFuNEdBc0RNSEt3TElLQ2tXQjhLSTd6UHIzSEdPQi9kOG1EUkxGcjhsTHAiLCJtYWMiOiI3YjU2OGY0ZTFjZjA1NWNhY2RjY2Y0YTVmMjRmZjZiNDg3YWM0MTA2NzJjOTFlNTIzMmI2MDBiZDM4YWJkNmYyIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:46:56 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IndDVkFTSjBndHZLenlUc1paY0FJV0E9PSIsInZhbHVlIjoiRWJKRkhmUG5oTEQrcThmZS9GLy9sbGM0YmsvNkFSSWJJaWRHWllBUytoRnp4amlBR2lzWTFHb1JaMzA1MzBoNnBOUmExQURyUk50bW90QWc4SXdpZmtJSlFweGNEL2JpdzlBc1JXTWJMMUY1TVlhanZRR2dYSzB0eERXemVCMlIiLCJtYWMiOiI1N2MxMjhlOWQ5MWZlZmJjZjk3ZWZjZGQyMjVkYzQ4NjY0NjhhZWFmNzA4YTVmNGMyZDZlMmE3NTIyMWMyMDFhIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:46:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IlR0VE1saVpiN1I4azd4R1RlbVRLMGc9PSIsInZhbHVlIjoiU2E4S2JoUndwdkhKalNjRGQ5ZWR1WVUwbWRheC9RMlIxT1FUSkV0Z2Q0ZU1WNE9kaldvdUJYWnhOb0N0NFJoMnluSVZjM0NqOFFETW9JaTg4djl6OGFuNEdBc0RNSEt3TElLQ2tXQjhLSTd6UHIzSEdPQi9kOG1EUkxGcjhsTHAiLCJtYWMiOiI3YjU2OGY0ZTFjZjA1NWNhY2RjY2Y0YTVmMjRmZjZiNDg3YWM0MTA2NzJjOTFlNTIzMmI2MDBiZDM4YWJkNmYyIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:46:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832843892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-150704667 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150704667\", {\"maxDepth\":0})</script>\n"}}