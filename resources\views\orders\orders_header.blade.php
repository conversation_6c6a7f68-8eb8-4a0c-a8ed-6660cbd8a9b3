@php
    // getConfigDetails helper function from app/Helper/customHelper.php
    $configDetails = getConfigDetails();
@endphp
<ul class="nav nav-tabs nav-tabs-line" role="tablist" id="navSettingsTab">
    <li class="nav-item" role="presentation">
        <a href="{{ route('ss_orders') }}"
            class="nav-link {{ Request::is('orders/ss_orders') || Request::is('orders/distributor_orders') || Request::is('orders/dealer_orders') || Request::is('orders/retailer_orders') ? 'active' : '' }}"
            role="tab" aria-selected="false">{{ __('orders') }}</a>
    </li>
    {{-- <li class="nav-item" role="presentation">
        <a href="{{ route('dispatch_order_list') }}"
            class="nav-link {{ Request::is('orders/dispatch_order_list') ? 'active' : '' }}" role="tab"
            aria-selected="false">{{__('Dispatch')}}</a>
    </li> --}}
</ul>


@section('orders_sub_header')
    <ul class="nav nav-tabs nav-tabs-line" role="tablist" id="navLocationTab">
        @if (isset($configDetails['customerTypes']['retailer']) && $configDetails['customerTypes']['retailer']['status'] == 1)
            <li class="nav-item" role="presentation">
                <a href="{{ route('retailer_orders') }}"
                    class="nav-link {{ Request::is('orders/retailer_orders') ? 'active' : '' }}" role="tab"
                    aria-selected="false">{{ __('retailer_orders') }}</a>
            </li>
        @endif
        @if (isset($configDetails['customerTypes']['dealer']) && $configDetails['customerTypes']['dealer']['status'] == 1)
            <li class="nav-item" role="presentation">
                <a href="{{ route('dealer_orders') }}"
                    class="nav-link {{ Request::is('orders/dealer_orders') ? 'active' : '' }}" role="tab"
                    aria-selected="false">{{ __('dealer_orders') }}</a>
            </li>
        @endif
        @if (isset($configDetails['customerTypes']['distributor']) &&
                $configDetails['customerTypes']['distributor']['status'] == 1)
            <li class="nav-item" role="presentation">
                <a href="{{ route('distributor_orders') }}"
                    class="nav-link {{ Request::is('orders/distributor_orders') ? 'active' : '' }}" role="tab"
                    aria-selected="false">{{ __('distributor_orders') }}</a>
            </li>
        @endif
        @if (isset($configDetails['customerTypes']['ss']) && $configDetails['customerTypes']['ss']['status'] == 1)
            <li class="nav-item" role="presentation">
                <a href="{{ route('ss_orders') }}" class="nav-link {{ Request::is('orders/ss_orders') ? 'active' : '' }}"
                    role="tab" aria-selected="false">{{ __('ss_orders') }}</a>
            </li>
        @endif
    </ul>
@endsection
