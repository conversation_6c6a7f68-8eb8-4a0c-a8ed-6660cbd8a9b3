{"__meta": {"id": "X9d8e51f0063fb4f10fd5e897208d3404", "datetime": "2025-06-03 13:17:25", "utime": **********.768641, "method": "GET", "uri": "/sfm_v2_laravel/public/dashboard", "ip": "*************"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.16794, "end": **********.768657, "duration": 0.6007170677185059, "duration_str": "601ms", "measures": [{"label": "Booting", "start": **********.16794, "relative_start": 0, "end": **********.541242, "relative_end": **********.541242, "duration": 0.37330198287963867, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.541255, "relative_start": 0.37331509590148926, "end": **********.768659, "relative_end": 2.1457672119140625e-06, "duration": 0.22740411758422852, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 37183112, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "layouts.stylesheets", "param_count": null, "params": [], "start": **********.740692, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/stylesheets.blade.phplayouts.stylesheets", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fstylesheets.blade.php&line=1", "ajax": false, "filename": "stylesheets.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.744548, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.sidebar", "param_count": null, "params": [], "start": **********.751638, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/sidebar.blade.phplayouts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "admin.dashboard", "param_count": null, "params": [], "start": **********.753948, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard.blade.phpadmin.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "admin.dashboard_box_content", "param_count": null, "params": [], "start": **********.754956, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/admin/dashboard_box_content.blade.phpadmin.dashboard_box_content", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Fadmin%2Fdashboard_box_content.blade.php&line=1", "ajax": false, "filename": "dashboard_box_content.blade.php", "line": "?"}}, {"name": "layouts.scripts", "param_count": null, "params": [], "start": **********.760474, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/scripts.blade.phplayouts.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.761688, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dashboard", "middleware": "web, debugbar, auth", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=83\" onclick=\"\">app/Http/Controllers/DashboardController.php:83-143</a>"}, "queries": {"nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04932, "accumulated_duration_str": "49.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.604768, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gopi_new", "explain": null, "start_percent": 0, "width_percent": 0.912}, {"sql": "select count(*) as aggregate from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.615045, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:40", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=40", "ajax": false, "filename": "DashboardController.php", "line": "40"}, "connection": "gopi_new", "explain": null, "start_percent": 0.912, "width_percent": 2.068}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 5 and `cm_status` = 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6208632, "duration": 0.02608, "duration_str": "26.08ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=44", "ajax": false, "filename": "DashboardController.php", "line": "44"}, "connection": "gopi_new", "explain": null, "start_percent": 2.981, "width_percent": 52.879}, {"sql": "select count(*) as aggregate from `product_master` where `product_status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6524022, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=45", "ajax": false, "filename": "DashboardController.php", "line": "45"}, "connection": "gopi_new", "explain": null, "start_percent": 55.86, "width_percent": 0.994}, {"sql": "select count(*) as aggregate from `customer_master` where `cm_type` = 3 and `cm_status` = 1", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.656062, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:49", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=49", "ajax": false, "filename": "DashboardController.php", "line": "49"}, "connection": "gopi_new", "explain": null, "start_percent": 56.853, "width_percent": 1.784}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 2 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [2, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6608, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 58.637, "width_percent": 1.034}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 3 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [3, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.666959, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 59.672, "width_percent": 1.095}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 4 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [4, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.671125, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 60.766, "width_percent": 1.135}, {"sql": "select count(*) as aggregate from `call_master` inner join `customer_master` on `call_master`.`client_code` = `customer_master`.`cm_code` where `customer_master`.`cm_type` = 5 and `call_master`.`created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": [5, "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 54}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 85}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6749032, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:79", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=79", "ajax": false, "filename": "DashboardController.php", "line": "79"}, "connection": "gopi_new", "explain": null, "start_percent": 61.902, "width_percent": 1.014}, {"sql": "select `emp_code`, `emp_name`, `emp_mobile`, `emp_status`, `emp_pin`, `designation_id`, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59') as `total_call_count`, (select count(*) from `call_master` where `employee_master`.`emp_code` = `call_master`.`emp_code` and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' and `product_order_type` = 1) as `total_product_call` from `employee_master` where `emp_status` = 1", "type": "query", "params": [], "bindings": ["2025-06-03 00:00:00", "2025-06-03 23:59:59", "2025-06-03 00:00:00", "2025-06-03 23:59:59", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.685324, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 62.916, "width_percent": 3.305}, {"sql": "select `atd_id`, `emp_code`, `atd_type`, `atd_login_time`, `atd_logout_time`, `atd_remarks` from `attendance` where `attendance`.`emp_code` in ('EMP0001', 'EMP0006', 'EMP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', '<PERSON>MP0038', '<PERSON>MP0039', '<PERSON>MP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', '<PERSON>MP0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', '<PERSON>MP0062', '<PERSON>MP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59'", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.691415, "duration": 0.0128, "duration_str": "12.8ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 66.221, "width_percent": 25.953}, {"sql": "select `emp_code`, MA<PERSON>(created_at) as last_call_time, MI<PERSON>(created_at) as first_call_time, SUM(CASE WHEN call_status != 4 THEN grand_total ELSE 0 END) as total_grand_total from `call_master` where `call_master`.`emp_code` in ('<PERSON>MP0001', 'EMP0006', '<PERSON>MP0007', '<PERSON>MP0008', '<PERSON>MP0009', '<PERSON>MP0010', '<PERSON>MP0012', 'EMP0013', 'EMP0014', '<PERSON>MP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', '<PERSON>MP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', '<PERSON>MP0043', '<PERSON>MP0044', '<PERSON>MP0045', '<PERSON>MP0046', '<PERSON>MP0047', '<PERSON><PERSON>0048', '<PERSON><PERSON>0051', '<PERSON>MP0052', '<PERSON>MP0053', '<PERSON>MP0055', '<PERSON>MP0057', '<PERSON>MP0058', '<PERSON>MP0059', '<PERSON>MP0060', 'EMP0061', 'EMP0062', 'EMP0064', 'EMP0065', 'NXC1234') and `created_at` between '2025-06-03 00:00:00' and '2025-06-03 23:59:59' group by `emp_code`", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234", "2025-06-03 00:00:00", "2025-06-03 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.707617, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 92.174, "width_percent": 2.494}, {"sql": "select * from `designation_master` where `designation_master`.`dm_id` in (5, 8, 9, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.713088, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 94.667, "width_percent": 1.014}, {"sql": "select * from `emp_state_relation` where `emp_state_relation`.`emp_code` in ('<PERSON>MP0001', '<PERSON>MP0006', '<PERSON>MP0007', 'EMP0008', 'EMP0009', 'EMP0010', 'EMP0012', 'EMP0013', '<PERSON>MP0014', 'EMP0015', 'EMP0017', 'EMP0018', 'EMP0020', 'EMP0021', 'EMP0023', 'EMP0024', 'EMP0026', 'EMP0031', 'EMP0032', 'EMP0033', 'EMP0034', 'EMP0036', 'EMP0037', 'EMP0038', 'EMP0039', 'EMP0040', 'EMP0042', 'EMP0043', 'EMP0044', 'EMP0045', 'EMP0046', 'EMP0047', 'EMP0048', 'EMP0051', 'EMP0052', 'EMP0053', 'EMP0055', 'EMP0057', 'EMP0058', '<PERSON>MP0059', '<PERSON>MP0060', '<PERSON>MP0061', 'EMP0062', '<PERSON>MP0064', '<PERSON>MP0065', 'NXC1234')", "type": "query", "params": [], "bindings": ["EMP0001", "EMP0006", "EMP0007", "EMP0008", "EMP0009", "EMP0010", "EMP0012", "EMP0013", "EMP0014", "EMP0015", "EMP0017", "EMP0018", "EMP0020", "EMP0021", "EMP0023", "EMP0024", "EMP0026", "EMP0031", "EMP0032", "EMP0033", "EMP0034", "EMP0036", "EMP0037", "EMP0038", "EMP0039", "EMP0040", "EMP0042", "EMP0043", "EMP0044", "EMP0045", "EMP0046", "EMP0047", "EMP0048", "EMP0051", "EMP0052", "EMP0053", "EMP0055", "EMP0057", "EMP0058", "EMP0059", "EMP0060", "EMP0061", "EMP0062", "EMP0064", "EMP0065", "NXC1234"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.718707, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 95.681, "width_percent": 2.129}, {"sql": "select * from `state_master` where `state_master`.`state_id` in (7, 13, 14, 21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.723833, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:110", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=110", "ajax": false, "filename": "DashboardController.php", "line": "110"}, "connection": "gopi_new", "explain": null, "start_percent": 97.81, "width_percent": 0.912}, {"sql": "select `state_name`, `state_id` from `state_master`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 123}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7288551, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:124", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\sfm_v2_laravel\\app\\Http\\Controllers\\DashboardController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=124", "ajax": false, "filename": "DashboardController.php", "line": "124"}, "connection": "gopi_new", "explain": null, "start_percent": 98.723, "width_percent": 1.277}]}, "models": {"data": {"App\\Models\\EmployeeMaster": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmployeeMaster.php&line=1", "ajax": false, "filename": "EmployeeMaster.php", "line": "?"}}, "App\\Models\\EmpStateRelation": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FEmpStateRelation.php&line=1", "ajax": false, "filename": "EmpStateRelation.php", "line": "?"}}, "App\\Models\\DesignationMaster": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FDesignationMaster.php&line=1", "ajax": false, "filename": "DesignationMaster.php", "line": "?"}}, "App\\Models\\StateMaster": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FStateMaster.php&line=1", "ajax": false, "filename": "StateMaster.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fdevan%2FOneDrive%2FVishal%2Fxampp%2Fhtdocs%2Fsfm_v2_laravel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 101, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://*************/sfm_v2_laravel/public/dashboard\"\n]", "login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d": "GOPIDIST0001", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-2019767899 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2019767899\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2028326048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2028326048\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-27661152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-27661152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7,gu;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1092 characters\">sidenav-state=unpinned; qserve_session=eyJpdiI6Ii9Lb1pud1FCb0NxNmE1R0JiNDI3VlE9PSIsInZhbHVlIjoiNCszQ0dEdXpLREJaanNoSXNVTzdqcW5KWDRsdVJPYUhFQXhLTEtkN0ZRSHJLQ0JKWEtkbVgzaTI1VXluRU4zQTFZSkxHakVHcllwK2Z0TENIRzZxSWJZMWlqUkFYNkxrWE4xUTFNUGJWQ3JLbzdUUS9qMzNXQ2NPYnRxNFNpNHYiLCJtYWMiOiI0MTA0MGM0NjA2OTM5MmYyOGNjMmE2NzZkNzY0Njc2NzRhZWM1YjJlZjY2YmQ1N2Y1ZmEzNDQwNzIyODZjOTY0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndKb3cyc2UraFRTcllJaGpRQURwdVE9PSIsInZhbHVlIjoiNlMyc0J0YkoxMFAvMkdVY2FFY3pRSkhWc1lnYytDRXVMOW9hc1NGK2Z3eHVTMExvYXovdC80cng1UWtjcC9lclM4UmFVekQzZkVDYmpKQm40L3VtWWtwTHh6amR3cWVNTE1ZMUxzcTR6TjdrcXZ6WkcydVFjVXRiMktpRmVmaHYiLCJtYWMiOiJmOTdjZjVjYjFlMTU3MTI5M2ZjODMyMjAzMzcxNTc3Mjk1NTFiNTFkMTdjYWVkMTU4ZDEzNzlkNjQ1MmNlMjYwIiwidGFnIjoiIn0%3D; sfm_session=eyJpdiI6IjFNWGd2MFR2eUpFQmZTUGRRcWhsV1E9PSIsInZhbHVlIjoiaWpJRG5FTWt5YTF0Tm03a2k1VVZhZlp2U2dHa2hCLzZIMGlRTm12TUlTVDE2bk5TOTBncWNQR2hybnNuN0dweGtyL016cGNnRmg2eWI4MGhrcnJlWWJHdWVtbzFmekRiZFZranhKYWZEZWt3aWhxK1N3eGlyOGJneWZFMHlIeG8iLCJtYWMiOiIyNTdhMzgxNzlmODEzYzFjZmRhYTBjYjc2NmFiZmJjZWU1OGYzNDc4MmQ5MTI3OTM1MDE1Y2U0ZWRhYTkzYmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-325335058 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidenav-state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>qserve_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>sfm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sHcFDPVFfOuS5lvuvthsX5qDJ4WccAW3tt4CALhT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325335058\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 07:47:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"432 characters\">XSRF-TOKEN=eyJpdiI6IjlsU25kalRoY1BVRVE5ckpOV2k0Snc9PSIsInZhbHVlIjoiUFNPSysvT0szUjN4NTI4WmQzQjFWYzY3dHA3Q0huVVhSbER2YWxqbTlJZHlqZVIveDVLQjBudVljWW14bTI0WGRqTDhxSW9jenJsZ0JVanFHUDY2TWhsS2Y0NWJFeW9pUlNyU1VGYTVmYkdHTFBDRS9ZKzVVTFNhSis2ZEo3V0EiLCJtYWMiOiJjNWE0MzQwYWE3MmZhMmFkMWEyMWY2ZTg1NDY0MjlhMTBlYzdjMzk0OWQzOGM3NjJmYWMxYTUxNjE2OWIzNDg2IiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:47:25 GMT; Max-Age=72000000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sfm_session=eyJpdiI6IjlCYUZVOVhuVXY4VXIrWTZSc1l2Unc9PSIsInZhbHVlIjoiNzVJelhRSmhFSTRsUER0OG9SMDhhcnFOZDlWV21VL1VJZncramZHaVJwSFRiaWtCMXIyTURKaTNVaTJkbmRCMThjbHRpR09tbFFWRldrRlZDd2UrUWthUDYzVXBvMERzbGkwL2h3cDZ4RHpqUGlzcjRUQnRWZ29CRzIxOVdlenIiLCJtYWMiOiI5ZmU1NzUyODg4Y2NmZmYzYmFjZThmOGRjNzE5N2Q1MTFmYjhmZTY5YWRjZGMzMDhkYjI3NmY3NDY1M2M0YTBkIiwidGFnIjoiIn0%3D; expires=Tue, 14 Sep 2027 15:47:25 GMT; Max-Age=72000000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlsU25kalRoY1BVRVE5ckpOV2k0Snc9PSIsInZhbHVlIjoiUFNPSysvT0szUjN4NTI4WmQzQjFWYzY3dHA3Q0huVVhSbER2YWxqbTlJZHlqZVIveDVLQjBudVljWW14bTI0WGRqTDhxSW9jenJsZ0JVanFHUDY2TWhsS2Y0NWJFeW9pUlNyU1VGYTVmYkdHTFBDRS9ZKzVVTFNhSis2ZEo3V0EiLCJtYWMiOiJjNWE0MzQwYWE3MmZhMmFkMWEyMWY2ZTg1NDY0MjlhMTBlYzdjMzk0OWQzOGM3NjJmYWMxYTUxNjE2OWIzNDg2IiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:47:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">sfm_session=eyJpdiI6IjlCYUZVOVhuVXY4VXIrWTZSc1l2Unc9PSIsInZhbHVlIjoiNzVJelhRSmhFSTRsUER0OG9SMDhhcnFOZDlWV21VL1VJZncramZHaVJwSFRiaWtCMXIyTURKaTNVaTJkbmRCMThjbHRpR09tbFFWRldrRlZDd2UrUWthUDYzVXBvMERzbGkwL2h3cDZ4RHpqUGlzcjRUQnRWZ29CRzIxOVdlenIiLCJtYWMiOiI5ZmU1NzUyODg4Y2NmZmYzYmFjZThmOGRjNzE5N2Q1MTFmYjhmZTY5YWRjZGMzMDhkYjI3NmY3NDY1M2M0YTBkIiwidGFnIjoiIn0%3D; expires=Tue, 14-Sep-2027 15:47:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">232RMoubpfCZpeuXtLqnxLY7uyTGgPvuy3jllL8K</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://*************/sfm_v2_laravel/public/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_distributor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GOPIDIST0001</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}